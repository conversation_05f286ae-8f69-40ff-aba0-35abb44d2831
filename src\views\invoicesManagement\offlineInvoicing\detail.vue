<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <!-- 头部信息 -->
    <div class="device-head">
      <img
        src="@/assets/stationDivision/settlement-icon.png"
        class="device-head-icon"
      />

      <div class="device-info-wrap">
        <div class="device-title-wrap">
          <div class="device-title">申请编号：{{ applicationNo }}</div>
          <div class="device-status" v-if="status">{{ status }}</div>
        </div>
        <div class="device-info-wrap">
          <el-row>
            <el-col :span="8">
              <span class="label">申请人：</span>
              <span class="value">{{ applicant }}</span>
            </el-col>
            <el-col :span="8">
              <span class="label">申请时间：</span>
              <span class="value">{{ applyTime }}</span>
            </el-col>
          </el-row>
        </div>
      </div>

      <div style="text-align: right; margin-top: 16px">
        <el-button type="primary" @click="downloadApplication">
          下载申请单
        </el-button>
        <el-button type="primary" @click="auditTrace">审核轨迹</el-button>
      </div>
    </div>

    <!-- 申请信息 -->
    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">申请信息</div>
      </div>

      <div class="form-wrap">
        <el-row :gutter="20" style="margin-bottom: 24px">
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">交易单位：</div>
              <div class="info-detail">{{ transactionUnit }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">开票电费金额：</div>
              <div class="info-detail">{{ electricityFee }} 元</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">开票服务费金额：</div>
              <div class="info-detail">{{ serviceFee }} 元</div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-bottom: 24px">
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">开票总金额：</div>
              <div class="info-detail">{{ totalAmount }} 元</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">开票类型：</div>
              <div class="info-detail">{{ invoiceType }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">发票种类：</div>
              <div class="info-detail">{{ invoiceKind }}</div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-bottom: 24px">
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">开票企业信息：</div>
              <div class="info-detail">{{ companyInfo }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">企业名称：</div>
              <div class="info-detail">{{ companyName }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">纳税人识别号：</div>
              <div class="info-detail">{{ taxId }}</div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-bottom: 24px">
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">开户行与账号：</div>
              <div class="info-detail">{{ bankAccount }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">注册地址：</div>
              <div class="info-detail">{{ registerAddress }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">注册电话：</div>
              <div class="info-detail">{{ registerPhone }}</div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-bottom: 24px">
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">附注：</div>
              <div class="info-detail">{{ note }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">备注：</div>
              <div class="info-detail">{{ remark }}</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 发票信息 -->
    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">发票信息</div>
      </div>

      <div class="form-wrap">
        <el-row :gutter="20" style="margin-bottom: 24px">
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">开票日期：</div>
              <div class="info-detail">{{ invoiceDate }}</div>
            </div>
          </el-col>
          <el-col :span="16">
            <div style="display: flex">
              <div class="info-title">发票：</div>
              <div class="info-detail">
                <a :href="invoiceUrl" target="_blank">{{ invoiceName }}</a>
                <el-button type="text" @click="downloadInvoice">下载</el-button>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-bottom: 24px">
          <el-col :span="24">
            <div style="display: flex">
              <div class="info-title">备注：</div>
              <div class="info-detail">{{ invoiceRemark }}</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-wrap">
      <el-button @click="goBack">返回</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InvoicingDetail',
  data() {
    return {
      // 头部信息
      applicationNo: '**********',
      status: '审核通过',
      applicant: '张三',
      applyTime: '2024-12-23 17:23:45',

      // 申请信息
      transactionUnit: '省电动',
      electricityFee: '100',
      serviceFee: '10',
      totalAmount: '100',
      invoiceType: '蓝票',
      invoiceKind: '数电票(增值税专用发票)',
      companyInfo: '公司1',
      companyName: '公司1',
      taxId: '*************',
      bankAccount: '中信银行 811050101822828864',
      registerAddress: '湖南省长沙市雨花区',
      registerPhone: '0731-6789078',
      note: '内容信息内容信息',
      remark: '内容信息内容信息',

      // 发票信息
      invoiceDate: '2024-12-23',
      invoiceName: '发票名称名称xxxxxxxx名称.pdf',
      invoiceUrl: '#',
      invoiceRemark: '备注信息备注信息信息信息信息信息信息信息',
    };
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    downloadApplication() {
      console.log('下载申请单');
    },
    auditTrace() {
      console.log('查看审核轨迹');
    },
    downloadInvoice() {
      console.log('下载发票');
    },
  },
};
</script>

<style lang="scss" scoped>
.container-float {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.device-head {
  background-color: #fff;
  display: flex;
  height: 112px;
  align-items: center;
  padding: 0 24px;
  box-sizing: border-box;

  .device-head-icon {
    width: 48px;
    height: 48px;
    margin-right: 24px;
  }

  .device-info-wrap {
    flex: 1;

    .device-title-wrap {
      height: 32px;
      display: flex;
      align-items: center;

      .device-title {
        font-weight: 500;
        font-size: 24px;
        color: #12151a;
      }

      .device-status {
        padding: 0 10px;
        height: 24px;
        border-radius: 10px 0 10px 0;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: center;
        color: #fff;
        background: linear-gradient(321.01deg, #00c864 8.79%, #38f3ca 100.27%);
        margin-left: 12px;
      }
    }

    .device-info-wrap {
      height: 16px;
      margin-top: 16px;
      font-size: 16px;
      font-weight: 400;
      color: #292b33;

      .label {
        color: #505363;
        margin-right: 8px;
      }

      .value {
        color: #292b33;
      }
    }
  }
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    background: linear-gradient(180deg, #e9f2ff 0%, #ffffff 100%);

    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }

    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .form-wrap {
    padding: 16px;

    .info-title {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #505363;
      margin-right: 8px;
    }

    .info-detail {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #292b33;
    }
  }
}

.bottom-wrap {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 86px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-color: #ffffff;
  padding-right: 32px;
  box-sizing: border-box;
  z-index: 100;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);

  .el-button {
    margin: 0 10px;
  }
}

@media print {
  .bottom-wrap {
    display: none;
  }
}
</style>
