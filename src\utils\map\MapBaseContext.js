
import { Map_Key, Security_Jscode, MapDisplayLevel } from './constants';
import AMapLoader from './AMapLoader';
import Vue from 'vue';

export default class MapBaseContext {

    _map = null;
    _mapOptions = {
        id: '',
        plugins: ['AMap.DistrictSearch', 'AMap.Object3DLayer', 'AMap.Object3D', 'AMap.GeoJSON'], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
        options: {
            showLabel: true,
            labelzIndex: 130,
            zoom: 7.2,
            mapStyle: "amap://styles/light",
        }
    };
    _Amap = null;

    _districtSearch = null;

    markerPool = [];
    currentMarkerList = [];

    constructor(mapOptions) {
        if (typeof mapOptions !== 'object') {
            mapOptions = {};
        }
        // 合并一下options
        this._mapOptions = {
            ...this._mapOptions,
            ...mapOptions,
            options: {
                ...(this._mapOptions.options || {}),
                ...mapOptions.options,
            },
        };
    }

    loadMap(plugins = []) {
        window._AMapSecurityConfig = {
            securityJsCode: Security_Jscode,
        };
        return AMapLoader.load(plugins)
    }

    async initMap(getLayers) {
        let { id, plugins = [], options = {} } = this._mapOptions;
        console.log(id, plugins, options);
        const AMap = await this.loadMap(plugins);
        // 是否需要在初始化的时候添加图层
        if (typeof getLayers === 'function') {
            Object.assign(options, getLayers(AMap));
        }
        const map = new AMap.Map(id, {
            ...options,
        });
        this._Amap = AMap;
        this._map = map;
    }

    initSearch() {
        this._districtSearch = new AMap.DistrictSearch({
            extensions: 'all', // 返回行政区边界坐标组等具体信息
            level: MapDisplayLevel.District, // 限定查询行政区级别为市
            subdistrict: 1,
        });
    }

    removeAllMarkers() {
        this._map.clearMap();
        this.markerPool = [...this.markerPool, ...this.currentMarkerList];
        this.currentMarkerList = [];
    }

    generateMarker() {
        let marker = this.markerPool.pop();
        if (!marker) {
            const { _Amap } = this;
            marker = new _Amap.Marker({
                anchor: 'bottom-center',
                offset: [0, -12],
            });
        }
        return marker;
    }

    // 使用Vue component创建一个marker
    addVueMarker(lnglat, component, props = {}) {
        const marker = this.generateMarker();
        const MapMarkClass = Vue.extend(component);
        marker.setContent(new MapMarkClass({
            propsData: props,
            on: {
                'stationClick': () => {
                    // 点击marker，触发marker的点击事件
                    marker.emit('stationClick', props);
                },
            }
        }).$mount().$el);
        marker.setPosition(lnglat);
        this.currentMarkerList.push(marker);
        // marker.extData = { ...site, id: index, lnglat };
        // 将 markers 添加到地图
        this._map.add(marker);
    }

    // 绘制省、市边界线
    drawBoundaryLine(json, styles = {
        strokeColor: "#FFFFFF",
        strokeWeight: 4,
        fillColor: 'transparent',
        cursor: 'pointer',
    }, cb) {
        const { _Amap, _map } = this;
        const geoLayer = new _Amap.GeoJSON({
            geoJSON: json,
            // 自定义样式
            getPolygon: (geojson, lnglats) => {
                const polygon = new _Amap.Polygon({
                    path: lnglats,
                    ...styles
                });
                // 添加一个毁掉函数，用来给polygon添加事件
                cb?.(geojson, polygon);
                return polygon;
            }
        });
        _map.add(geoLayer);
        return geoLayer;
    }
}