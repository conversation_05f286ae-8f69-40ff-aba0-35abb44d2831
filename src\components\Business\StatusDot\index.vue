<template>
  <div class="status-tab">
    <span class="status-dot" :class="[color]"></span>
    {{ title }}
  </div>
</template>

<script>
export default {
  name: 'StatusDot',
  props: {
    value: {
      type: String,
      default: '',
    },
    dictValue: {
      type: Array,
      default: () => [],
    },
    colors: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      title: '',
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          console.log(
            'this.dictValue',
            this.selectDictLabel(this.dictValue, newVal),
            this.dictValue,
            newVal
          );
          this.title = this.selectDictLabel(this.dictValue, newVal);
        } else {
          this.title = '';
        }
      },
    },
  },
  computed: {
    color() {
      return this.colors[
        this.dictValue.findIndex((item) => item.value === this.value)
      ];
    },
  },
  mounted() {},
  methods: {
    tabChange(val) {
      this.$emit('input', val);
      this.$emit('tabChange', val);
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.status-tab {
  display: flex;
  align-items: center;
  .status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 8px;
    background-color: transparent;
  }
  .success {
    background-color: #00c864;
  }
  .warning {
    background-color: #ff8d24;
  }
  .danger {
    background-color: red;
  }
  .default {
    background-color: #217aff;
  }
  .complete {
    background-color: #1ab2ff;
  }
  .info {
    background-color: #1ab2ff;
  }
  .build {
    background-color: #1AB2FF;
  }
  .ty {
    background-color: #00C864;
  }
  .stop {
    background-color: #FC1E31;
  }
  .tempstop{
    background-color: #8126FF;
  }
  .forverstop {
    background-color: #818496;
  }
  .charge {
    background-color: #217AFF;
  }
}
</style>
