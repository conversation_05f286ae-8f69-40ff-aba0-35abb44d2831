import request from '@/utils/request';

// 省市区层级关系查询
export const areaList = (data) => {
  return request({
    url: '/vehicle-grid-admin/area/list',
    method: 'post',
    data,
  });
};
// 台区列表
export const getTgList = (data) => {
  return request({
    url: '/vehicle-grid-admin/area/getTg',
    method: 'post',
    data,
  });
};
// 运营商列表
export const operatorsList = (data) => {
  return request({
    url: '/vehicle-grid-admin/operators/list',
    method: 'post',
    data,
  });
};
// 站列表
export const stationList = (data) => {
  return request({
    url: '/vehicle-grid-admin/station/stationList',
    method: 'post',
    data,
  });
};
// 桩列表
export const pileList = (data) => {
  return request({
    url: '/vehicle-grid-admin/pile/list',
    method: 'post',
    data,
  });
};
// 省-市-区-台区 树形结构
export const areaTreeStation = (data) => {
  return request({
    url: '/vehicle-grid-admin/area/list',
    method: 'post',
    data,
  });
};
export const elecArcList = (data) => {
  return request({
    url: '/vehicle-grid-admin/elecArc/list',
    method: 'post',
    data,
  });
};
