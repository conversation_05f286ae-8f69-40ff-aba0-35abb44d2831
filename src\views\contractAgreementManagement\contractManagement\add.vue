<template>
  <div
    class="container container-float"
    style="padding: 0 0 100px 0; position: relative"
  >
    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">合同信息</div>
      </div>
      <div class="form-wrap">
        <el-form
          :model="contractInformationForm"
          :rules="contractInformationRules"
          ref="contractInformationForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="合同编号" prop="code">
                <el-input
                  v-model="contractInformationForm.code"
                  placeholder="请输入合同编号"
                  maxlength="30"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="合同名称" prop="name">
                <el-input
                  v-model="contractInformationForm.name"
                  placeholder="请输入合同名称"
                  maxlength="30"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="合同类型" prop="type">
                <el-cascader
                  v-model="contractInformationForm.type"
                  :options="contractTypeOptions"
                  @change="handleChange"
                  style="width: 100%"
                ></el-cascader>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <!-- <el-col :span="8">
              <el-form-item label="合同开始日期" prop="startDate">
                <el-date-picker
                  v-model="contractInformationForm.startDate"
                  type="date"
                  placeholder="请选择开始日期"
                  style="width: 100%"
                  value-format="yyyy-MM-dd"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="合同结束日期" prop="endDate">
                <el-date-picker
                  v-model="contractInformationForm.endDate"
                  type="date"
                  placeholder="请选择结束日期"
                  style="width: 100%"
                  value-format="yyyy-MM-dd"
                />
              </el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item label="合同开始结束日期" prop="contractDate">
                <el-date-picker
                  v-model="contractInformationForm.contractDate"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
              <el-form-item label="合同密级" prop="contractLevel">
                <el-select
                  v-model="contractInformationForm.contractLevel"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in contractLevelOptions"
                    :key="item.value"
                    :value="item.label"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">合同签约方信息</div>
      </div>
      <div class="form-wrap">
        <el-form
          :model="signatoryInformationForm"
          :rules="signatoryInformationRules"
          ref="signatoryInformationForm"
          label-position="top"
        >
          <!-- <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="合同甲方" prop="partyA">
                <el-input
                  v-model="signatoryInformationForm.partyA"
                  placeholder="请输入合同甲方"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="甲方负责人" prop="partyAResponsiblePerson">
                <el-input
                  v-model="signatoryInformationForm.partyAResponsiblePerson"
                  placeholder="请输入甲方负责人"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="甲方联系方式" prop="partyAContactInfo">
                <el-input
                  v-model="signatoryInformationForm.partyAContactInfo"
                  placeholder="请输入甲方联系方式"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="甲方公司地址" prop="partyACompanyAddress">
                <el-input
                  v-model="signatoryInformationForm.partyACompanyAddress"
                  placeholder="请输入甲方公司地址"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="合同乙方" prop="partyB">
                <el-input
                  v-model="signatoryInformationForm.partyB"
                  placeholder="请输入合同乙方"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="乙方负责人" prop="partyBResponsiblePerson">
                <el-input
                  v-model="signatoryInformationForm.partyBResponsiblePerson"
                  placeholder="请输入乙方负责人"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="乙方联系方式" prop="partyBContactInfo">
                <el-input
                  v-model="signatoryInformationForm.partyBContactInfo"
                  placeholder="请输入乙方联系方式"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="乙方公司地址" prop="partyBCompanyAddress">
                <el-input
                  v-model="signatoryInformationForm.partyBCompanyAddress"
                  placeholder="请输入乙方公司地址"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="乙方开户银行" prop="partyBOpeningBank">
                <el-input
                  v-model="signatoryInformationForm.partyBOpeningBank"
                  placeholder="请输入乙方开户银行"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="乙方开户行账号" prop="partyBAccountNumber">
                <el-input
                  v-model="signatoryInformationForm.partyBAccountNumber"
                  placeholder="请输入乙方开户行账号"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row> -->
          <el-row :gutter="20">
            <!-- <el-col :span="24">
              <el-form-item label="合同附件" prop="contractAttachments">
                <div class="upload-section">
                  <el-upload
                    ref="upload"
                    class="upload-area"
                    action="#"
                    :limit="1"
                    :auto-upload="false"
                    :file-list="signatoryInformationForm.contractAttachments"
                    :on-change="handleFileChange"
                    :on-remove="handleFileRemove"
                    :on-exceed="handleExceed"
                    drag
                  >
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">
                      点击或将文件拖拽到此处上传
                      <div class="upload-tip">
                        支持扩展名：.rar、.zip、.doc、.docx、.pdf
                      </div>
                    </div>
                  </el-upload>
                </div>
              </el-form-item>
            </el-col> -->
            <el-col :span="24">
              <el-form-item label="合同附件" prop="contractAttachments">
                <Upload
                  v-model="signatoryInformationForm.contractAttachments"
                  :limit="1"
                  :maxSizeMB="10"
                  :accept="'application/zip,application/rar,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf'"
                  @input="handleUpload"
                  @success="handleUploadSuccess"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="bottom-wrap">
      <el-button @click="() => handleCancel()">
        <span v-if="isDetail">返回</span>
        <span v-if="!isDetail">取消</span>
      </el-button>
      <el-button type="primary" @click="() => handleConfirm()" v-if="!isDetail">
        保存
      </el-button>
    </div>
  </div>
</template>

<script>
import {
  addContract,
  editContract,
} from '@/api/contractAgreementManagement/contractManagement';
import Upload from '@/components/Upload/index';

export default {
  dicts: [
    'template_type', // 合同类型(主)
    'cxfw_template_two', // 出行服务
    'llhd_template_two', // 两轮换电合同
    'cddyy_template_two', // 充电代运营合同
    'cwhd_template_two', // 车网互动合同
  ],
  components: {
    Upload,
  },
  data() {
    return {
      // 合同信息
      contractInformationForm: {
        code: '',
        name: '',
        type: [],
        typeDesc: '',
        contractDate: [],
        // startDate: '',
        // endDate: '',
        // contractLevel: '',
      },
      contractInformationRules: {
        code: [{ required: true, message: '请输入合同编号', trigger: 'blur' }],
        name: [{ required: true, message: '请输入合同名称', trigger: 'blur' }],
        type: [
          { required: true, message: '请选择合同类型', trigger: 'change' },
        ],
        contractDate: [
          {
            required: true,
            message: '请选择合同开始结束日期',
            trigger: 'change',
          },
        ],
        // startDate: [
        //   { required: true, message: '请选择合同开始日期', trigger: 'change' },
        // ],
        // endDate: [
        //   { required: true, message: '请选择合同结束日期', trigger: 'change' },
        // ],
        // contractLevel: [
        //   { required: true, message: '请选择合同密级', trigger: 'change' },
        // ],
      },
      contractInformationFlag: false,
      // 合同签约方信息
      signatoryInformationForm: {
        // partyA: '',
        // partyAResponsiblePerson: '',
        // partyAContactInfo: '',
        // partyACompanyAddress: '',
        // partyB: '',
        // partyBResponsiblePerson: '',
        // partyBContactInfo: '',
        // partyBCompanyAddress: '',
        // partyBOpeningBank: '',
        // partyBAccountNumber: '',
        contractAttachments: [], // 文件上传功能
      },
      signatoryInformationRules: {
        // partyA: [
        //   { required: true, message: '请输入合同甲方', trigger: 'blur' },
        // ],
        // partyAResponsiblePerson: [
        //   { required: true, message: '请输入甲方负责人', trigger: 'blur' },
        // ],
        // partyAContactInfo: [
        //   { required: true, message: '请输入甲方联系方式', trigger: 'blur' },
        // ],
        // partyACompanyAddress: [
        //   { required: true, message: '请输入甲方公司地址', trigger: 'blur' },
        // ],
        // partyB: [
        //   { required: true, message: '请输入合同乙方', trigger: 'blur' },
        // ],
        // partyBResponsiblePerson: [
        //   { required: true, message: '请输入乙方负责人', trigger: 'blur' },
        // ],
        // partyBContactInfo: [
        //   { required: true, message: '请输入乙方联系方式', trigger: 'blur' },
        // ],
        // partyBCompanyAddress: [
        //   { required: true, message: '请输入乙方公司地址', trigger: 'blur' },
        // ],
        // partyBOpeningBank: [
        //   { required: true, message: '请输入乙方开户银行', trigger: 'blur' },
        // ],
        // partyBAccountNumber: [
        //   { required: true, message: '请输入乙方开户行账号', trigger: 'blur' },
        // ],
        contractAttachments: [
          { required: true, message: '请上传合同附件', trigger: 'change' },
        ],
      },
      signatoryInformationFlag: false,
      isDetail: false,
      contractTypeOptions: [],
      contractLevelOptions: [
        { label: '公开', value: 'PUBLIC' },
        { label: '内部', value: 'INTERNAL' },
        { label: '机密', value: 'CONFIDENTIAL' },
      ],
    };
  },
  mounted() {
    this.contractTypeOptions = this.dict.type.template_type;
    console.log('this.contractTypeOptions', this.contractTypeOptions);
    this.$nextTick(() => {
      //   console.log('this.contractTypeOptions', this.contractTypeOptions.length);
      this.contractTypeOptions.forEach((item) => {
        if (item.value == 'cxfw_template_two') {
          if (
            this.dict.type.cxfw_template_two &&
            this.dict.type.cxfw_template_two.length > 0
          ) {
            item.children = this.dict.type.cxfw_template_two;
          }
        } else if (item.value == 'llhd_template_two') {
          if (
            this.dict.type.llhd_template_two &&
            this.dict.type.llhd_template_two.length > 0
          ) {
            item.children = this.dict.type.llhd_template_two;
          }
        } else if (item.value == 'cddyy_template_two') {
          if (
            this.dict.type.cddyy_template_two &&
            this.dict.type.cddyy_template_two.length > 0
          ) {
            item.children = this.dict.type.cddyy_template_two;
          }
        } else if (item.value == 'cwhd_template_two') {
          if (
            this.dict.type.cwhd_template_two &&
            this.dict.type.cwhd_template_two.length > 0
          ) {
            item.children = this.dict.type.cwhd_template_two;
          }
        }
        console.log('==', item);
      });
      // console.log('this.contractTypeOptions', this.contractTypeOptions);
      // console.log('route', this.$route.query);
      if (this.$route.query && this.$route.query.id) {
        this.contractInformationForm = {
          id: this.$route.query.id,
          code: this.$route.query.code,
          name: this.$route.query.name,
          type: JSON.parse(this.$route.query.type),
          typeDesc: '',
          // startDate: this.$route.query.startDate,
          // endDate: this.$route.query.endDate,
          contractDate: [
            this.$route.query.startDate,
            this.$route.query.endDate,
          ],
        };
        this.handleChange(this.contractInformationForm.type);
        this.signatoryInformationForm.contractAttachments = [
          {
            name: this.$route.query.attachment,
            url: this.$route.query.attachmentPath,
          },
        ];
        this.$forceUpdate();
      }
    });
  },
  methods: {
    // 取消
    handleCancel() {
      this.$router.back();
    },
    // 确定
    async handleConfirm() {
      //   console.log('提交', this.signatoryInformationForm.contractAttachments);
      //   console.log('提交', this.contractInformationForm.type);
      this.$refs.contractInformationForm.validate(async (valid) => {
        this.contractInformationFlag = valid;
      });
      this.$refs.signatoryInformationForm.validate(async (valid) => {
        this.signatoryInformationFlag = valid;
      });
      if (this.contractInformationFlag && this.signatoryInformationFlag) {
        let params = {
          ...this.signatoryInformationForm,
          ...this.contractInformationForm,
          attachment: '',
          attachmentPath: '',
          startDate:
            this.contractInformationForm?.contractDate.length > 0
              ? this.contractInformationForm?.contractDate[0]
              : '',
          endDate:
            this.contractInformationForm?.contractDate.length > 0
              ? this.contractInformationForm?.contractDate[1]
              : '',
        };
        if (
          this.signatoryInformationForm.contractAttachments &&
          this.signatoryInformationForm.contractAttachments.length > 0
        ) {
          params.attachment =
            this.signatoryInformationForm.contractAttachments[0].name;
          params.attachmentPath =
            this.signatoryInformationForm.contractAttachments[0].url;
        }
        if (this.contractInformationForm.id) {
          const [err, res] = await editContract(params);
          this.loading = false;
          if (err) return;
          this.$message.success('修改成功');
        } else {
          const [err, res] = await addContract(params);
          this.loading = false;
          if (err) return;
          this.$message.success('新增成功');
        }
        this.handleCancel();
      }
    },
    // 用户选择的文件数量超过 limit 限制
    handleExceed(files, fileList) {
      console.log(files, fileList);
      // 清空已选文件列表
      this.$refs.upload.clearFiles();
      // 手动添加新选择的文件（首个文件）
      this.$refs.upload.handleStart(files[0]);
    },
    // 文件上传相关方法
    handleFileChange(file, fileList) {
      console.log('文件变更', fileList);
      this.signatoryInformationForm.contractAttachments = fileList;
      // 模拟上传进度
      if (fileList.length > 0) {
        this.uploadProgress = 10;
        const timer = setInterval(() => {
          this.uploadProgress += 10;
          if (this.uploadProgress >= 100) {
            clearInterval(timer);
          }
        }, 300);
      }
      this.$refs.signatoryInformationForm.validate(); // 手动触发校验
    },
    handleFileRemove(file, fileList) {
      this.signatoryInformationForm.contractAttachments = fileList;
      if (fileList.length === 0) {
        this.uploadProgress = 0;
      }
      this.$refs.signatoryInformationForm.validate();
    },
    // 联级选择变更事件
    handleChange(value) {
      // console.log('修改级联选择', value);
      //   this.contractInformationForm.typeDesc = [];
      let typeDesc = [];
      this.contractTypeOptions.forEach((item) => {
        if (item.value == this.contractInformationForm.type[0]) {
          typeDesc.push(item.label);
          if (item.children.length > 0) {
            item.children.forEach((it) => {
              if (it.value == this.contractInformationForm.type[1]) {
                typeDesc.push(it.label);
              }
            });
          }
        }
      });
      this.$nextTick(() => {
        this.contractInformationForm.typeDesc = typeDesc.join('/');
        console.log(
          'this.contractInformationForm',
          this.contractInformationForm.typeDesc
        );
      });
    },
    handleUpload(file) {
      console.log('上传中:', file);
    },
    handleUploadSuccess(file) {
      console.log('上传成功:', file);
      // console.log('提交', this.signatoryInformationForm.contractAttachments);
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .info-card {
    margin: 16px;
    border-radius: 5px;
    border: 1px solid #fff;
    background-color: #fff;

    .card-head {
      height: 56px;
      padding: 0 16px;
      display: flex;
      align-items: center;

      .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
      }

      .card-head-text {
        font-weight: 500;
        font-size: 16px;
        color: #12151a;
      }
    }

    .form-wrap {
      padding: 16px;
    }
  }

  .upload-section {
    margin-top: 20px;

    .upload-title {
      font-size: 16px;
      color: #505363;
      margin-bottom: 10px;
    }

    .upload-area {
      width: 100%;
      border-radius: 6px;

      .upload-tip {
        font-size: 12px;
        color: #909399;
        margin-top: 8px;
      }
    }
  }

  .bottom-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
  }
}
</style>
