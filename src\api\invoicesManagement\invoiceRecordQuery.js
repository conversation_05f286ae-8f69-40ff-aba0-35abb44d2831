import request from '@/utils/request';

const baseUrl = '/vehicle-grid-system';

// 分页查询发票记录
export function queryInvoiceRecordPage(data) {
  return request({
    url: baseUrl + '/invRecord/queryInvoiceRecordPage',
    method: 'post',
    data: data,
  });
}

// 查询订单信息
export function queryInvOrderPage(data) {
  return request({
    url: baseUrl + '/invRecord/queryInvOrderPage',
    method: 'post',
    data: data,
  });
}

// 查询发票关联订单统计信息
export function queryInvOrderStatistics(data) {
  return request({
    url: baseUrl + '/invRecord/queryInvOrderStatistics',
    method: 'post',
    data: data,
  });
}

// 开蓝票
export function makeOutBlueInvoice(data) {
  return request({
    url: baseUrl + '/inv/makeOutBlueInvoice',
    method: 'post',
    data: data,
  });
}

// 开红票
export function makeOutRedInvoice(data) {
  return request({
    url: baseUrl + '/inv/makeOutRedInvoice',
    method: 'post',
    data: data,
  });
}
