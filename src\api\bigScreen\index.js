import request from "@/utils/request";
/**
 * @description: 大屏相关接口
 */
export const getAreaList = (data) => {
    return request({
        // url: '/vehicle-grid-admin/area/list',
        url: '/vehicle-grid-admin/area/list',
        method: 'post',
        data,
        isDebounce: true
    })
}

export const getMapAreaList = (data) => {
    return request({
        // url: '/vehicle-grid-admin/area/list',
        url: '/vehicle-charging-admin/area/list',
        method: 'post',
        data,
        isDebounce: true
    })
}

/**
 * @description: 站点相关区域接口
 */
export const getStationAreaList = (data) => {
    return request({
        url: '/vehicle-grid-admin/station/list',
        method: 'post',
        data,
        isDebounce: true
    })
}


/**
 * @description: 获取数据统计
 */
export const getStatistic = (data) => {
    return request({
        url: '/vehicle-grid-admin/station/statistics',
        method: 'post',
        data,
        isDebounce: true
    })
}

/**
 * @description: 获取筛选项
 */
export const getOperationList = (data) => {
    return request({
        url: '/vehicle-grid-admin/operators/list',
        method: 'post',
        data,
        isDebounce: true
    })
}

/**
 * @description: 查看充电站明细
 */
export const getStationDetailList = (data) => {
    return request({
        url: '/vehicle-grid-admin/station/detailList',
        method: 'post',
        data,
        isDebounce: true
    })
}

/**
 * @description: 区级 台区级获取站点列表数据
 */
export const getStationByAreaCode = (data) => {
    return request({
        url: '/vehicle-grid-admin/station/getStationByAreaCode',
        method: 'post',
        data,
        isDebounce: true
    })
}

/**
 * @description: 需求池
 */
export const getDemandPool = (data) => {
    return request({
        url: '/vehicle-grid-admin/dataOverview/demandPool',
        method: 'post',
        data,
        isDebounce: true
    })
}
/**
 * @description: 资源设施分析
 */
export const getFacilityAnalysis = (data) => {
    return request({
        url: '/vehicle-grid-admin/dataOverview/facilityAnalysis',
        method: 'post',
        data,
        isDebounce: true
    })
}
/**
 * @description: 台区负荷分析
 */
export const getLoadAnalysis = (data) => {
    return request({
        url: '/vehicle-grid-admin/dataOverview/loadAnalysis',
        method: 'post',
        data,
        isDebounce: true
    })
}
/**
 * @description: 交易申报
 */
export const getTransactionReporting = (data) => {
    return request({
        url: '/vehicle-grid-admin/dataOverview/transactionReporting',
        method: 'post',
        data,
        isDebounce: true
    })
}



/**
 * @description: 供给池
 */
export const getSupplyPool = (data) => {
    return request({
        url: '/vehicle-grid-admin/dataOverview/supplyPool',
        method: 'post',
        data,
        isDebounce: true
    })
}


/**
 * @description: 可控资源池
 */
export const getResourcePool = (data) => {
    return request({
        url: '/vehicle-grid-admin/dataOverview/resourcePool',
        method: 'post',
        data,
        isDebounce: true
    })
}

/**
 * @description: 可控资源池
 */
export const getTotal = (data) => {
    return request({
        url: '/vehicle-grid-admin/dataOverview/total',
        method: 'post',
        data,
        isDebounce: true
    })
}

/**
 * @description: 互动概览数据
 */
export const getOverviewData = (data) => {
    return request({
        url: '/vehicle-grid-admin/interactiveOverview/query',
        method: 'post',
        data,
        isDebounce: true
    })
}

/**
 * @description:  历史负荷调控需求池 
 */
export const getHisDemandPool = (data) => {
    return request({
        url: '/vehicle-grid-admin/interactiveOverview/hisDemandPool',
        method: 'post',
        data,
        isDebounce: true
    })
}


/**
 * @description: 获取台区
 */
export const getTg = (data) => {
    return request({
        url: '/vehicle-grid-admin/area/getTg',
        method: 'post',
        data,
        isDebounce: true
    })
}

/**
 * @description: 站点详情
 */
export const getStationDetail = (data) => {
    return request({
        url: '/vehicle-grid-admin/interactiveOverview/stationDetail',
        method: 'post',
        data,
        isDebounce: true
    })
}


/**
 * @description: 地图获取站点详情
 */
export const getDetailByStationCode = (data) => {
    return request({
        url: '/vehicle-grid-admin/station/equipment/stationDetail',
        method: 'post',
        data,
        isDebounce: true
    })
}
