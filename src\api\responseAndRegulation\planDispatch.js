import request from '@/utils/request'

const baseUrl = '/vehicle-grid-admin'

// 调控计划下发列表
export function getAdjustmentPlanReleaseList(data) {
  return request({
    url: baseUrl + '/adjustment/plan/release/list',
    method: 'post',
    data: data
  })
}

// 获取需求工单选项列表
export function getRequirementOptions(data) {
    return request({
        url: baseUrl + '/adjustment/requirement/list/simple',
        method: 'post',
        data: data
    })
}

// 调控计划批量发送
export function sendAdjustmentPlan(data) {
    return request({
        url: baseUrl + '/adjustment/plan/release/send',
        method: 'post',
        data: data
    })
}

// 站点调控计划明细列表
export function getPlanStationList(data) {
    return request({
        url: baseUrl + '/adjustment/plan/release/list/station',
        method: 'post',
        data: data
    })
}

// 户号明细列表
export function getConsumerList(data) {
    return request({
        url: baseUrl + '/adjustment/plan/release/list/consumer',
        method: 'post',
        data: data
    })
}

// 出清结果匹配
export function confirmClearPlan(data) {
    return request({
        url: baseUrl + '/adjustment/plan/clear/confirm',
        method: 'post',
        data: data
    })
}   

// 车主计划列表
export function getDriverList(data) {
    return request({
        url: baseUrl + '/adjustment/plan/release/list/driver',
        method: 'post',
        data: data
    })
}

// 车主计划详情
export function getDriverDetail(data) {
    return request({
        url: baseUrl + '/adjustment/plan/release/driver/detail',
        method: 'post',
        data: data
    })
}