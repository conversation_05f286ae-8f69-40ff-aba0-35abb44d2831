<template>
  <div class="bg-title">
    <slot />
  </div>
</template>

<script>
export default {
  name: 'BgTitle',
  props: {},
  data() {
    return {};
  },
  watch: {},
  mounted() {},
  methods: {},
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.bg-title {
  height: 26px;
  background-image: url('~@/assets/images/bg-title.png');
  background-size: 520px 26px;
  background-repeat: no-repeat;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px;
  padding-left: 36px;
}
</style>
