<template>
  <div class="container container-float ">
    <div class="table-wrap">
          <BuseCrud
              ref="crud"
              :loading="loading"
              :filterOptions="filterOptions"
              :tablePage="tablePage"
              :tableColumn="tableColumn"
              :tableData="tableData"
              :pagerProps="pagerProps"
              :modalConfig="modalConfig"
                class="buse-wrap-station"
              @loadData="loadData"
          >
          <template slot="defaultHeader">
              <div>
                  <div class="card-head">
                      <div class="card-head-text">线损预警规则管理</div>

                      <div class="top-button-wrap">

                        <el-button
                            type="primary"
                            @click="handleAdd"
                        >
                            <svg-icon iconClass="a-add"></svg-icon>
                            新增线损规则
                        </el-button>

                      </div>
                  </div>
                  

                  
              </div>
              
          </template>

              <template slot="operate" slot-scope="{ row }">
                  <div class="menu-box">
                      <el-button
                          class="button-border"
                          @click="hanleDetail(row)"
                      >
                          详情
                      </el-button>

                      <el-button
                          class="button-border"
                          @click="handelStatus(row,'1')"
                      >
                          停用
                      </el-button>

                      <el-button
                          class="button-border"
                          @click="handelStatus(row,'2')"
                      >
                          启用
                      </el-button>

                      <el-button
                          class="button-border"
                          @click="handleDelete(row)"
                      >
                          删除
                      </el-button>



                  
                  </div>
              
              </template>

          </BuseCrud>
      </div>
 
  </div>
  
</template>

<script>


  export default {
  components: {
    
  },
  dicts: [],
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'rulerId',
          title: '预警规则编号',
          minWidth: 180,
        },
        {
          field: 'ruleName',
          title: '预警规则名称',
          minWidth: 180,
        },
        {
          field: 'warningCycle',
          title: '预警周期',
          minWidth: 180,
        },
        {
          field: 'stationId',
          title: '适用充电站',
          minWidth: 180,
        },
        {
          field: 'warningInfo',
          title: '预警内容',
          minWidth: 180,
        },
        {
          field: 'warningMethod',
          title: '预警措施',
          minWidth: 180,
        },
        {
          field: 'channel',
          title: '通知渠道',
          minWidth: 180,
        },
        {
          field: 'status',
          title: '状态',
          minWidth: 180,
        },
        {
          field: 'createTime',
          title: '创建时间',
          minWidth: 180,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],
      tableData: [
      {
        rulerId: 'WR001',
        ruleName: '设备故障预警规则',
        warningCycle: '每天',
        stationId: 'STATION1001',
        warningInfo: '设备离线持续超过2小时触发预警',
        warningMethod: '自动发送维修通知',
        channel: '短信, 邮件',
        status: '启用',
        createTime: '2023-03-15 14:30:00'
      },
      {
        rulerId: 'WR002',
        ruleName: '温度过高预警规则',
        warningCycle: '每小时',
        stationId: 'STATION1002',
        warningInfo: '充电桩温度超过60℃时触发',
        warningMethod: '自动切断电源并通知',
        channel: '短信, 应用内通知',
        status: '启用',
        createTime: '2023-03-16 09:15:00'
      },
      {
        rulerId: 'WR003',
        ruleName: '电量不足预警规则',
        warningCycle: '每30分钟',
        stationId: 'STATION1003',
        warningInfo: '电池电量低于20%时触发',
        warningMethod: '推送充电提醒',
        channel: '邮件, 应用内通知',
        status: '禁用',
        createTime: '2023-03-17 16:45:00'
      }],
      params: {
        rulerId: '',
        ruleName: '',
        stationId: '',
        cycle: '',
        createTime: '',
        status: '',
      },

    };
  },

  computed: {
    filterOptions() {
      return {
        config: [
          {
              field: 'rulerId',
              title: '预警规则编号',
              element: 'el-input',
          },
          {
              field: 'ruleName',
              title: '预警规则名称',
              element: 'el-input',
          },
          {
              field: 'stationId',
              title: '充电站',
              element: 'el-select',
              props: {
                  placeholder: '请选择',
                  options: []
              }
          },
          {
              field: 'cycle',
              title: '预警周期',
              element: 'el-select',
              props: {
                  placeholder: '请选择',
                  options: []
              }
          },
          {
              field: 'createTime',
              title: '创建时间',
              element: 'el-date-picker',
              props: {
                  type: 'daterange',
                  valueFormat: 'yyyy-MM-dd',
                  options: [],
              }
          },
          {
              field: 'status',
              title: '状态',
              element: 'el-select',
              props: {
                  placeholder: '请选择',
                  options: []
              }
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
          addBtn: false,
          viewBtn: false,
          menu: false,
          editBtn: false,
          delBtn: false,
      }
    },
  },
  mounted() {

  },
  methods: {
    //  新增线损规则
    handleAdd() {
      this.$router.push({
        path: '/v2g-charging/earlyWarningManage/lineLossEarlyWarning/lineLossRuler/add',
      })
    },

    // 线损规则详情
    hanleDetail(row) {
      this.$router.push({
        path: '/v2g-charging/earlyWarningManage/lineLossEarlyWarning/lineLossRuler/detail',
        query: {
          id: row.id,
        },
      })
    }

  },
}
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}
 

.table-wrap {
  ::v-deep .bd3001-table-select-box {
      display: none;
  }
  ::v-deep .bd3001-header  {
      display: block;
  }
  ::v-deep .bd3001-button {
      display: block !important;
  }
  
  .card-head {
      // position: relative; 
      height: 56px;
      padding: 0 16px;
      display: flex;
      align-items: center;
      margin-top: -20px;
      .card-head-text {
      flex:1;
      width: 520px;
          height: 26px;
          background-image: url('~@/assets/images/bg-title.png');
          background-size: 520px 26px;
          background-repeat: no-repeat;
          font-size: 20px;
          font-style: normal;
          font-weight: 500;
          line-height: 16px;
          padding-left: 36px;
          color: #21252e;
      &::before {
          content: '';
          position: absolute;
          top: 0;
          bottom: 0;
          left: -3px; /* 调整这个值来改变边框的宽度 */
          width: 0;
          border-top: 3px solid transparent;
          border-bottom: 3px solid transparent;
          border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
      }   

  }

  .card-head-after {
      width: 100%;
      height: 1px;
      background-color: #DCDEE2;
      margin-bottom: 16px;
  }
  .info-wrap {
      margin-top: 16px;
      display: flex;
      flex-wrap: wrap;
      gap: 16px;

      .info-item {
          background-color: #FAFBFC;
          flex: 1 1 0;
          // min-width: 180px;
          
          border-radius: 5px;
          padding: 8px 24px;
          box-sizing: border-box;
          // margin-right: 16px;
          display: flex;
          .info-icon {
              width: 42px;
              height: 42px;
          }
          .info-right-wrap {
              flex:1;
              margin-left: 8px;
              .info-title {
                  font-weight: 400;
                  font-size: 14px;
                  line-height: 14px;
                  margin-bottom: 8px;
              }
              .info-number {
                  font-size: 20px;
                  font-weight: 500;
                  .info-unit {
                      font-size: 14px;
                      font-weight: 400;
                  }
              }
          }
      }
      .info-item:last-child {
          margin-right: 0;
      }
  }

  .top-button-wrap {
      display:flex;
      margin: 16px 0;
      .set-btn {
        background-color: #FFFFFF;
        color: #292B33;
        border-color: #DFE1E5;
    }
  }
}

.button-border {
  border: 0.01rem solid #217AFF;
  color: #217AFF;
  background-color: #fff;
}

</style>
