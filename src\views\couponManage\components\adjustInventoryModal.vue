<template>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="630px"
      @close="handleCancel"
    >
        <div class="info-wrap">
            <div class="info-title">券业务类型：</div>
            <div class="info-detail">{{ form.couponBusinessType }}</div>
        </div>

        <div class="info-wrap">
            <div class="info-title">券基本类型：</div>
            <div class="info-detail">{{ form.basicType }}</div>
        </div>

        <div class="info-wrap">
            <div class="info-title">优惠券名称：</div>
            <div class="info-detail">{{ form.couponName }}</div>
        </div>

        <div class="info-wrap">
            <div class="info-title">优惠券编码：</div>
            <div class="info-detail">{{ form.couponNo}}</div>
        </div>

        <el-form :model="form" :rules="rules" ref="ruleForm"  label-position="top">
            <el-row :gutter="20">
                <el-col :span="20">
                    <el-form-item
                        label="调整方式："
                        prop="adjustType"
                        :label-width="formLabelWidth"
                    >
                        <el-radio-group
                            v-model="form.adjustType"
                        >
                            <el-radio
                                v-for="dict in adjustTypeList"
                                :key="dict.value"
                                :label="dict.value"
                            >
                                {{ dict.label }}
                            </el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>

                <el-col :span="20">
                    <el-form-item
                        label="调整数量："
                        prop="adjustNumber"
                        :label-width="formLabelWidth"
                    >
                    <el-input-number
                        v-model="form.adjustNumber"
                        :min="0"
                        :precision="0"
                        :step="1"
                        :controls="false"
                    ></el-input-number>
                    </el-form-item>
                </el-col>

            </el-row>
           

        </el-form>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
    </el-dialog>
  </template>
  <script>  

import {
    adjustStock
} from '@/api/operator/coupon';


import moment from 'moment';

import _ from 'lodash';

  export default {
    props: {
        dialogTitle:{
            type: String,
            default: '调控配置'
        }
    },
    components: {
  
    },
    dicts: [
        'ls_charging_status', // 是否状态
        'ls_charging_adjustable_type', // 可调控类型
        'ls_charging_station_control', // 站点调节方式
    ],
    data() {
        return {
            dialogVisible: false,
            couponId: '',
            form: {
                couponBusinessType: '',
                basicType: '',
                couponName: '',
                couponNo: '',
                adjustType: '',
                adjustNumber: '',
            },
            rules: {
                adjustType: [
                    { required: true, message: '请选择调整方式', trigger: 'change' },
                ],
                adjustNumber: [
                    { required: true, message: '请输入调整数量', trigger: 'blur' },
                ]

            },
            formLabelWidth: '120px',

            adjustTypeList: [
                {
                    label: '增加库存',
                    value: 'INCREASE'
                },
                {
                    label: '减少库存',
                    value: 'REDUCE'
                },
            ]
        };
    },
    computed: {},
    mounted() {},
    methods: {

        resetForm() {
            this.couponId = '';

            Object.keys(this.form).forEach((key) => {
                this.form[key] = '';
                
            });
        },

        handleCancel() {
            this.$refs.ruleForm.resetFields();
            this.resetForm();
            console.log(this.form, 'this.form')
            this.dialogVisible = false;
        },



        // 新增按钮防抖        
        handleConfirm: _.debounce(function() {
            this.$refs.ruleForm.validate(async (valid) => {
                if (valid) {
                     const {
                        adjustType,
                        adjustNumber,
                     } = this.form;

                     const params = {
                         couponId: this.couponId,
                         type: adjustType,
                         number: adjustNumber,
                     }
                   
                        const [err, res] = await adjustStock(params);
                        if (err) return;
                    
                        this.$message.success('调整成功');
                        this.dialogVisible = false;
                        this.couponId  = '';
                        this.$emit('loadData');
                    
                }
            });
        }, 300) ,


    },
  };
  </script>
  <style lang="scss" scoped>
::v-deep .el-form-item__content{
    display: flex !important;
}

::v-deep .el-input-number {
    width: 90% !important;
}



.info-wrap {
    display: flex;
    height: 20px;
    margin-bottom: 24px;
    align-items: center;
    .info-title {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #505363;
      }
      .info-detail {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #292B33;
      }
}
  </style>
  