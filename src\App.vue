<template>
  <div id="app">
    <router-view />
    <theme-picker />
  </div>
</template>

<script>
import ThemePicker from "@/components/ThemePicker";

export default {
  name: "App",
  components: { ThemePicker },
  metaInfo() {
    return {
      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
      titleTemplate: title => {
        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
      }
    }
  },
  created() {
    window.microApp?.addDataListener(this.getDataFromMainHandler, true)
  },
  beforeDestroy() {
    window.microApp?.removeDataListener(this.getDataFromMainHandler)
  },
  methods: {
    // data：主应用传递过来的参数
    getDataFromMainHandler(data) {
      this.$router.replace({ path: data.pagePath })
      //其他数据自行处理；
    }
  }
};
</script>
<style scoped>
#app .theme-picker {
  display: none;
}
</style>
