import request from '@/utils/request';

// 需求列表查询
export const controlRequireList = (data) => {
  return request({
    url: '/vehicle-grid-admin/monitor/controlExec/requireList',
    method: 'post',
    data,
  });
};
// 调控执行监控
export const controlExecQuery = (data) => {
  return request({
    url: '/vehicle-grid-admin/monitor/controlExec/query',
    method: 'post',
    data,
  });
};
// 历史调控
export const historyRegulation = (data) => {
  return request({
    url: '/vehicle-grid-admin/monitor/controlExec/historyRegulation',
    method: 'post',
    data,
  });
};
// 调控详情
export const regulationDetail = (data) => {
  return request({
    url: '/vehicle-grid-admin/monitor/controlExec/regulationDetail',
    method: 'post',
    data,
  });
};
// 调控日志
export const regulationLog = (data) => {
  return request({
    url: '/vehicle-grid-admin/monitor/controlExec/regulationLog',
    method: 'post',
    data,
  });
};
// 场站详情
export const stationDetail = (data) => {
  return request({
    url: '/vehicle-grid-admin/monitor/controlExec/stationDetail',
    method: 'post',
    data,
  });
};
// 场站调控
export const stationRegulation = (data) => {
  return request({
    url: '/vehicle-grid-admin/monitor/controlExec/stationRegulation',
    method: 'post',
    data,
  });
};
