import HuNan_District_Json from '@/assets/bigScreen/hunan_district.json';
import HuNan_City_Json from '@/assets/bigScreen/hunan_city.json';
import Vue from 'vue';
import MapMarker from '@/views/stationInteraction/components/MapMarker.vue';
import StationMarker from '@/views/stationInteraction/components/StationMarker.vue';
import MapBaseContext from './MapBaseContext';
import { MapDisplayLevel } from './constants';
import MapEvent from './MapEvent';

const BoundaryLineStyle = {
    strokeColor: "#0092FA",
    strokeWeight: 2,
    fillColor: "#D3E4FE",
    fillOpacity: 0.8,
    cursor: 'pointer'
};

export default class MapContext extends MapBaseContext {

    // _map = null;
    // _mapOptions = null;
    // _Amap = null;
    _geojsonLayer = null;
    currentMarkerList = [];
    markerPool = [];
    parentAdcode = '';

    // 当前地图层级
    currentLevel = MapDisplayLevel.Province;
    // 行政区对应的经纬度中心点
    codeCenterMap = new Map();

    // 行政中心地图列表
    mapCodeList = [];

    // 站点列表数据
    stationList = [];


    constructor(id, options = {}) {
        super({ id, options });
        return (async () => {
            await this.init();
            return this;
        })();
    }

    async init() {
        await this.initMap();
        this.initSearch();
        // this.createGeoLayer(HuNan_City_Json);
        // this._map.setFitView();
    }

    createGeoLayer() {
        this._geojsonLayer = this.drawBoundaryLine(HuNan_City_Json, { ...BoundaryLineStyle }, (geojson, polygon) => {
            const { adcode: currentAdcode, name, center } = geojson.properties;

            let stationNum = ''

            console.log('currentAdcode', currentAdcode, name, center,this.mapCodeList)

            this.mapCodeList.forEach(item => {
                if (Number(item.areaCode) === currentAdcode) {
                    stationNum = item.stationNum
                }
            })

            this.addNormalMarker(center, stationNum);

            // 添加鼠标移入事件
            polygon.on('mouseover', function (event) {
                // 鼠标移入时改变样式
                polygon.setOptions({
                    fillColor: 'rgba(104, 148, 251, 0.47)',
                    strokeWeight: 3
                });
            });

            // 添加鼠标移出事件
            polygon.on('mouseout', function (event) {
                // 鼠标移出时恢复样式
                polygon.setOptions({
                    fillColor: "#D3E4FE",
                    strokeWeight: 2
                });
            });

            polygon.on('click', () => {
                if (this.currentLevel === MapDisplayLevel.Province) {
                    this.currentLevel = MapDisplayLevel.City;

                    let temMapCodeList = []
                    this.mapCodeList.forEach(item => {
                        if (Number(item.areaCode) === currentAdcode) {
                           temMapCodeList = item.children
                        }
                    })
                    this.mapCodeList = temMapCodeList

                    this.showAreaMap(HuNan_District_Json, currentAdcode);
                } else if (this.currentLevel === MapDisplayLevel.City) {
                    this.currentLevel = MapDisplayLevel.District;
                    this.parentAdcode = currentAdcode;
                    // 下钻到区级 
                    // this.searchDistrict(name);
                    MapEvent.$emit('areaClick',{areaCode: currentAdcode, level:this.currentLevel});
                    return;
                }

            })
        });
    }

    // 地图下钻，adcode 为父级行政区的adcode
    showAreaMap(json, adcode) {
        if (!this._map || !this._geojsonLayer) return;
        // 清除地图上的所有覆盖物
        this.removeAllMarkers();
        let filterJson = json;
        if (adcode) {
            filterJson = {
                type: 'FeatureCollection',
                features: json.features.filter(item => item.properties.parent.adcode == adcode),
            };
        }
        this._geojsonLayer.importData(filterJson);
        this._map.setFitView();//地图自适应
    }

    // 地图切换到指定层级
    upMapToLevel(level, adcode) {
        if (!Object.values(MapDisplayLevel).includes(level)) {
            throw new Error('level is not valid');
        }
        if (level === MapDisplayLevel.City) {
            this.showAreaMap(HuNan_District_Json, adcode);
        } else if (level === MapDisplayLevel.Province) {
            this.showAreaMap(HuNan_City_Json);
        } else if (level === MapDisplayLevel.District) {
            // 展示区级
            this.hanldeStationList()
            const center = []
            HuNan_District_Json.features.forEach((item) => {
                if(Number(item.properties.adcode) == Number(adcode)){
                    center.push(item.properties.center[0])
                    center.push(item.properties.center[1])
                }
            })
            console.log(center, 'center')

             this._map.setCenter(center); //设置地图中心点

        }
        this.currentLevel = level;
    }

    removeAllMarkers() {
        this._map.clearMap();
        this.markerPool = [...this.markerPool, ...this.currentMarkerList];
        this.currentMarkerList = [];
    }

    generateMarker() {

        let marker = this.markerPool.pop();
        if (!marker) {
            const { _Amap } = this;
            marker = new _Amap.Marker({
                anchor: 'bottom-center',
            });
        }
        return marker;
    }


    /**
     *  添加marker
     */
    addNormalMarker(lnglat, content) {
        this.addVueMarker(lnglat, MapMarker, {
            content: content || 0,
            highlight: Math.random() < 0.5
        });
    }

    // 添加充电桩marker
    addStationMarker(lnglat,info) {
        this.addVueMarker(lnglat, StationMarker, {
            content: 231,
            info: info,
            stationClick(){
                console.log( ' mapCodeList stationClick');
                MapEvent.$emit('stationClick', info.stationNo);
            },
        });
    }

    searchDistrict(name) {
        console.time('查询耗时' + name);
        this._districtSearch.search(name, (status, result) => {
            if (status === 'complete') {
                // this.removeAllMarkers();
                // result.districtList[0].districtList.forEach(item => {
                //     this.addStationMarker([item.center.lng, item.center.lat]);
                // });
                this._map.setFitView();//地图自适应
                console.timeEnd('查询耗时' + name);
            }
        });
    }

    hanldeStationList() {
        this.removeAllMarkers();
        this.stationList.forEach(item => {
            this.addStationMarker([item.lon, item.lat],item,);
        })
        this._map.setFitView();//地图自适应

       
    }
        

}