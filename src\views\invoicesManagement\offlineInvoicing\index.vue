<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        class="buse-wrap-station"

        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">线下收入开票管理</div>
          </div>
          <div class="card-head-after"></div>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button type="primary" plain @click="handleEdit(row)">
              审核
            </el-button>
            <el-button type="primary" plain @click="handleDetail(row)">
              详情
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableColumn: [
        { type: 'seq', title: '序号', width: 60 },
        { field: 'applyNo', title: '申请编号', minWidth: 120 },
        { field: 'sharingPeriod', title: '分成周期', minWidth: 150 },
        { field: 'stationName', title: '充电站名称', minWidth: 150 },
        { field: 'cityName', title: '地市', minWidth: 100 },
        { field: 'sharingType', title: '分成类型', minWidth: 120 },
        { field: 'settlementPartyType', title: '结算方类型', minWidth: 120 },
        { field: 'settlementParty', title: '结算方', minWidth: 120 },
        { field: 'sharingParty', title: '分成方', minWidth: 120 },
        {
          field: 'receivableElectricityFee',
          title: '应收电费（元）',
          minWidth: 140,
        },
        {
          field: 'receivableServiceFee',
          title: '应收服务费（元）',
          minWidth: 150,
        },
        {
          field: 'receivableOperationFee',
          title: '应收代运营费（元）',
          minWidth: 180,
        },
        {
          field: 'receivableRentFee',
          title: '应收场地租金（元）',
          minWidth: 180,
        },
        {
          field: 'receivableTotalAmount',
          title: '应收总金额（元）',
          minWidth: 180,
        },
        {
          field: 'applyElectricityFee',
          title: '申请开票电费（元）',
          minWidth: 180,
        },
        {
          field: 'applyServiceFee',
          title: '申请开票服务费（元）',
          minWidth: 200,
        },
        {
          field: 'applyOperationFee',
          title: '申请开票代运营费（元）',
          minWidth: 200,
        },
        {
          field: 'applyRentFee',
          title: '申请开票场地租金（元）',
          minWidth: 200,
        },
        {
          field: 'applyTotalAmount',
          title: '申请开票总金额（元）',
          minWidth: 200,
        },
        { field: 'invoiceType', title: '发票种类', minWidth: 120 },
        { field: 'applyUser', title: '申请人', minWidth: 100 },
        { field: 'applyTime', title: '申请时间', minWidth: 150 },
        { field: 'invoiceDate', title: '开票日期', minWidth: 150 },
        {
          field: 'auditStatus',
          title: '审核状态',
          minWidth: 120,
          fixed: 'right',
          slots: {
            default: ({ row }) => {
              const statusMap = {
                pending: { text: '审核中', color: '#FF9900' },
                approved: { text: '审核通过', color: '#00C864' },
                rejected: { text: '审核不通过', color: '#F56C6C' },
              };
              const status = statusMap[row.auditStatus] || {
                text: '未知',
                color: '#909399',
              };
              return [
                <div style={`display: flex; align-items: center;`}>
                  <div
                    style={`width: 6px; height: 6px; border-radius: 50%; background-color: ${status.color}; margin-right: 5px;`}
                  ></div>
                  <span>{status.text}</span>
                </div>,
              ];
            },
          },
        },
        {
          slots: { default: 'operate' },
          title: '操作',
          width: 200,
          align: 'center',
          fixed: 'right',
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        chargingStation: '',
        settlementParty: '',
        settlementPartyType: '',
        sharingType: '',
        sharingParty: '',
        auditStatus: '',
        applyTimeRange: [],
        invoiceType: '',
        invoiceDateRange: [],
        sharingPeriodRange: [],
        city: '',
      },
      // 下拉选项数据
      chargingStationOptions: [
        { label: '充电站A', value: 'station_a' },
        { label: '充电站B', value: 'station_b' },
      ],
      settlementPartyOptions: [
        { label: '结算方A', value: 'party_a' },
        { label: '结算方B', value: 'party_b' },
      ],
      settlementPartyTypeOptions: [
        { label: '类型A', value: 'type_a' },
        { label: '类型B', value: 'type_b' },
      ],
      sharingTypeOptions: [
        { label: '类型A', value: 'type_a' },
        { label: '类型B', value: 'type_b' },
      ],
      sharingPartyOptions: [
        { label: '分成方A', value: 'party_a' },
        { label: '分成方B', value: 'party_b' },
      ],
      auditStatusOptions: [
        { label: '审核中', value: 'pending' },
        { label: '审核通过', value: 'approved' },
        { label: '审核不通过', value: 'rejected' },
      ],
      invoiceTypeOptions: [
        { label: '类型A', value: 'type_a' },
        { label: '类型B', value: 'type_b' },
      ],
      cityOptions: [
        { label: '城市A', value: 'city_a' },
        { label: '城市B', value: 'city_b' },
      ],
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'chargingStation',
            title: '充电站',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.chargingStationOptions,
            },
          },
          {
            field: 'settlementParty',
            title: '结算方',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.settlementPartyOptions,
            },
          },
          {
            field: 'settlementPartyType',
            title: '结算方类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.settlementPartyTypeOptions,
            },
          },
          {
            field: 'sharingType',
            title: '分成类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.sharingTypeOptions,
            },
          },
          {
            field: 'sharingParty',
            title: '分成方',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.sharingPartyOptions,
            },
          },
          {
            field: 'auditStatus',
            title: '审核状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.auditStatusOptions,
            },
          },
          {
            field: 'applyTimeRange',
            title: '申请时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
          {
            field: 'invoiceType',
            title: '开票类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.invoiceTypeOptions,
            },
          },
          {
            field: 'invoiceDateRange',
            title: '开票日期',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
          {
            field: 'sharingPeriodRange',
            title: '分成周期',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
          {
            field: 'city',
            title: '地市',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.cityOptions,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      console.log('加载数据', this.params);

      // 模拟接口请求
      setTimeout(() => {
        this.tableData = [
          // 示例数据
          {
            applyNo: '2345580182',
            sharingPeriod: '2024-09-01~2024-09-30',
            stationName: '充电站1',
            cityName: '长沙',
            sharingType: '电费+代运营费',
            settlementPartyType: '公交',
            settlementParty: '公司1',
            sharingParty: '省电动',
            receivableElectricityFee: 2200,
            receivableServiceFee: 0,
            receivableOperationFee: 0,
            receivableRentFee: 0,
            receivableTotalAmount: 2200,
            applyElectricityFee: 2200,
            applyServiceFee: 0,
            applyOperationFee: 0,
            applyRentFee: 0,
            applyTotalAmount: 2200,
            invoiceType: '增值税专用发票',
            applyUser: '张三',
            applyTime: '2024-10-01 10:00:00',
            invoiceDate: '2024-10-05',
            auditStatus: 'pending',
          },
          // 更多示例数据...
        ];
        this.tablePage.total = this.tableData.length;
        this.loading = false;
      }, 500);
    },
    // 审核
    handleEdit(row) {
      console.log(row);
      this.$router.push({
        path: '/v2g-charging/invoicesManagement/offlineInvoicing/invoicingExamine',
        // query: {
        //   status,
        // },
      });
    },
    // 详情
    handleDetail(row) {
      console.log(row);
      this.$router.push({
        path: '/v2g-charging/invoicesManagement/offlineInvoicing/detail',
        // query: {
        //   status,
        // },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }
}
</style>
