<template>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="630px"
      @close="handleCancel"
    >
        <el-form :model="form" :rules="rules" ref="ruleForm"  label-position="top">
            <el-row :gutter="20">
                <el-col :span="20">
                    <el-form-item
                        label="二维码域名："
                        prop="domain"
                        :label-width="formLabelWidth"
                    >
                        <el-select
                            v-model="form.domain"
                            placeholder="请选择"
                            style="width: 100%"
                            >
                            <el-option
                                v-for="item in dict.type.ls_charging_qrCodeRule"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
           

        </el-form>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
    </el-dialog>
  </template>
  <script>  

import {
    batchIssue
} from '@/api/pile/index';


import moment from 'moment';

import _ from 'lodash';

  export default {
    props: {
        dialogTitle:{
            type: String,
            default: '下载二维码'
        }
    },
    components: {
  
    },
    dicts: [
        'ls_charging_qrCodeRule',  
    ],
    data() {
        return {
            dialogVisible: false,
            pileId: [],
            form: {
                domain: '',
            },
            rules: {
                domain: [
                    { required: true, message: '请选择二维码域名', trigger: 'blur' },
                ],
            },
            formLabelWidth: '120px',

        };
    },
    computed: {},
    mounted() {},
    methods: {

        resetForm() {
            this.pileId = [];

            Object.keys(this.form).forEach((key) => {
                this.form[key] = '';
            });
        },

        handleCancel() {
            this.$refs.ruleForm.resetFields();
            this.resetForm();
            console.log(this.form, 'this.form')
            this.dialogVisible = false;
        },



        // 新增按钮防抖        
        handleConfirm: _.debounce(function() {
            this.$refs.ruleForm.validate(async (valid) => {
                if (valid) {
                    console.log(this.form, 'this.form')
                    const {
                        domain
                    } = this.form;
             
                         const params = {
                            pileIds: this.pileId,
                            domain,
                            type: 'normal',
                        }

                        this.download(
                            '/vehicle-charging-admin/qrCode/batchGenerate',
                            {
                                ...params,
                            },
                            `充电桩二维码.zip`
                        );

                        // console.log('params', params)
                        // const [err, res] = await batchIssue(params);
                        // if (err) return;
                    
                        // this.$message.success('下载成功');
                        // this.dialogVisible = false;
                        // this.pileId  = '';
                        // this.$emit('loadData');
                    
                }
            });
        }, 300) ,


    },
  };
  </script>
  <style lang="scss" scoped>
::v-deep .el-form-item__content{
    display: flex !important;
}

::v-deep .el-input-number {
    width: 90% !important;
}



.info-wrap {
    display: flex;
    height: 20px;
    margin-bottom: 24px;
    align-items: center;
    .info-title {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #505363;
      }
      .info-detail {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #292B33;
      }
}
  </style>
  