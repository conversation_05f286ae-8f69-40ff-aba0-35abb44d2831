import request from '@/utils/request';

const baseUrl = '/vehicle-charging-admin';

// 分组站点分页查询
export function getStationGroupList(data) {
  return request({
    url: baseUrl + '/ops/stationGroup/page',
    method: 'post',
    data: data,
  });
}

// 站点分组查询
export function geGroupList(data) {
  return request({
    url: baseUrl + '/ops/stationGroup/groupList',
    method: 'post',
    data: data,
  });
}

// 分组站点新增
export function addStationGroup(data) {
  return request({
    url: baseUrl + '/ops/stationGroup/create',
    method: 'post',
    data: data,
  });
}

// 分组站点移除
export function removeStationGroup(data) {
  return request({
    url: baseUrl + '/ops/stationGroup/remove',
    method: 'post',
    data: data,
  });
}
