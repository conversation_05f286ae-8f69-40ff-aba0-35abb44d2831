<template>
  <div class="container container-float" style="padding: 0">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="tableLoading"
        :filterOptions="tableFilterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        class="buse-wrap-station"
        @loadData="loadTableData"
      >
        <template slot="defaultHeader">
          <div>
            <div class="card-head">
              <div class="card-head-text">巡视计划列表</div>

              <div class="top-button-wrap">
                <el-button
                  type="primary"
                  @click="() => handlePlanEdit('create', {})"
                >
                  新增巡视计划
                </el-button>
              </div>
            </div>
          </div>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button
              type="primary"
              plain
              @click="handlePlanEdit('edit', row)"
            >
              编辑
            </el-button>
            <el-button type="danger" plain @click="handlePlanDel(row)">
              删除
            </el-button>
            <el-button type="primary" plain @click="viewPlanDetail(row)">
              详情
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
    <editPlan
      ref="editPlan"
      :type="planType"
      :detailObj="selectPlan"
      @planAdd="loadTableData"
    />
  </div>
</template>

<script>
import editPlan from './components/editPlan.vue';
import {
  getPlanPage,
  deletePlan,
  getPlanDetail,
  getGroupList,
  getTeamPage,
} from '@/api/interconnection/patrolPlan';

export default {
  components: {
    editPlan,
  },
  dicts: [
    'ls_charging_patrol_type', // 巡视类型
    'ls_charging_patrol_frequencies_type', // 巡视频次
  ],
  data() {
    return {
      tableLoading: false,
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        patrolPlanId: '',
        patrolPlanName: '',
        groupId: '',
        maintenanceTeamId: '',
        patrolType: '',
      },
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'patrolPlanId',
          title: '计划编号',
          minWidth: 120,
        },
        {
          field: 'patrolPlanName',
          title: '计划名称',
          minWidth: 120,
        },
        {
          field: 'groupName',
          title: '分组名称',
          minWidth: 120,
        },
        {
          field: 'stationNum',
          title: '充电站数量',
          minWidth: 80,
        },
        {
          field: 'maintenanceTeamName',
          title: '运维班组',
          minWidth: 120,
        },
        {
          field: 'patrolType',
          title: '巡视类型',
          minWidth: 100,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_patrol_type,
              cellValue
            );
          },
        },
        {
          field: 'patrolFrequencies',
          title: '巡视频次',
          minWidth: 150,
        },

        {
          field: 'patrolDay',
          title: '巡视时效(天)',
          minWidth: 120,
        },
        {
          field: 'status',
          title: '状态',
          minWidth: 80,
          fixed: 'right',

          formatter: ({ cellValue }) => {
            return this.selectDictLabel(this.statusList, cellValue);
          },
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 250,
          align: 'center',
          fixed: 'right',
        },
      ],
      statusList: [
        { label: '启用', value: 0 },
        { label: '停用', value: 1 },
      ],
      tableData: [],
      selectPlan: {},
      planType: 'create',
      groupListLoading: false,
      groupList: [],
      teamPageLoading: false,
      teamPageList: [],
    };
  },

  computed: {
    tableFilterOptions() {
      return {
        config: [
          {
            field: 'patrolPlanId',
            title: '计划编号',
            element: 'el-input',
          },
          {
            field: 'patrolPlanName',
            title: '计划名称',
            element: 'el-input',
          },
          {
            field: 'groupId',
            title: '分组',
            element: 'el-select',
            props: {
              placeholder: '请选择分组',
              options: this.groupList,
              filterable: true,
              remote: true,
              remoteMethod: this.debouncedGroupListSearch,
              loading: this.groupListLoading,
            },
          },
          {
            field: 'maintenanceTeamId',
            title: '运维班组',
            element: 'el-select',
            props: {
              placeholder: '请选择运维班组',
              options: this.teamPageList,
              filterable: true,
              remote: true,
              remoteMethod: this.debouncedTeamPageSearch,
              loading: this.teamPageLoading,
            },
          },
          {
            field: 'patrolType',
            title: '巡视类型',
            element: 'el-select',
            props: {
              placeholder: '请选择巡视类型',
              clearable: true,
              options: this.dict.type.ls_charging_patrol_type,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadTableData();
  },
  methods: {
    async debouncedGroupListSearch(query) {
      if (query !== '') {
        this.groupListLoading = true;
        setTimeout(async () => {
          const [err, res] = await getGroupList({
            groupName: query,
          });

          if (err) return;
          this.groupListLoading = false;
          this.groupList = res.data.map((item) => ({
            label: item.groupName,
            value: item.groupId,
          }));
        }, 200);
      } else {
        this.groupList = [];
      }
    },
    async debouncedTeamPageSearch(query) {
      if (query !== '') {
        this.teamPageLoading = true;
        setTimeout(async () => {
          const [err, res] = await getTeamPage({
            teamName: query,
          });

          if (err) return;
          this.teamPageLoading = false;
          this.teamPageList = res.data.map((item) => ({
            label: item.teamName,
            value: item.teamId,
          }));
        }, 200);
      } else {
        this.teamPageList = [];
      }
    },
    // 加载表格数据
    async loadTableData() {
      this.tableLoading = true;
      try {
        const params = {
          ...this.params,
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize,
        };

        const [err, res] = await getPlanPage(params);
        if (err) return;
        this.tableData = res.data || [];
        this.tablePage.total = res.total || 0;
      } finally {
        this.tableLoading = false;
      }
    },

    // 编辑计划
    async handlePlanEdit(type, row) {
      this.planType = type;
      this.selectPlan = {};

      if (type !== 'create') {
        const [err, res] = await getPlanDetail({
          patrolPlanId: row.patrolPlanId,
        });
        if (err) return;
        this.selectPlan = res.data || {};
      }

      this.$refs.editPlan.dialogVisible = true;
    },

    // 删除计划
    handlePlanDel(row) {
      this.$confirm(
        `确定删除巡视计划：${row?.patrolPlanName || ''}吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        const [err, res] = await deletePlan({
          patrolPlanId: row?.patrolPlanId || '',
        });
        if (err) return;
        this.$message({
          type: 'success',
          message: '删除成功!',
        });
        this.loadTableData();
      });
    },

    // 查看详情
    viewPlanDetail(row) {
      this.$router.push({
        path: '/v2g-charging/intelligence/patrolManagement/patrolPlanDetail',
        query: { patrolPlanId: row.patrolPlanId },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }
  margin: 16px;

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
  }
}
</style>
