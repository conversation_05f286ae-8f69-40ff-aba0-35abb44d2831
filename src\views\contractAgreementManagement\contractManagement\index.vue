<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        @loadData="loadData"
        class="buse-wrap-user"
      >
        <template slot="type">
          <el-cascader
            v-model="params.type"
            :options="typeOptions"
            style="width: 100%"
            clearable
          ></el-cascader>
        </template>
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">合同管理</div>
            <div class="top-button-wrap">
              <el-button type="primary" @click="handleAdd">新增</el-button>
              <el-button type="primary" @click="handleOutput">导出</el-button>
            </div>
          </div>
          <div class="card-head-after"></div>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <!-- <el-button type="primary" plain @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button type="primary" plain @click="handleDownLoad(row)">
            下载
          </el-button>
          <el-button type="danger" plain @click="handleDel(row)">
            删除
          </el-button> -->
          <el-dropdown trigger="click">
            <el-button class="button-border" type="primary" plain>
              操作
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <div @click="handleEdit(row)">编辑</div>
              </el-dropdown-item>
              <el-dropdown-item>
                <div @click="handleDownLoad(row)">下载</div>
              </el-dropdown-item>
              <el-dropdown-item>
                <div @click="handleDel(row)">删除</div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </BuseCrud>
    </div>
  </div>
</template>

<script>
import {
  getContractList,
  getContractDetail,
  deleteContract,
} from '@/api/contractAgreementManagement/contractManagement';

export default {
  dicts: [
    'template_type', // 合同类型(主)
    'cxfw_template_two', // 出行服务
    'llhd_template_two', // 两轮换电合同
    'cddyy_template_two', // 充电代运营合同
    'cwhd_template_two', // 车网互动合同
  ],
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          minWidth: 60,
        },
        {
          field: 'code',
          title: '合同编号',
          minWidth: 120,
        },
        {
          field: 'name',
          title: '合同名称',
          minWidth: 160,
        },
        {
          field: 'typeDesc',
          title: '合同类型',
          minWidth: 160,
          //   formatter: ({ cellValue }) => {
          //     return this.selectDictLabel(
          //       [
          //         { label: '类型一', value: 1 },
          //         { label: '类型二', value: 2 },
          //       ],
          //       cellValue
          //     );
          //   },
        },
        {
          field: 'startDate',
          title: '合同开始日期',
          minWidth: 120,
        },
        {
          field: 'endDate',
          title: '合同结束日期',
          minWidth: 120,
        },
        {
          field: 'updateTime',
          title: '更新时间',
          minWidth: 120,
        },
        {
          title: '操作',
          minWidth: 150,
          align: 'center',
          fixed: 'right',
          slots: {
            default: 'operate',
          },
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        code: '',
        name: '',
        type: [],
        updateTime: [],
      },
      typeOptions: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'code',
            title: '合同编号',
            element: 'el-input',
            props: {
              placeholder: '请输入合同编号',
            },
          },
          {
            field: 'name',
            title: '合同名称',
            element: 'el-input',
            props: {
              placeholder: '请输入合同名称',
            },
          },
          {
            field: 'type',
            title: '合同类型',
            element: 'slot',
            slotName: 'type',
          },
          {
            field: 'updateTime',
            title: '更新时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
              valueFormat: 'yyyy-MM-dd',
              options: [],
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  async mounted() {
    await this.loadData();
    await this.getTypeOptions();
    this.typeOptions = this.dict.type.template_type;
    this.typeOptions.forEach((item) => {
      item.children = [];
      if (item.value == 'cxfw_template_two') {
        item.children = this.dict.type.cxfw_template_two;
      } else if (item.value == 'llhd_template_two') {
        item.children = this.dict.type.llhd_template_two;
      } else if (item.value == 'cddyy_template_two') {
        item.children = this.dict.type.cddyy_template_two;
      } else if (item.value == 'cwhd_template_two') {
        item.children = this.dict.type.cwhd_template_two;
      }
    });
  },
  methods: {
    async loadData() {
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        startDate: '',
        endDate: '',
        type: '',
      };
      if (this.params.updateTime && this.params.updateTime.length > 0) {
        params.startDate = this.params.updateTime[0];
        params.endDate = this.params.updateTime[1];
      }
      if (this.params.type && this.params.type.length > 0) {
        params.type = this.params.type;
      }
      this.loading = true;
      const [err, res] = await getContractList(params);
      this.loading = false;
      if (err) return;
      const { data, total } = res;
      this.tableData = data;
      this.tablePage.total = total;
    },
    async getTypeOptions() {
      this.typeOptions.forEach((item) => {
        console.log('000');

        // console.log('item', item);
        // item.children = [];
        // if (item.value == 'cxfw_template_two') {
        //   item.children = this.dict.cxfw_template_two;
        //   console.log('cxfw_template_two', item);
        // } else if (item.value == 'llhd_template_two') {
        //   item.children = this.dict.llhd_template_two;
        //   console.log('llhd_template_two', item);
        // } else if (item.value == 'cddyy_template_two') {
        //   item.children = this.dict.cddyy_template_two;
        //   console.log('cddyy_template_two', item);
        // }
      });
      console.log('typeOptions', this.typeOptions);
    },
    // 新增
    handleAdd() {
      this.$router.push({
        path: '/v2g-charging/contractAgreementManagement/contractManagement/add',
        // query: query
      });
    },
    // 导出
    handleOutput() {
      this.download(
        '/vehicle-grid-system/contract/export',
        { ...this.params },
        `合同列表.xlsx`
      );
    },
    // 编辑
    async handleEdit(row) {
      //   console.log(row);
      let params = { operateId: row.id };
      const [err, res] = await getContractDetail(params);
      if (err) return;
      //   console.log('合同详情', res);
      this.$router.push({
        path: '/v2g-charging/contractAgreementManagement/contractManagement/add',
        query: res.data,
      });
    },
    // 下载
    handleDownLoad(row) {
      //   console.log(row);
      const link = document.createElement('a');
      link.href = row.attachmentPath;
      link.download = decodeURIComponent(row.attachmentPath.split('/').pop()); // 自动取文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    // 删除
    async handleDel(row) {
      this.$confirm(`确定删除${row.name}吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          let params = { operateId: row.id };
          const [err, res] = await deleteContract(params);
          if (err) return;
          this.$message.success('删除成功');
          await this.loadData();
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  background: #fff;
  padding: 20px;
  border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
}
</style>
