<template>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="630px"
      @close="handleCancel"
    >
        <div class="info-wrap">
            <div class="info-title">所属充电站：</div>
            <div class="info-detail">{{ form.stationName }}</div>
        </div>

        <div class="info-wrap">
            <div class="info-title">充电桩：</div>
            <div class="info-detail">{{ form.stationNo }}</div>
        </div>

        <div class="info-wrap">
            <div class="info-title">额定功率：</div>
            <div class="info-detail">{{ form.ratePower }}</div>
        </div>

        <el-form :model="form" :rules="rules" ref="ruleForm"  label-position="top">
            <el-row :gutter="20">
                <el-col :span="20">
                    <el-form-item
                        label="可控类型："
                        prop="controlType"
                        :label-width="formLabelWidth"
                    >
                        <el-select
                            v-model="form.controlType"
                            placeholder="请选择"
                            style="width: 100%"
                            >
                            <el-option
                                v-for="item in dict.type.ls_charging_adjustable_type"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="20">
                    <el-form-item
                        label="最大下调(削峰)能力："
                        prop="controlType"
                        :label-width="formLabelWidth"
                    >
                    <el-input-number
                        v-model="form.maxDownCapability"
                        :min="0"
                        :precision="2"
                        :step="0.01"
                        :controls="false"
                    ></el-input-number>
                    <div style="margin-left: 6px;">kW</div>
                    </el-form-item>
                </el-col>

                <el-col :span="20">
                    <el-form-item
                        label="最大上调(填谷)能力："
                        prop="controlType"
                        :label-width="formLabelWidth"
                    >
                    <el-input-number
                        v-model="form.maxUpwardCapability"
                        :min="0"
                        :precision="2"
                        :step="0.01"
                        :controls="false"
                    ></el-input-number>
                    <div style="margin-left: 6px;">kW</div>
                    </el-form-item>
                </el-col>
            </el-row>
           

        </el-form>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
    </el-dialog>
  </template>
  <script>  

import {
    editPileControl
} from '@/api/pile/index';


import moment from 'moment';

import _ from 'lodash';

  export default {
    props: {
        dialogTitle:{
            type: String,
            default: '调控配置'
        }
    },
    components: {
  
    },
    dicts: [
        'ls_charging_status', // 是否状态
        'ls_charging_adjustable_type', // 可调控类型
        'ls_charging_station_control', // 站点调节方式
    ],
    data() {
        return {
            dialogVisible: false,
            pileId: '',
            form: {
                stationName: '',
                stationNo: '',
                ratePower: '',
                controlType: '',
                maxDownCapability: '',
                maxUpwardCapability: '',
            },
            rules: {
                controlType: [
                    { required: true, message: '请选择可控类型', trigger: 'blur' },
                ],
                maxDownCapability: [
                    { required: true, message: '请输入最大下调(削峰)能力', trigger: 'blur' },
                ],
                maxUpwardCapability: [
                    { required: true, message: '请输入最大上调(填谷)能力', trigger: 'blur' },
                ],
            },
            formLabelWidth: '120px',

        };
    },
    computed: {},
    mounted() {},
    methods: {

        resetForm() {
            this.pileId = '';

            Object.keys(this.form).forEach((key) => {
                this.form[key] = '';
                
            });
        },

        handleCancel() {
            this.$refs.ruleForm.resetFields();
            this.resetForm();
            console.log(this.form, 'this.form')
            this.dialogVisible = false;
        },



        // 新增按钮防抖        
        handleConfirm: _.debounce(function() {
            this.$refs.ruleForm.validate(async (valid) => {
                if (valid) {
                    console.log(this.form, 'this.form')
                    const {
                        controlType,
                        maxDownCapability,
                        maxUpwardCapability
                    } = this.form;
             
                         const params = {
                            pileId: this.pileId,
                            controlType,
                            maxDownCapability,
                            maxUpwardCapability
                        }

                        console.log('params', params)
                        const [err, res] = await editPileControl(params);
                        if (err) return;
                    
                        this.$message.success('编辑成功');
                        this.dialogVisible = false;
                        this.pileId  = '';
                        this.$emit('loadData');
                    
                }
            });
        }, 300) ,


    },
  };
  </script>
  <style lang="scss" scoped>
::v-deep .el-form-item__content{
    display: flex !important;
}

::v-deep .el-input-number {
    width: 90% !important;
}



.info-wrap {
    display: flex;
    height: 20px;
    margin-bottom: 24px;
    align-items: center;
    .info-title {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #505363;
      }
      .info-detail {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #292B33;
      }
}
  </style>
  