<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <!-- 顶部信息栏 -->
    <div class="device-head">
      <img
        src="@/assets/charge/price-period-title-icon.png"
        class="device-head-icon"
      />
      <div class="device-info-wrap">
        <div class="device-title-wrap">
          <div class="device-title">电价时段编号：{{ applicationId }}</div>
          <div class="device-status">{{ status }}</div>
        </div>
        <div class="device-info-wrap">
          <el-row>
            <el-col :span="8">
              <span class="label">创建人：</span>
              <span class="value">{{ applicant }}</span>
            </el-col>
            <el-col :span="8">
              <span class="label">提交时间：</span>
              <span class="value">{{ applyTime }}</span>
            </el-col>
          </el-row>
        </div>
      </div>
      <el-button type="primary" @click="drawer = true">审核轨迹</el-button>
    </div>

    <!-- 进度条 -->
    <div class="info-card">
      <el-steps :active="activeStep" align-center finish-status="success">
        <el-step title="开票申请"></el-step>
        <el-step title="开票审核"></el-step>
        <el-step title="发票上传"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
    </div>

    <!-- 审核信息区域 -->
    <div class="info-card">
      <div class="audit-info">
        <h3>审核信息</h3>
        <el-form
          :model="form"
          :rules="rules"
          ref="formRef"
          label-width="120px"
          label-position="top"
          v-if="activeStep == 1"
        >
          <el-form-item label="审核结果" prop="auditResult">
            <el-select
              v-model="form.auditResult"
              placeholder="请选择"
              style="width: 30%"
            >
              <el-option label="通过" value="pass"></el-option>
              <el-option label="不通过" value="fail"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="审核意见" prop="auditOpinion">
            <el-input
              type="textarea"
              :rows="4"
              placeholder="请输入审核意见"
              v-model="form.auditOpinion"
            ></el-input>
            <span class="word-limit">{{ form.auditOpinion.length }}/200</span>
          </el-form-item>
        </el-form>
        <el-form
          :model="uploadForm"
          :rules="uploadRules"
          ref="uploadFormRef"
          label-width="120px"
          label-position="top"
          v-if="activeStep == 2"
        >
          <el-form-item label="开票日期" prop="invoiceDate">
            <el-date-picker
              v-model="uploadForm.invoiceDate"
              type="date"
              placeholder="请选择日期"
              style="width: 30%"
            ></el-date-picker>
          </el-form-item>

          <el-form-item>
            <div class="upload-title">
              <span style="color: red">*</span>
              上传文件
            </div>
            <el-upload
              ref="upload"
              class="upload-demo"
              drag
              :limit="1"
              :headers="upload.headers"
              :action="upload.url"
              :disabled="upload.isUploading"
              :auto-upload="false"
              :on-exceed="handleExceed"
              :on-success="handleUploadSuccess"
              :file-list="uploadForm.fileList"
              :before-remove="beforeRemove"
              :on-change="handleChange"
              :data="upload.data"
              multiple
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                点击或将文件拖拽到这里上传
                <br />
                支持：.jpg .jpeg .pdf .doc .docx
              </div>
            </el-upload>
          </el-form-item>

          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              :rows="4"
              placeholder="请输入备注"
              v-model="uploadForm.remark"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="bottom-wrap">
      <el-button @click="() => handleCancel()">取消</el-button>
      <el-button type="primary" @click="() => handleConfirm()">提交</el-button>
    </div>

    <el-drawer :visible.sync="drawer" :with-header="false" :size="825">
      <div class="draw-wrap">
        <div class="draw-card-head">
          <div class="card-head-text">审核轨迹</div>
          <div class="card-head-close" @click="onClickCloseDrawer"></div>
        </div>
        <div class="draw-card-head-after"></div>

        <div class="approval-steps">
          <el-steps direction="vertical" :active="3" space="100px">
            <el-step v-for="(item, index) in examineList" :key="index">
              <!-- 自定义步骤图标 -->
              <template #icon>
                <div :class="`step-icon step-icon-${item.status}`">
                  {{ index + 1 }}
                </div>
              </template>

              <!-- 自定义步骤内容 -->
              <template #title>
                <div class="step-header">
                  <span class="title">{{ item.title }}</span>
                </div>
              </template>

              <template #description>
                <div
                  class="remark-box"
                  v-if="index !== 3 && item.status !== '05'"
                >
                  <div class="remark-icon"></div>
                  <!-- 处理人信息 -->
                  <div v-if="item.name" class="person-info">
                    <div class="person-title">
                      <span>{{ item.name }}</span>
                      <span class="identity">{{ item.identity }}</span>
                      <span>{{ item.department }}</span>
                    </div>

                    <div
                      :class="`person-info-status person-info-status-${item.status} `"
                    >
                      {{ statusObj[item.status] }}
                    </div>

                    <div v-if="item.remark" class="remark">
                      <span>审核意见：</span>
                      <span>{{ item.remark }}</span>
                    </div>
                  </div>

                  <div class="remark-time">
                    {{ item.time }}
                  </div>
                </div>
              </template>
            </el-step>
          </el-steps>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth';

export default {
  data() {
    return {
      applicationId: '2345580182',
      status: '审核中',
      applicant: '张三',
      applyTime: '2024-12-23 17:23:45',
      activeStep: 2, // 当前步骤（从 0 开始）
      auditResult: '',
      auditOpinion: '',
      form: {
        auditResult: '',
        auditOpinion: '',
      },
      rules: {
        auditResult: [
          { required: true, message: '请选择审核结果', trigger: 'change' },
        ],
      },
      uploadForm: {
        invoiceDate: '',
        invoiceFile: null,
        fileList: [],
        remark: '',
      },
      uploadRules: {
        invoiceDate: [
          { required: true, message: '请选择开票日期', trigger: 'change' },
        ],
        invoiceFile: [
          { required: false, message: '请上传发票文件', trigger: 'change' },
        ],
        remark: [{ required: false, message: '请输入备注', trigger: 'blur' }],
      },
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: '结算',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '',
      },
      drawer: false, // 审核轨迹抽屉
      examineList: [
        {
          title: '发起人',
          name: '张三',
          identity: '项目经理',
          department: '研发技术中心',
          status: '01',
          time: '2021-01-01 12:00:00',
        },
        {
          title: '部门初审',
          name: '李四',
          identity: '项目经理1',
          department: '研发技术中心',
          status: '02',
          remark: '描述描述描述内容信息信息',
          time: '2021-01-01 12:00:00',
        },
        {
          title: '部门复审',
          name: '王五',
          identity: '项目经理1',
          department: '研发技术中心',
          status: '04',
          // remark: '描述描述描述内容信息信息',
          time: '2021-01-01 12:00:00',
        },
        {
          title: '流程结束',
          status: '05',
        },
      ], // 审核轨迹列表
      statusObj: {
        '01': '已提交',
        '02': '已通过',
        '03': '已拒绝',
        '04': '处理中',
        '05': '等待中',
      }, // 审核状态对象
    };
  },
  methods: {
    // 关闭抽屉
    onClickCloseDrawer() {
      this.drawer = false;
    },
    // 处理文件上传成功
    handleUploadSuccess(response, file, fileList) {
      console.log('处理文件上传成功');
      this.uploadForm.fileList = fileList;
    },
    // 选择文件
    handleChange(file, fileList) {
      console.log('文件变更', file, fileList);
      this.uploadForm.fileList = fileList;
    },
    // 移除文件前的钩子
    beforeRemove(file, fileList) {
      this.uploadForm.fileList = fileList;
    },
    // 用户选择的文件数量超过 limit 限制
    handleExceed(files, fileList) {
      console.log(files, fileList);
      // 清空已选文件列表
      this.$refs.upload.clearFiles();
      // 手动添加新选择的文件（首个文件）
      this.$refs.upload.handleStart(files[0]);
    },
    // 取消
    handleCancel() {
      this.$router.back();
    },
    // 提交
    handleConfirm() {
      if (this.activeStep == 1) {
        this.$refs.formRef.validate((valid) => {
          if (valid) {
            console.log('提交表单数据:', this.form);
            this.$message({
              type: 'success',
              message: '提交成功!',
            });
          } else {
            console.log('表单验证失败');
            return false;
          }
        });
      } else if (this.activeStep == 2) {
        this.$refs.uploadFormRef.validate((valid) => {
          if (valid) {
            if (this.uploadForm.fileList.length == 0) {
              this.$message.error(`请选择上传文件`);
              return;
            }
            console.log('提交表单数据:', this.uploadForm);
            this.$message({
              type: 'success',
              message: '提交成功!',
            });
          } else {
            console.log('表单验证失败');
            return false;
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .bottom-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
  }
}

.device-head {
  background-color: #fff;

  display: flex;
  height: 112px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  box-sizing: border-box;
  .device-head-icon {
    width: 48px;
    height: 48px;
    margin-right: 24px;
  }
  .device-info-wrap {
    flex: 1;
    .device-title-wrap {
      height: 32px;
      display: flex;
      align-items: center;
      .device-title {
        font-weight: 500;
        font-size: 24px;
        color: #12151a;
      }
      .device-status {
        // width: 50px;
        padding: 0 10px;
        height: 24px;
        border-radius: 10px 0 10px 0;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: center;
        color: #fff;
        background: linear-gradient(321.01deg, #ffb624 8.79%, #ff8d24 100.27%);
        margin-left: 12px;
      }
    }
    .device-info-wrap {
      height: 16px;
      margin-top: 16px;
      font-size: 16px;
      font-weight: 400;
      color: #292b33;
    }
  }
  .device-status-wrap {
    display: flex;
    align-items: center;
    .device-status-item-wrap {
      width: 150px;
      .device-status-item-title {
        font-weight: 400;
        font-size: 14px;
        line-height: 14px;
        color: #505363;
        margin: 0 auto 12px auto;
        text-align: center;
      }
      .device-status {
        width: 86px;
        height: 34px;
        border-radius: 4px;
        display: flex;
        margin: 0 auto;
        align-items: center;
        justify-content: center;
        background-color: #ebf3ff;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #217aff;
      }
      .device-status-success {
        width: 86px;
        height: 34px;
        border-radius: 4px;
        display: flex;
        margin: 0 auto;
        align-items: center;
        justify-content: center;
        background-color: #ebfff1;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #00c864;
      }
    }
    .device-status-split {
      width: 1px;
      height: 36px;
      background-color: #e9ebf0;
    }
  }
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
}

.el-steps {
  margin: 20px 0;
}

::v-deep .el-step__title.is-success {
  color: #217aff;
}

::v-deep .el-step__head.is-success {
  color: #217aff;
  border-color: #217aff;
}

.audit-info {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 20px;
}

.audit-info h3 {
  border-left: 4px solid #409eff;
  padding-left: 10px;
  margin-bottom: 20px;
}

.word-limit {
  position: absolute;
  right: 10px;
  bottom: 10px;
  color: #999;
}

.draw-wrap {
  .draw-card-head {
    // position: relative;
    height: 82px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    // margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 24px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }

    .card-head-close {
      width: 24px;
      height: 24px;
      background-image: url('~@/assets/station/drawer-close.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }

  .draw-card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .card-head {
    height: 18px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-bottom: 32px;
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }
  .approval-steps {
    padding: 20px;
    background: #fff;
    border-radius: 4px;
  }

  :deep(.el-step__head) {
    padding-bottom: 10px;
  }

  :deep(.el-step__title) {
    line-height: 1.5;
    max-width: 600px;
  }
  ::v-deep .el-step__icon {
    border: 0px;
  }

  .step-icon {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .step-icon-01 {
    background-color: #ebf3ff;
    border-radius: 50%;
    color: #217aff;
  }

  .step-icon-02 {
    background-color: #ebf3ff;
    border-radius: 50%;
    color: #217aff;
  }
  .step-icon-03 {
    background-color: #fc1e31;
    border-radius: 50%;
    color: #fff;
  }
  .step-icon-04 {
    background-color: #217aff;
    border-radius: 50%;
    color: #fff;
  }
  .step-icon-05 {
    background-color: #f3f6fc;
    border-radius: 50%;
    color: #818496;
  }
  .step-header {
    display: flex;
    align-items: center;
    //   margin-bottom: 8px;
  }

  .title {
    margin-right: 12px;
    font-weight: 400;
    font-size: 16px;
    color: #12151a;
  }

  .status-tag {
    margin-right: 12px;
  }

  .time {
    color: #999;
    font-size: 12px;
    margin-left: auto;
  }

  .remark-box {
    margin-top: 8px;
    padding: 16px 12px 16px 16px;
    background: #f9f9fb;
    border-radius: 5px;
    display: flex;
    margin-bottom: 32px;
    margin-right: 12px;
    .remark-icon {
      width: 48px;
      height: 48px;
      background-image: url('~@/assets/station/drawer-icon.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 16px;
    }
    .person-info {
      font-weight: 400;
      font-size: 16px;
      line-height: 20px;
      flex: 1;
      .person-title {
        color: #292b33;
        .identity {
          margin: 0 8px;
        }
      }
      .person-info-status {
        font-weight: 400;
        font-size: 16px;
        line-height: 20px;
        margin: 10px 0 0 0;
      }
      .person-info-status-01 {
        color: #00c864;
      }
      .person-info-status-02 {
        color: #00c864;
      }
      .person-info-status-03 {
        color: #fc1e31;
      }
      .person-info-status-04 {
        color: #217aff;
      }

      .remark {
        font-weight: 400;
        font-size: 16px;
        line-height: 20px;
        color: #292b33;
        margin-top: 16px;
      }
    }

    .remark-time {
      font-weight: 400;
      font-size: 16px;
      line-height: 20px;
      color: #292b33;
      margin-top: 15px;
    }
  }

  .remark-label {
    color: #666;
    margin-right: 6px;
  }

  .remark-text {
    color: #999;
  }

  .processing {
    animation: rotating 2s linear infinite;
  }

  @keyframes rotating {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}

.upload-title {
  font-size: 14px;
  color: #606266;
  font-weight: 700;
  line-height: 28px;
  margin-bottom: 10px;
}
</style>
