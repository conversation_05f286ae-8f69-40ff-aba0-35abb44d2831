<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="630px"
    @close="handleCancel"
  >
    <el-form :model="form" :rules="rules" ref="ruleForm" label-position="top">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item
            label="充电包卡退卡人："
            prop="enterpriseId"
            :label-width="formLabelWidth"
          >
            <el-select
              v-model="form.enterpriseId"
              :loading="enterpriseLoading"
              filterable
              remote
              :remote-method="debouncedEnterpriseSearch"
              style="width: 100%"
            >
              <el-option
                v-for="item in enterpriseNameList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="充电卡退款方式："
            prop="refundType"
            :label-width="formLabelWidth"
          >
            <el-select v-model="form.refundType" style="width: 100%">
              <el-option
                v-for="item in this.dict.type.ls_charging_refund_method"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item
            label="充电卡交易金额："
            prop="allAmount"
            :label-width="formLabelWidth"
          >
            <el-input
              v-model="allAmount"
              placeholder="自动计算"
              disabled
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="handleConfirm">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { cardRefundCard, enterpriseList } from '@/api/user/chargeCardManage';

export default {
  props: {
    dialogTitle: {
      type: String,
      default: '退卡',
    },
  },
  components: {},
  dicts: [
    'ls_charging_refund_method', // 是否状态
  ],
  data() {
    return {
      dialogVisible: false,
      form: {
        refundNumber: '',
        enterpriseId: '',
        refundType: '',
      },
      rules: {
        refundNumber: [
          { required: true, message: '请输入退款数量', trigger: 'change' },
        ],
        enterpriseId: [
          { required: true, message: '请选择退款人', trigger: 'change' },
        ],
        refundType: [
          { required: true, message: '请选择退款方式', trigger: 'change' },
        ],
      },
      formLabelWidth: '120px',
      info: {
        cardId: '',
        cardProcessFee: 0,
      },
      enterpriseLoading: false,
      enterpriseNameList: [],
      submitLoading: false,
    };
  },
  computed: {
    allAmount() {
      return this.info.cardProcessFee || 0;
    },
  },
  mounted() {},
  methods: {
    async debouncedEnterpriseSearch(query) {
      if (query !== '') {
        this.enterpriseLoading = true;
        setTimeout(async () => {
          const [err, res] = await enterpriseList({
            enterpriseName: query,
          });
          this.enterpriseLoading = false;
          if (err) return;

          this.enterpriseNameList = res.data.map((item) => ({
            label: item.enterpriseName,
            value: item.enterpriseId,
          }));
        }, 200);
      } else {
        this.enterpriseNameList = [];
      }
    },
    resetForm() {
      Object.keys(this.form).forEach((key) => {
        this.form[key] = '';
      });
    },

    handleCancel() {
      this.$refs.ruleForm.resetFields();
      this.resetForm();
      console.log(this.form, 'this.form');
      this.dialogVisible = false;
    },

    // 新增按钮防抖
    handleConfirm() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          console.log(this.form, 'this.form');
          this.submitLoading = true;
          const params = {
            ...this.form,
            cardId: this.info.cardId || '',
          };
          console.log('params', params);
          const [err, res] = await cardRefundCard(params);
          this.submitLoading = false;
          if (err) return;
          this.$message.success('退卡成功');
          this.dialogVisible = false;
          this.$emit('loadData');
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__content {
  display: flex !important;
}

::v-deep .el-input-number {
  width: 100% !important;
}

.info-bg {
  display: flex;
  margin-bottom: 12px;
  justify-content: space-between;
  .info-price {
    background: #ebf3ff;
    width: 271px;
    height: 104px;
    padding: 24px;
    border-radius: 2px;
    text-align: center;
    font-family: 'PingFang SC';
    font-weight: 400;
    font-size: 16px;
    line-height: 16px;
    color: #292b33;
    .price-value {
      font-family: 'Oswald Regular';
      font-weight: 400;
      font-size: 20px;
      line-height: 24px;
      color: #217aff;
      margin-top: 16px;
    }
  }
}

.price-wrap {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  background-color: #ebf3ff;
  border-radius: 2px;
  margin-bottom: 12px;
  font-weight: 400;
  font-size: 16px;
  padding-left: 16px;
  box-sizing: border-box;
  .price {
    font-family: Oswald Regular;
    color: #217aff;
  }
}
</style>
