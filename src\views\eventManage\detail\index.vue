<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <div class="device-head">
      <img
        src="@/assets/charge/price-period-title-icon.png"
        class="device-head-icon"
      />

      <div class="device-info-wrap">
        <div class="device-title-wrap">
          <div class="device-title">活动名称：周年庆活动</div>
          <!-- <div class="device-status">{{ status }}</div> -->
        </div>
        <div class="device-info-wrap">
          <el-row>
            <el-col :span="8">
              <span class="label">活动ID：</span>
              <span class="value">010310493021</span>
            </el-col>
            <el-col :span="8">
              <span class="label">创建人：</span>
              <span class="value">张三</span>
            </el-col>
            <el-col :span="8">
              <span class="label">创建时间：</span>
              <span class="value">2024-11-23 11:20:02</span>
            </el-col>
          </el-row>
        </div>
      </div>
      <el-button type="primary" @click="drawer = true">审核轨迹</el-button>
    </div>
    <el-form
      :model="baseInfo.form"
      :rules="baseInfo.rules"
      ref="baseInfoForm"
      label-position="top"
    >
      <div class="info-card">
        <el-tabs v-model="detailTab">
          <el-tab-pane label="活动概览" name="overview">
            <div class="card-content">
              <div class="info-card info-card-border">
                <div class="card-head" style="margin-bottom: 8px">
                  <div class="before-icon"></div>
                  <div class="card-head-text">基础信息</div>
                </div>

                <div class="form-wrap">
                  <el-row :gutter="20">
                    <el-col :span="7" style="margin-bottom: 24px">
                      <div style="display: flex">
                        <div class="info-title">展示标题：</div>
                        <div class="info-detail">周年庆活动</div>
                      </div>
                    </el-col>
                    <el-col :span="7" style="margin-bottom: 24px">
                      <div style="display: flex">
                        <div class="info-title">活动类型：</div>
                        <div class="info-detail">定向发券活动</div>
                      </div>
                    </el-col>
                    <el-col :span="10" style="margin-bottom: 24px">
                      <div style="display: flex">
                        <div class="info-title">活动有效时间：</div>
                        <div class="info-detail">
                          2021-10-26 15:10:05 ～ 2021-10-26 15:10:05
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="7" style="margin-bottom: 24px">
                      <div style="display: flex">
                        <div class="info-title">活动承办单位：</div>
                        <div class="info-detail">湖南省电动</div>
                      </div>
                    </el-col>
                    <el-col :span="7" style="margin-bottom: 24px">
                      <div style="display: flex">
                        <div class="info-title">活动区域：</div>
                        <div class="info-detail">湖南省长沙市</div>
                      </div>
                    </el-col>
                    <el-col :span="24" style="margin-bottom: 24px">
                      <div style="display: flex">
                        <div class="info-title">活动描述：</div>
                        <div class="info-detail">
                          周年庆活动信息信息内容信息内容，周年庆活动信息信息内容信息内容周年庆活动信息信息内容信息内容周年庆活动信息信息内容信息内容
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>
              <div class="info-card info-card-border">
                <div class="card-head" style="margin-bottom: 8px">
                  <div class="before-icon"></div>
                  <div class="card-head-text">奖品配置</div>
                </div>
                <div class="form-wrap">
                  <BuseCrud
                    style="margin-bottom: 16px"
                    ref="prizesCrud"
                    :tableColumn="tableColumn"
                    :tableData="tableData"
                    :modalConfig="{ addBtn: false, menu: false }"
                  ></BuseCrud>
                </div>
              </div>
              <div class="info-card info-card-border">
                <div class="card-head" style="margin-bottom: 8px">
                  <div class="before-icon"></div>
                  <div class="card-head-text">活动规则</div>
                </div>
                <el-tabs v-model="rulerTab">
                  <el-tab-pane label="用户管理" name="bussinessRuler">
                    <div class="form-wrap form-wrap-top">
                      <el-row :gutter="20">
                        <el-col :span="24">
                          <el-form-item
                            label="使用范围"
                            prop="scopeOfUse"
                            :label-width="formLabelWidth"
                          >
                            <el-radio-group v-model="baseInfo.form.scopeOfUse">
                              <el-radio
                                v-for="(item, index) in scopeOfUseList"
                                :label="item.value"
                                :key="index"
                              >
                                {{ item.label }}
                              </el-radio>
                            </el-radio-group>
                          </el-form-item>
                          <el-form-item
                            label=""
                            prop="limitations"
                            :label-width="formLabelWidth"
                          >
                            <el-radio-group v-model="baseInfo.form.limitations">
                              <el-radio
                                v-for="(item, index) in limitationsList"
                                :label="item.value"
                                :key="index"
                              >
                                {{ item.label }}
                              </el-radio>
                            </el-radio-group>
                          </el-form-item>
                          <div
                            class="table-wrap"
                            v-if="baseInfo.form.limitations !== '1'"
                          >
                            <BuseCrud
                              ref="stationCrud"
                              :tableColumn="stationTableColumnConfig"
                              :tableData="stationTableData"
                              :modalConfig="modalConfig"
                            >
                              <template slot="defaultHeader">
                                <div>
                                  <div class="card-head">
                                    <div class="top-button-wrap">
                                      <div class="choose-box">
                                        已选择
                                        <span>10</span>
                                        个充电站,
                                        <span>100</span>
                                        个充电桩
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </template>
                            </BuseCrud>
                          </div>
                        </el-col>
                      </el-row>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane label="配置管理" name="userRuler">
                    <div class="form-wrap form-wrap-top">
                      <el-row :gutter="20">
                        <el-col :span="24">
                          <el-form-item
                            label="用户类型"
                            prop="userType"
                            :label-width="formLabelWidth"
                          >
                            <el-radio-group v-model="baseInfo.form.userType">
                              <el-radio
                                v-for="(item, index) in userTypeList"
                                :label="item.value"
                                :key="index"
                              >
                                {{ item.label }}
                              </el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row :gutter="20">
                        <div
                          class="table-wrap"
                          v-if="baseInfo.form.userType === '3'"
                        >
                          <BuseCrud
                            ref="userCrud"
                            :tableColumn="userTableColumn"
                            :tableData="userTableData"
                            :modalConfig="modalConfig"
                          >
                            <template slot="defaultHeader">
                              <div>
                                <div class="card-head">
                                  <div class="top-button-wrap">
                                    <div class="choose-box">
                                      圈选
                                      <span>10</span>
                                      个用户
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </template>
                          </BuseCrud>
                        </div>
                      </el-row>
                      <el-row :gutter="20">
                        <el-col :span="24">
                          <el-form-item
                            label="是否新用户注册"
                            prop="newUserRegister"
                            :label-width="formLabelWidth"
                          >
                            <el-radio-group
                              v-model="baseInfo.form.newUserRegister"
                            >
                              <el-radio
                                v-for="(item, index) in newUserRegisterList"
                                :label="item.value"
                                :key="index"
                              >
                                {{ item.label }}
                              </el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </el-col>
                        <el-col :span="24">
                          <div class="flex-end">
                            <el-form-item
                              label="常用充电区域"
                              prop="chargingArea"
                              :label-width="formLabelWidth"
                            >
                              <el-radio-group
                                v-model="baseInfo.form.chargingArea"
                              >
                                <el-radio
                                  v-for="(item, index) in chargingAreaList"
                                  :label="item.value"
                                  :key="index"
                                >
                                  {{ item.label }}
                                </el-radio>
                              </el-radio-group>
                            </el-form-item>
                            <el-form-item
                              v-if="baseInfo.form.chargingArea === '2'"
                              label=""
                              prop="activeType"
                              :label-width="formLabelWidth"
                            >
                              <el-select
                                style="margin-left: 20px"
                                v-model="baseInfo.form.activeType"
                                placeholder="请选择"
                                multiple
                              >
                                <el-option
                                  v-for="item in activeTypeList"
                                  :key="item.value"
                                  :label="item.label"
                                  :value="item.value"
                                ></el-option>
                              </el-select>
                            </el-form-item>
                          </div>
                        </el-col>
                        <el-col :span="24">
                          <div class="flex-end">
                            <el-form-item
                              label="充电情况"
                              prop="chargingSituation"
                              :label-width="formLabelWidth"
                            >
                              <el-radio-group
                                v-model="baseInfo.form.chargingSituation"
                              >
                                <el-radio
                                  v-for="(item, index) in chargingSituationList"
                                  :label="item.value"
                                  :key="index"
                                >
                                  {{ item.label }}
                                </el-radio>
                              </el-radio-group>
                            </el-form-item>
                            <div
                              class="flex-end"
                              style="margin-left: 10px"
                              v-if="baseInfo.form.chargingSituation !== '1'"
                            >
                              <el-form-item
                                label=""
                                prop="activeType"
                                :label-width="formLabelWidth"
                              >
                                <span style="margin-right: 4px">近</span>
                                <el-input
                                  style="width: 70% !important"
                                  v-model="baseInfo.form.activeType"
                                  placeholder="请输入"
                                ></el-input>
                                <span style="margin-left: 4px">天达到</span>
                              </el-form-item>

                              <el-form-item
                                label=""
                                prop="activeType"
                                :label-width="formLabelWidth"
                              >
                                <el-input
                                  v-model="baseInfo.form.activeType"
                                  placeholder="请输入"
                                ></el-input>
                              </el-form-item>
                              <el-form-item
                                label=""
                                prop="activeType"
                                :label-width="formLabelWidth"
                              >
                                <el-select
                                  style="margin-left: 20px"
                                  v-model="baseInfo.form.activeType"
                                  placeholder="请选择单位"
                                >
                                  <el-option
                                    v-for="item in activeTypeList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                  ></el-option>
                                </el-select>
                              </el-form-item>
                            </div>
                          </div>
                        </el-col>
                      </el-row>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane label="角色管理" name="limitRuler">
                    <div class="form-wrap form-wrap-top">
                      <el-row :gutter="20">
                        <el-col :span="24">
                          <div class="flex-end">
                            <el-form-item
                              label="活动限制"
                              prop="activityLimitation"
                              :label-width="formLabelWidth"
                            >
                              <el-radio-group
                                v-model="baseInfo.form.activityLimitation"
                              >
                                <el-radio
                                  v-for="(
                                    item, index
                                  ) in activityLimitationList"
                                  :label="item.value"
                                  :key="index"
                                >
                                  {{ item.label }}
                                </el-radio>
                              </el-radio-group>
                            </el-form-item>
                          </div>
                        </el-col>
                        <el-col :span="24">
                          <div
                            class="flex-end"
                            style="margin-left: 10px"
                            v-if="baseInfo.form.activityLimitation !== '1'"
                          >
                            <el-form-item
                              label=""
                              prop="activeType"
                              :label-width="formLabelWidth"
                            >
                              <span style="margin-right: 4px">
                                活动期间每个用户
                              </span>
                              <el-select
                                v-model="baseInfo.form.activeType"
                                placeholder="请选择"
                              >
                                <el-option
                                  v-for="item in activeTypeList"
                                  :key="item.value"
                                  :label="item.label"
                                  :value="item.value"
                                ></el-option>
                              </el-select>
                            </el-form-item>

                            <el-form-item
                              label=""
                              prop="activeType"
                              :label-width="formLabelWidth"
                            >
                              <span style="margin: 0 4px 0 10px">限制参与</span>

                              <el-input
                                style="width: 50% !important"
                                v-model="baseInfo.form.activeType"
                                placeholder="请输入"
                              ></el-input>
                              <span style="margin-left: 4px">次</span>
                            </el-form-item>
                          </div>
                        </el-col>
                        <el-col :span="24">
                          <div
                            class="flex-end"
                            style="margin-left: 10px"
                            v-if="baseInfo.form.activityLimitation !== '1'"
                          >
                            <el-form-item
                              label=""
                              prop="activeType"
                              :label-width="formLabelWidth"
                            >
                              <span style="margin-right: 4px">
                                活动期间限制
                              </span>
                              <el-select
                                v-model="baseInfo.form.activeType"
                                placeholder="请选择"
                              >
                                <el-option
                                  v-for="item in activeTypeList"
                                  :key="item.value"
                                  :label="item.label"
                                  :value="item.value"
                                ></el-option>
                              </el-select>
                            </el-form-item>

                            <el-form-item
                              label=""
                              prop="activeType"
                              :label-width="formLabelWidth"
                            >
                              <span style="margin: 0 4px 0 10px">
                                发放优惠券
                              </span>

                              <el-input
                                style="width: 50% !important"
                                v-model="baseInfo.form.activeType"
                                placeholder="请输入"
                              ></el-input>
                              <span style="margin-left: 4px">张</span>
                            </el-form-item>
                          </div>
                        </el-col>
                      </el-row>
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="优惠券发放使用记录" name="usageRecords">
            <div class="card-content">
              <BuseCrud
                ref="usageRecordsCrud"
                :loading="loading"
                :filterOptions="filterOptions"
                :tablePage="tablePage"
                :tableColumn="usageTableColumn"
                :tableData="usageTableData"
                :pagerProps="pagerProps"
                :modalConfig="modalConfig"
                @loadData="loadData"
              >
                <template slot="defaultHeader">
                  <div>
                    <div class="card-head">
                      <div class="top-button-wrap">
                        <el-button
                          type="primary"
                          class="btn-export"
                          @click="handleExport"
                        >
                          导出
                        </el-button>
                      </div>
                    </div>
                  </div>
                </template>
              </BuseCrud>
            </div>
          </el-tab-pane>
          <el-tab-pane label="用户参与记录" name="participationRecord">
            <div class="card-content">
              <BuseCrud
                ref="participationRecordCrud"
                :loading="loading"
                :filterOptions="participationFilterOptions"
                :tablePage="tablePage"
                :tableColumn="participationTableColumn"
                :tableData="participationTableData"
                :pagerProps="pagerProps"
                :modalConfig="modalConfig"
                @loadData="loadData"
              >
                <template slot="defaultHeader">
                  <div>
                    <div class="card-head">
                      <div class="top-button-wrap">
                        <el-button
                          type="primary"
                          class="btn-export"
                          @click="handleExport"
                        >
                          导出
                        </el-button>
                      </div>
                    </div>
                  </div>
                </template>
              </BuseCrud>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-form>
    <el-drawer :visible.sync="drawer" :with-header="false" :size="825">
      <div class="draw-wrap">
        <div class="draw-card-head">
          <div class="card-head-text">审核轨迹</div>
          <div class="card-head-close" @click="onClickCloseDrawer"></div>
        </div>
        <div class="draw-card-head-after"></div>

        <div class="card-head" style="margin-bottom: 8px">
          <div class="before-icon"></div>
          <div class="card-head-text">申请信息</div>
        </div>

        <div class="approval-steps">
          <el-steps direction="vertical" :active="3" space="100px">
            <el-step v-for="(item, index) in examineList" :key="index">
              <!-- 自定义步骤图标 -->
              <template #icon>
                <div :class="`step-icon step-icon-${item.status}`">
                  {{ index + 1 }}
                </div>
              </template>

              <!-- 自定义步骤内容 -->
              <template #title>
                <div class="step-header">
                  <span class="title">{{ item.title }}</span>
                </div>
              </template>

              <template #description>
                <div
                  class="remark-box"
                  v-if="index !== 3 && item.status !== '05'"
                >
                  <div class="remark-icon"></div>
                  <!-- 处理人信息 -->
                  <div v-if="item.name" class="person-info">
                    <div class="person-title">
                      <span>{{ item.name }}</span>
                      <span class="identity">{{ item.identity }}</span>
                      <span>{{ item.department }}</span>
                    </div>

                    <div
                      :class="`person-info-status person-info-status-${item.status} `"
                    >
                      {{ statusObj[item.status] }}
                    </div>

                    <div v-if="item.remark" class="remark">
                      <span>审核意见：</span>
                      <span>{{ item.remark }}</span>
                    </div>
                  </div>

                  <div class="remark-time">
                    {{ item.time }}
                  </div>
                </div>
              </template>
            </el-step>
          </el-steps>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { getElectricPricePeriodDetail } from '@/api/electricPricePeriod/index';
export default {
  components: {},
  dicts: ['ls_charging_contracted_unit'],
  data() {
    return {
      formLabelWidth: '120px',
      chargePeriodId: '', // 时段id
      status: '',
      loading: false,
      baseInfo: {
        form: {
          timePeriodId: '',
          periodName: '',
          suitCityCode: '',
          createUnit: '',
          activeType: '',
          scopeOfUse: '1',
          limitations: '1',
          userType: '1',
          newUserRegister: '1',
          chargingArea: '1',
          chargingSituation: '1',
          activityLimitation: '1',
        },
        rules: {
          activeType: [
            { required: true, message: '请选择活动类型', trigger: 'blur' },
          ],
          periodName: [
            { required: true, message: '请输入活动名称', trigger: 'blur' },
          ],
          suitCityCode: [
            { required: true, message: '请选择活动类型', trigger: 'blur' },
          ],
          createUnit: [
            { required: true, message: '请选择活动区域', trigger: 'blur' },
          ],
        },
      },

      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'type',
          title: '优惠券编码',
          minWidth: 120,
        },
        {
          field: 'startTime',
          title: '优惠券名称',
          minWidth: 120,
        },
        {
          field: 'endTime',
          title: '投放库存',
          minWidth: 120,
        },
      ],
      tableData: [],
      detailTab: 'overview',
      drawer: false, // 审核轨迹抽屉
      examineList: [
        {
          title: '发起人',
          name: '张三',
          identity: '项目经理',
          department: '研发技术中心',
          status: '01',
          time: '2021-01-01 12:00:00',
        },
        {
          title: '部门初审',
          name: '李四',
          identity: '项目经理1',
          department: '研发技术中心',
          status: '02',
          remark: '描述描述描述内容信息信息',
          time: '2021-01-01 12:00:00',
        },
        {
          title: '部门复审',
          name: '王五',
          identity: '项目经理1',
          department: '研发技术中心',
          status: '04',
          // remark: '描述描述描述内容信息信息',
          time: '2021-01-01 12:00:00',
        },
        {
          title: '流程结束',
          status: '05',
        },
      ], // 审核轨迹列表

      statusObj: {
        '01': '已提交',
        '02': '已通过',
        '03': '已拒绝',
        '04': '处理中',
        '05': '等待中',
      }, // 审核状态对象

      statusTagType: {
        '01': 'info',
        '02': 'success',
        '03': 'danger',
        '04': 'warning',
        '05': 'info',
      },
      activeTypeList: [
        { label: '领券活动', value: '1' },
        { label: '定向发券活动', value: '2' },
        { label: '立减活动', value: '3' },
      ],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'periodName',
          title: '优惠券编码',
          minWidth: 150,
        },
        {
          field: 'validMonthSummary',
          title: '优惠券名称',
          minWidth: 150,
        },
        {
          field: 'createBy',
          title: '投放库存',
          minWidth: 120,
        },
      ],
      reductionTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'periodName',
          title: '立减时间段',
          minWidth: 150,
        },
        {
          field: 'validMonthSummary',
          title: '优惠范围',
          minWidth: 150,
        },
        {
          field: 'createBy',
          title: '立减方式',
          minWidth: 120,
        },
      ],
      userTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'periodName',
          title: '用户ID',
          minWidth: 150,
        },
        {
          field: 'validMonthSummary',
          title: '用户昵称',
          minWidth: 150,
        },
        {
          field: 'createBy',
          title: '手机号码',
          minWidth: 120,
        },
      ],
      stationTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'periodName',
          title: '充电站编号',
          minWidth: 150,
        },
        {
          field: 'validMonthSummary',
          title: '充电站名称',
          minWidth: 150,
        },
        {
          field: 'createBy',
          title: '站点类型',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '运营模式',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '资产属性',
          minWidth: 120,
        },
      ],
      pileTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'periodName',
          title: '所属充电站',
          minWidth: 150,
        },
        {
          field: 'validMonthSummary',
          title: '运营模式',
          minWidth: 150,
        },
        {
          field: 'createBy',
          title: '充电桩编号',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '充电桩名称',
          minWidth: 120,
        },
      ],
      institutionTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'periodName',
          title: '产权机构',
          minWidth: 250,
          align: 'center',
        },
      ],
      tableData: [],
      stationTableData: [],
      userTableData: [],
      rulerTab: 'bussinessRuler',
      scopeOfUseList: [
        { label: '按场站', value: '1' },
        { label: '按充电桩', value: '2' },
        { label: '按产权机构', value: '3' },
      ],
      limitationsList: [
        { label: '不限制', value: '1' },
        { label: '指定', value: '2' },
        { label: '批量导入', value: '3' },
      ],
      userTypeList: [
        { label: '个人用户', value: '1' },
        { label: '企业用户', value: '2' },
        { label: '批量导入', value: '3' },
      ],
      newUserRegisterList: [
        { label: '不限制', value: '1' },
        { label: '是', value: '2' },
        { label: '否', value: '3' },
      ],
      chargingAreaList: [
        { label: '不限制', value: '1' },
        { label: '限制', value: '2' },
      ],
      chargingSituationList: [
        { label: '不限制', value: '1' },
        { label: '充电量', value: '2' },
        { label: '充电金额', value: '3' },
      ],
      activityLimitationList: [
        { label: '不限制', value: '1' },
        { label: '限制', value: '2' },
      ],
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      usageTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'periodNo',
          title: '发放流水',
          minWidth: 150,
        },
        {
          field: 'periodName',
          title: '优惠券编码',
          minWidth: 150,
        },
        {
          field: 'validMonthSummary',
          title: '优惠券名称',
          minWidth: 150,
        },
        {
          field: 'suitCityName',
          title: '券码',
          minWidth: 120,
        },
        {
          field: 'createTime',
          title: '券业务类型',
          minWidth: 180,
        },
        {
          field: 'createUnit',
          title: '券基本类型',
          minWidth: 220,
        },
        {
          field: 'createBy',
          title: '用户ID',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '用户手机号',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '发放时间',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '发放状态',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '优惠券状态',
          minWidth: 120,
        },
      ],
      usageTableData: [],

      participationTableColumn: [
        {
          field: 'createBy',
          title: '用户ID',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '用户手机号',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '参与时间',
          minWidth: 120,
        },
      ],
      participationTableData: [],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        periodNo: '',
        periodName: '',
        suitCityCode: '',
        approvalStatus: '',
        monthDate: [],
      },
    };
  },

  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'periodNo',
            title: '发放流水：',
            element: 'el-input',
          },
          {
            field: 'periodName',
            title: '优惠券编码：',
            element: 'el-input',
          },
          {
            field: 'suitCityCode',
            title: '券基本类型：',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.areaList,
            },
          },

          {
            field: 'approvalStatus',
            title: '优惠券状态：',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_audit_type,
            },
          },
          {
            field: 'approvalStatus',
            title: '发放状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_audit_type,
            },
          },
          {
            field: 'periodNo',
            title: '用户ID：',
            element: 'el-input',
          },
          {
            field: 'periodName',
            title: '用户手机号：',
            element: 'el-input',
          },
          {
            field: 'monthDate',
            title: '发放时间：',
            element: 'el-date-picker',
            props: {
              type: 'monthrange',
              options: [],
              valueFormat: 'yyyy-MM',
            },
          },
        ],
        params: this.params,
      };
    },
    participationFilterOptions() {
      return {
        config: [
          {
            field: 'monthDate',
            title: '参与时间：',
            element: 'el-date-picker',
            props: {
              type: 'monthrange',
              options: [],
              valueFormat: 'yyyy-MM',
            },
          },
          {
            field: 'periodNo',
            title: '用户ID：',
            element: 'el-input',
          },
          {
            field: 'periodName',
            title: '用户手机号：',
            element: 'el-input',
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
    tableColumnConfig() {
      return this.baseInfo.form.activeType === '3'
        ? this.reductionTableColumn
        : this.tableColumn;
    },
    stationTableColumnConfig() {
      return this.baseInfo.form.scopeOfUse === '1'
        ? this.stationTableColumn
        : this.baseInfo.form.scopeOfUse === '2'
        ? this.pileTableColumn
        : this.institutionTableColumn;
    },
    chooseNameFilter() {
      return this.baseInfo.form.scopeOfUse === '1'
        ? '充电站选择'
        : this.baseInfo.form.scopeOfUse === '2'
        ? '充电桩选择'
        : '产权机构选择';
    },
  },
  mounted() {
    const chargePeriodId = this.$route.query.chargePeriodId;
    if (chargePeriodId) {
      this.chargePeriodId = chargePeriodId;
      this.getElectricPricePeriodDetail();
    }
  },
  methods: {
    async loadData() {
      const { periodNo, periodName, suitCityCode, approvalStatus, monthDate } =
        this.params;

      let validMonthLeft = '';
      let validMonthRight = '';
      if (monthDate && monthDate.length > 0) {
        validMonthLeft = monthDate[0];
        validMonthRight = monthDate[1];
      }

      const params = {
        periodNo,
        periodName,
        suitCityCode,
        approvalStatus,
        validMonthLeft,
        validMonthRight,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.loading = true;
      const [err, res] = await getElectricPricePeriodList(params);

      this.loading = false;
      if (err) return;

      const { data, total } = res;

      this.tableData = data;
      this.tablePage.total = total;
    },
    handleExport() {},
    // 获取计费详情
    async getElectricPricePeriodDetail() {
      const [err, res] = await getElectricPricePeriodDetail({
        operateId: this.chargePeriodId,
      });
      if (err) return;
    },

    // 关闭抽屉
    onClickCloseDrawer() {
      this.drawer = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.device-head {
  background-color: #fff;

  display: flex;
  height: 112px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  box-sizing: border-box;
  .device-head-icon {
    width: 48px;
    height: 48px;
    margin-right: 24px;
  }
  .device-info-wrap {
    flex: 1;
    .device-title-wrap {
      height: 32px;
      display: flex;
      align-items: center;
      .device-title {
        font-weight: 500;
        font-size: 24px;
        color: #12151a;
      }
      .device-status {
        // width: 50px;
        padding: 0 10px;
        height: 24px;
        border-radius: 10px 0 10px 0;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: center;
        color: #fff;
        background: linear-gradient(321.01deg, #ffb624 8.79%, #ff8d24 100.27%);
        margin-left: 12px;
      }
    }
    .device-info-wrap {
      height: 16px;
      margin-top: 16px;
      font-size: 16px;
      font-weight: 400;
      color: #292b33;
    }
  }
  .device-status-wrap {
    display: flex;
    align-items: center;
    .device-status-item-wrap {
      width: 150px;
      .device-status-item-title {
        font-weight: 400;
        font-size: 14px;
        line-height: 14px;
        color: #505363;
        margin: 0 auto 12px auto;
        text-align: center;
      }
      .device-status {
        width: 86px;
        height: 34px;
        border-radius: 4px;
        display: flex;
        margin: 0 auto;
        align-items: center;
        justify-content: center;
        background-color: #ebf3ff;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #217aff;
      }
      .device-status-success {
        width: 86px;
        height: 34px;
        border-radius: 4px;
        display: flex;
        margin: 0 auto;
        align-items: center;
        justify-content: center;
        background-color: #ebfff1;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #00c864;
      }
    }
    .device-status-split {
      width: 1px;
      height: 36px;
      background-color: #e9ebf0;
    }
  }
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;

  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;

    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
        background-color: #1ab2ff;
        border-color: #1ab2ff;
      }
      ::v-deep .el-button--small {
        font-size: 14px;
      }
      .distribution {
        margin-left: 24px;
        margin-right: 24px;
        display: flex;
        align-items: center;
      }
    }
  }

  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
    padding: 0 16px 0 16px;
    .info-title {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #505363;
    }
    .info-detail {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #292b33;
    }
    .info-amount {
      height: 28px;
      background-color: #fff7e6;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 8px;
      font-weight: 400;
      font-size: 20px;
      color: #fe8921;
      margin-top: -6px;
      margin-right: 4px;
    }
    .info-img {
      width: 140px;
      height: 140px;
    }
  }
  .form-wrap-top {
    margin-top: 16px;
  }
}
.info-card-border {
  border: 1px solid #e9ebf0;
}

::v-deep .bd3001-content {
  padding: 0;
}

.draw-wrap {
  .draw-card-head {
    // position: relative;
    height: 82px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    // margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 24px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }

    .card-head-close {
      width: 24px;
      height: 24px;
      background-image: url('~@/assets/station/drawer-close.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }

  .draw-card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .card-head {
    height: 18px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-bottom: 32px;
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }
  .approval-steps {
    padding: 20px;
    background: #fff;
    border-radius: 4px;
  }

  :deep(.el-step__head) {
    padding-bottom: 10px;
  }

  :deep(.el-step__title) {
    line-height: 1.5;
    max-width: 600px;
  }
  ::v-deep .el-step__icon {
    border: 0px;
  }

  .step-icon {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .step-icon-01 {
    background-color: #ebf3ff;
    border-radius: 50%;
    color: #217aff;
  }

  .step-icon-02 {
    background-color: #ebf3ff;
    border-radius: 50%;
    color: #217aff;
  }
  .step-icon-03 {
    background-color: #fc1e31;
    border-radius: 50%;
    color: #fff;
  }
  .step-icon-04 {
    background-color: #217aff;
    border-radius: 50%;
    color: #fff;
  }
  .step-icon-05 {
    background-color: #f3f6fc;
    border-radius: 50%;
    color: #818496;
  }
  .step-header {
    display: flex;
    align-items: center;
    //   margin-bottom: 8px;
  }

  .title {
    margin-right: 12px;
    font-weight: 400;
    font-size: 16px;
    color: #12151a;
  }

  .status-tag {
    margin-right: 12px;
  }

  .time {
    color: #999;
    font-size: 12px;
    margin-left: auto;
  }

  .remark-box {
    margin-top: 8px;
    padding: 16px 12px 16px 16px;
    background: #f9f9fb;
    border-radius: 5px;
    display: flex;
    margin-bottom: 32px;
    margin-right: 12px;
    .remark-icon {
      width: 48px;
      height: 48px;
      background-image: url('~@/assets/station/drawer-icon.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 16px;
    }
    .person-info {
      font-weight: 400;
      font-size: 16px;
      line-height: 20px;
      flex: 1;
      .person-title {
        color: #292b33;
        .identity {
          margin: 0 8px;
        }
      }
      .person-info-status {
        font-weight: 400;
        font-size: 16px;
        line-height: 20px;
        margin: 10px 0 0 0;
      }
      .person-info-status-01 {
        color: #00c864;
      }
      .person-info-status-02 {
        color: #00c864;
      }
      .person-info-status-03 {
        color: #fc1e31;
      }
      .person-info-status-04 {
        color: #217aff;
      }

      .remark {
        font-weight: 400;
        font-size: 16px;
        line-height: 20px;
        color: #292b33;
        margin-top: 16px;
      }
    }

    .remark-time {
      font-weight: 400;
      font-size: 16px;
      line-height: 20px;
      color: #292b33;
      margin-top: 15px;
    }
  }

  .remark-label {
    color: #666;
    margin-right: 6px;
  }

  .remark-text {
    color: #999;
  }

  .processing {
    animation: rotating 2s linear infinite;
  }

  @keyframes rotating {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}
.card-content {
  padding: 16px;
}
.flex-end {
  display: flex;
  align-items: flex-end;
}
</style>
