<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="630px"
    @close="handleCancel"
  >
    <el-form :model="form" :rules="rules" ref="ruleForm" label-position="top">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item
            label="充电卡购买数量："
            prop="cardSum"
            :label-width="formLabelWidth"
          >
            <el-input-number
              v-model="form.cardSum"
              :min="1"
              :precision="0"
              :step="1"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="充电卡购买人："
            prop="enterpriseId"
            :label-width="formLabelWidth"
          >
            <el-select
              v-model="form.enterpriseId"
              style="width: 100%"
              :loading="enterpriseLoading"
              filterable
              remote
              :remote-method="debouncedEnterpriseSearch"
            >
              <el-option
                v-for="item in enterpriseNameList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="充电卡支付方式："
            prop="paymentMethod"
            :label-width="formLabelWidth"
          >
            <el-select v-model="form.paymentMethod" style="width: 100%">
              <el-option
                v-for="item in this.dict.type.ls_charging_enterprise_payMent"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="充电卡工本费(元/本)："
            prop="cardProcessFee"
            :label-width="formLabelWidth"
          >
            <el-input-number
              v-model="form.cardProcessFee"
              :min="1"
              :precision="0"
              :step="1"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="充电卡交易金额(元)："
            :label-width="formLabelWidth"
          >
            <el-input
              v-model="allAmount"
              placeholder="自动计算"
              disabled
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="handleConfirm">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { cardPurchaseCard, enterpriseList } from '@/api/user/chargeCardManage';
import Decimal from 'decimal.js';
import moment from 'moment';

export default {
  props: {
    dialogTitle: {
      type: String,
      default: '购卡',
    },
  },
  components: {},
  dicts: [
    'ls_charging_enterprise_payMent', // 充电卡购卡-充电卡支付方式
  ],
  data() {
    return {
      dialogVisible: false,
      form: {
        cardSum: '',
        enterpriseId: '',
        paymentMethod: '',
        cardProcessFee: '',
      },
      rules: {
        cardSum: [
          { required: true, message: '请输入充电卡购买数量', trigger: 'blur' },
        ],
        enterpriseId: [
          { required: true, message: '请选择购买人', trigger: 'blur' },
        ],
        paymentMethod: [
          { required: true, message: '请选择支付方式', trigger: 'change' },
        ],
        cardProcessFee: [
          {
            required: true,
            message: '请输入充电卡工本费',
            trigger: 'change',
          },
        ],
      },
      formLabelWidth: '120px',
      submitLoading: false,
      enterpriseLoading: false,
      enterpriseNameList: [],
    };
  },
  computed: {
    allAmount() {
      return new Decimal(this.form.cardSum || 0)
        .times(this.form.cardProcessFee || 0)
        .toFixed(2);
    },
  },
  methods: {
    async debouncedEnterpriseSearch(query) {
      if (query !== '') {
        this.enterpriseLoading = true;
        setTimeout(async () => {
          const [err, res] = await enterpriseList({
            enterpriseName: query,
          });
          this.enterpriseLoading = false;
          if (err) return;

          this.enterpriseNameList = res.data.map((item) => ({
            label: item.enterpriseName,
            value: item.enterpriseId,
          }));
        }, 200);
      } else {
        this.enterpriseNameList = [];
      }
    },
    resetForm() {
      Object.keys(this.form).forEach((key) => {
        this.form[key] = '';
      });
    },

    handleCancel() {
      this.$refs.ruleForm.resetFields();
      this.resetForm();
      console.log(this.form, 'this.form');
      this.dialogVisible = false;
    },

    // 新增按钮防抖
    handleConfirm() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true;
          const params = {
            ...this.form,
          };

          const [err, res] = await cardPurchaseCard(params);
          this.submitLoading = false;
          if (err) return;

          this.$message.success('新增成功');
          this.dialogVisible = false;
          this.$emit('loadData');
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__content {
  display: flex !important;
}

::v-deep .el-input-number {
  width: 100% !important;
}

.info-wrap {
  display: flex;
  height: 20px;
  margin-bottom: 24px;
  align-items: center;
  .info-title {
    font-weight: 400;
    font-size: 16px;
    line-height: 16px;
    color: #505363;
  }
  .info-detail {
    font-weight: 400;
    font-size: 16px;
    line-height: 16px;
    color: #292b33;
  }
}

.price-wrap {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  background-color: #ebf3ff;
  border-radius: 2px;
  margin-bottom: 12px;
  font-weight: 400;
  font-size: 16px;
  padding-left: 16px;
  box-sizing: border-box;
  .price {
    font-family: Oswald Regular;
    color: #217aff;
  }
}
</style>
