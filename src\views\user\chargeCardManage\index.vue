<template>
  <div class="container container-float" style="padding: 0">
    <el-tabs v-model="activeName" @tab-click="handleClickTab">
      <el-tab-pane label="充电卡购卡记录" name="meal">
        <div class="table-wrap">
          <BuseCrud
            ref="meal"
            :loading="mealLoading"
            :filterOptions="mealFilterOptions"
            :tablePage="mealTablePage"
            :tableColumn="mealTableColumn"
            :tableData="mealTableData"
            :modalConfig="modalConfig"
            class="buse-wrap-user"
            @loadData="loadMealData"
          >
            <template slot="defaultHeader">
              <div>
                <div class="card-head">
                  <div class="card-head-text">充电卡购卡记录列表</div>

                  <div class="top-button-wrap">
                    <el-button
                      class="set-btn"
                      type="primary"
                      @click="onClickMealExport()"
                    >
                      <svg-icon iconClass="a-export-black"></svg-icon>
                      导出
                    </el-button>

                    <el-button type="primary" @click="() => handleBuyCard()">
                      <svg-icon iconClass="a-add"></svg-icon>
                      购卡
                    </el-button>
                  </div>
                </div>
              </div>
            </template>
            <template slot="operate" slot-scope="{ row }">
              <el-button
                v-if="row.transactionStatus === '1'"
                class="button-border"
                @click="handleRefundCard(row)"
              >
                退卡
              </el-button>
            </template>
          </BuseCrud>
        </div>
      </el-tab-pane>

      <el-tab-pane label="充电卡退卡记录" name="purchase">
        <div class="table-wrap">
          <BuseCrud
            ref="purchase"
            :loading="purchaseLoading"
            :filterOptions="purchaseFilterOptions"
            :tablePage="purchaseTablePage"
            :tableColumn="purchaseTableColumn"
            :tableData="purchaseTableData"
            :modalConfig="modalConfig"
            class="buse-wrap-user"
            @loadData="loadPurchaseData"
          >
            <template slot="defaultHeader">
              <div>
                <div class="card-head">
                  <div class="card-head-text">充电卡退卡记录列表</div>

                  <div class="top-button-wrap">
                    <el-button
                      class="set-btn"
                      type="primary"
                      @click="onClickPurchaseExport()"
                    >
                      <svg-icon iconClass="a-export-black"></svg-icon>
                      导出
                    </el-button>
                  </div>
                </div>
              </div>
            </template>
          </BuseCrud>
        </div>
      </el-tab-pane>

      <el-tab-pane label="充电卡管理" name="Refund">
        <div class="table-wrap">
          <BuseCrud
            ref="refund"
            :loading="refundLoading"
            :filterOptions="refundFilterOptions"
            :tablePage="refundTablePage"
            :tableColumn="refundTableColumn"
            :tableData="refundTableData"
            :modalConfig="modalConfig"
            class="buse-wrap-user"
            @loadData="loadRefundData"
          >
            <template slot="defaultHeader">
              <div>
                <div class="card-head">
                  <div class="card-head-text">充电卡管理列表</div>

                  <div class="top-button-wrap">
                    <el-button
                      class="set-btn"
                      type="primary"
                      @click="onClickRefundExport()"
                    >
                      <svg-icon iconClass="a-export-black"></svg-icon>
                      导出
                    </el-button>
                  </div>
                </div>
              </div>
            </template>

            <template slot="operate" slot-scope="{ row }">
              <el-button
                v-if="row.cardStatus === '00' && row.cardBindStatus === '01'"
                class="button-border"
                @click="bindUser(row)"
              >
                绑定用户
              </el-button>
              <el-button
                v-if="row.cardStatus === '00' && row.cardBindStatus === '00'"
                class="button-border"
                @click="cardRecharge(row)"
              >
                充值
              </el-button>
              <el-button
                v-if="
                  row.cardStatus === '00' &&
                  row.cardBindStatus === '01' &&
                  Number(row.cardAmount) === 0
                "
                class="button-border"
                @click="handleCardRefund(row)"
              >
                退卡
              </el-button>
              <el-button class="button-border" @click="chargingCardFlow(row)">
                流水
              </el-button>
              <el-button
                v-if="row.cardStatus === '00' && row.cardBindStatus === '00'"
                class="button-border"
                @click="releaseUser(row)"
              >
                解绑用户
              </el-button>
              <el-button
                v-if="row.cardStatus === '05' && row.cardBindStatus === '00'"
                class="button-border"
                @click="cardReplacement(row)"
              >
                补卡
              </el-button>
              <el-button
                v-if="row.cardStatus === '00' && row.cardBindStatus === '00'"
                class="button-border"
                @click="reportLoss(row)"
              >
                挂失
              </el-button>
              <el-button
                v-if="row.cardStatus === '05' && row.cardBindStatus === '00'"
                class="button-border"
                @click="cancelLossReport(row)"
              >
                取消挂失
              </el-button>
              <el-button
                v-if="row.cardStatus === '00' && row.cardBindStatus === '01'"
                class="button-border"
                @click="logOff(row)"
              >
                注销
              </el-button>
            </template>
          </BuseCrud>
        </div>
      </el-tab-pane>

      <el-tab-pane label="充电卡业务记录" name="manage">
        <div class="table-wrap">
          <BuseCrud
            ref="purchase"
            :loading="businessLoading"
            :filterOptions="businessFilterOptions"
            :tablePage="businessTablePage"
            :tableColumn="businessTableColumn"
            :tableData="businessTableData"
            :modalConfig="modalConfig"
            class="buse-wrap-user"
            @loadData="loadBusinessData"
          >
            <template slot="defaultHeader">
              <div>
                <div class="card-head">
                  <div class="card-head-text">充电卡业务记录列表</div>

                  <div class="top-button-wrap">
                    <el-button
                      class="set-btn"
                      type="primary"
                      @click="onClickBusinessExport()"
                    >
                      <svg-icon iconClass="a-export-black"></svg-icon>
                      导出
                    </el-button>
                  </div>
                </div>
              </div>
            </template>
          </BuseCrud>
        </div>
      </el-tab-pane>
    </el-tabs>

    <BuyCardModal @loadData="loadMealData" ref="buyCardModal" />

    <RefundCardModal @loadData="loadMealData" ref="refundCardModal" />

    <CardRefundModal @loadData="loadRefundData" ref="cardRefundModal" />

    <BindUserModal @loadData="loadRefundData" ref="bindUserModal" />

    <CardRechargeModal @loadData="loadRefundData" ref="cardRechargeModal" />

    <CancelLossReportModal
      @loadData="loadRefundData"
      ref="cancelLossReportModal"
    />

    <ReportLoss @loadData="loadRefundData" ref="reportLoss" />

    <CardReplacement @loadData="loadRefundData" ref="cardReplacement" />

    <LogOff @loadData="loadRefundData" ref="logOff" />

    <ChargingCardFlow ref="chargingCardFlow" />
  </div>
</template>

<script>
import {
  queryCardPurchasePage,
  cardReturnPage,
  cardBizTypePage,
  getCardPage,
  enterpriseList,
  queryEnterpriseUser,
  cardRelieve,
} from '@/api/user/chargeCardManage';
import BuyCardModal from './components/buyCardModal.vue';
import RefundCardModal from './components/refundCardModal.vue';
import CardRefundModal from './components/cardRefundModal.vue';
import BindUserModal from './components/bindUserModal.vue';
import CardRechargeModal from './components/cardRechargeModal.vue';
import CancelLossReportModal from './components/cancelLossReportModal.vue';
import ReportLoss from './components/reportLoss.vue';
import CardReplacement from './components/cardReplacement.vue';
import LogOff from './components/logOff.vue';
import ChargingCardFlow from './components/chargingCardFlow.vue';
import { row } from 'mathjs';

export default {
  components: {
    BuyCardModal,
    RefundCardModal,
    CardRefundModal,
    BindUserModal,
    CardRechargeModal,
    CancelLossReportModal,
    ReportLoss,
    CardReplacement,
    LogOff,
    ChargingCardFlow,
  },
  dicts: [
    'ls_charging_card_bind_status', // 绑定用户状态
    'ls_charging_card_opertor_bizType', // 电卡业务记录-业务类型
    'ls_charging_card_payment_method', // 充电卡支付方式
    'ls_charging_card_status', // 充电卡状态
    'ls_charging_enterprise_payMent', // 充电卡购卡-充电卡支付方式
    'ls_charging_refund_method', // 退卡方式
    'ls_charging_transaction_status', // 充电卡-交易状态
  ],
  data() {
    return {
      activeName: 'meal',
      mealLoading: false,
      mealParams: {
        chargeCardOrderNo: '',
        chargeCardSerialNo: '',
        transactionTime: [],
        transactionStatus: '',
        operator: '',
      },
      mealTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      mealTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'chargeCardOrderNo',
          title: '业务订单号',
          minWidth: 150,
        },
        {
          field: 'chargeCardSerialNo',
          title: '交易流水',
          minWidth: 150,
        },
        {
          field: 'cardSum',
          title: '充电卡购买数量',
          minWidth: 180,
        },
        {
          field: 'cardBoundSum',
          title: '充电卡绑定员工数量',
          minWidth: 180,
        },
        {
          field: 'enterpriseName',
          title: '充电卡购卡人',
          minWidth: 150,
        },
        {
          field: 'cardProcessFee',
          title: '充电卡工本费',
          minWidth: 120,
        },
        {
          field: 'paymentMethodName',
          title: '支付方式',
          minWidth: 120,
        },
        {
          field: 'amount',
          title: '交易金额',
          minWidth: 120,
        },
        {
          field: 'transactionTime',
          title: '交易时间',
          minWidth: 180,
        },
        {
          field: 'operator',
          title: '操作人',
          minWidth: 120,
        },
        {
          field: 'transactionStatusName',
          title: '交易状态',
          minWidth: 120,
          fixed: 'right',
          align: 'center',
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 120,
          align: 'center',
          fixed: 'right',
        },
      ],
      mealTableData: [],

      purchaseLoading: false,
      purchaseParams: {
        chargeCardOrderNo: '',
        chargeCardSerialNo: '',
        transactionTime: [],
        transactionStatus: '',
        operator: '',
        cardRefundNo: '',
      },
      purchaseTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      purchaseTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'cardRefundNo',
          title: '退款单号',
          minWidth: 180,
        },
        {
          field: 'chargeCardOrderNo',
          title: '业务订单号',
          minWidth: 150,
        },
        {
          field: 'chargeCardSerialNo',
          title: '交易流水号',
          minWidth: 150,
        },

        {
          field: 'refundableSum',
          title: '充电卡可退数量',
          minWidth: 180,
        },
        {
          field: 'returnedCardsSum',
          title: '充电卡退卡数量',
          minWidth: 200,
        },
        {
          field: 'enterpriseName',
          title: '充电卡退卡企业',
          minWidth: 120,
        },
        {
          field: 'refundMethodName',
          title: '退款方式',
          minWidth: 120,
        },
        {
          field: 'transactionAmount',
          title: '交易金额',
          minWidth: 120,
        },
        {
          field: 'transactionTime',
          title: '交易时间',
          minWidth: 180,
        },

        {
          field: 'transactionStatusName',
          title: '交易状态',
          minWidth: 120,
          fixed: 'right',
        },
        {
          field: 'operator',
          title: '操作人',
          minWidth: 120,
        },
      ],
      purchaseTableData: [],

      refundLoading: false,
      refundParams: {
        cardNo: '',
        cardBindUserId: '',
        cardBindTime: [],
        cardBindStatus: '',
        operator: '',
        enterpriseId: '',
        cardStatus: '',
      },
      refundTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      refundTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'cardNo',
          title: '充电卡ID',
          minWidth: 150,
        },
        {
          field: 'cardAmount',
          title: '充电卡余额（元）',
          minWidth: 150,
        },
        {
          field: 'cardBindStatusName',
          title: '充电卡绑定状态',
          minWidth: 150,
        },
        {
          field: 'cardBindUserName',
          title: '充电卡绑定用户',
          minWidth: 180,
        },
        {
          field: 'cardBindTime',
          title: '充电卡绑定时间',
          minWidth: 180,
        },
        {
          field: 'operator',
          title: '操作人',
          minWidth: 200,
        },
        {
          field: 'cardStatusName',
          title: '充电卡状态',
          minWidth: 120,
          fixed: 'right',
          align: 'center',
        },
        {
          field: 'enterpriseName',
          title: '所属企业',
          minWidth: 120,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 350,
          align: 'center',
          fixed: 'right',
        },
      ],
      refundTableData: [],
      businessLoading: false,
      businessParams: {
        operator: '',
        operateTime: [],
        bizType: '',
      },
      businessTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      businessTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'cardNo',
          title: '充电卡ID',
          minWidth: 150,
        },
        {
          field: 'bizTypeName',
          title: '业务类型',
          minWidth: 120,
        },
        {
          field: 'operateTime',
          title: '操作时间',
          minWidth: 120,
        },
        {
          field: 'operator',
          title: '操作员',
          minWidth: 120,
        },
        {
          field: 'instruction',
          title: '操作说明',
          minWidth: 120,
        },
      ],
      businessTableData: [],
      enterpriseLoading: false,
      enterpriseNameList: [],
      bindUserNameList: [],
      bindUserLoading: false,
    };
  },

  computed: {
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
    mealFilterOptions() {
      return {
        config: [
          {
            field: 'chargeCardOrderNo',
            title: '业务订单号',
            element: 'el-input',
          },
          {
            field: 'chargeCardSerialNo',
            title: '交易流水号',
            element: 'el-input',
          },
          {
            field: 'transactionTime',
            title: '交易时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              placeholder: '开始日期 - 自定日期',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
            },
          },
          {
            field: 'transactionStatus',
            title: '交易状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_transaction_status,
            },
          },
          {
            field: 'operator',
            title: '操作人',
            element: 'el-input',
          },
        ],
        params: this.mealParams,
      };
    },
    purchaseFilterOptions() {
      return {
        config: [
          {
            field: 'chargeCardOrderNo',
            title: '业务订单号',
            element: 'el-input',
          },
          {
            field: 'chargeCardSerialNo',
            title: '交易流水号',
            element: 'el-input',
          },
          {
            field: 'transactionTime',
            title: '交易时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              options: [],
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
              defaultTime: ['00:00:00', '23:59:59'],
            },
          },
          {
            field: 'transactionStatus',
            title: '交易状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_transaction_status,
            },
          },
          {
            field: 'cardRefundNo',
            title: '退款单号',
            element: 'el-input',
          },

          {
            field: 'operator',
            title: '操作人',
            element: 'el-input',
          },
        ],
        params: this.purchaseParams,
      };
    },

    refundFilterOptions() {
      return {
        config: [
          {
            field: 'cardNo',
            title: '充电卡ID',
            element: 'el-input',
          },
          {
            field: 'cardBindUserId',
            title: '绑定用户',
            element: 'el-select',
            props: {
              options: this.bindUserNameList,
              filterable: true,
              remote: true,
              remoteMethod: this.debouncedBindUserSearch,
              loading: this.bindUserLoading,
            },
          },
          {
            field: 'cardBindTime',
            title: '绑定时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              placeholder: '开始日期 - 自定日期',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'cardBindStatus',
            title: '绑定状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_card_bind_status,
            },
          },
          {
            field: 'operator',
            title: '操作人',
            element: 'el-input',
          },
          {
            field: 'enterpriseId',
            title: '所属企业',
            element: 'el-select',
            props: {
              options: this.enterpriseNameList,
              filterable: true,
              remote: true,
              remoteMethod: this.debouncedEnterpriseSearch,
              loading: this.enterpriseLoading,
            },
          },
          {
            field: 'cardStatus',
            title: '充电卡状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_card_status,
            },
          },
        ],
        params: this.refundParams,
      };
    },

    businessFilterOptions() {
      return {
        config: [
          {
            field: 'operateTime',
            title: '操作时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              placeholder: '开始日期 - 自定日期',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
            },
          },
          {
            field: 'operator',
            title: '操作员',
            element: 'el-input',
          },
          {
            field: 'bizType',
            title: '业务状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_card_opertor_bizType,
            },
          },
        ],
        params: this.businessParams,
      };
    },
  },
  mounted() {
    this.loadMealData();
  },
  methods: {
    async debouncedBindUserSearch(query) {
      if (query !== '') {
        this.bindUserLoading = true;
        setTimeout(async () => {
          const [err, res] = await queryEnterpriseUser({
            enterpriseName: query,
          });
          this.bindUserLoading = false;
          if (err) return;

          this.bindUserNameList = res.data.map((item) => ({
            label: item.fullName,
            value: item.userId,
          }));
        }, 200);
      } else {
        this.bindUserNameList = [];
      }
    },
    async debouncedEnterpriseSearch(query) {
      if (query !== '') {
        this.enterpriseLoading = true;
        setTimeout(async () => {
          const [err, res] = await enterpriseList({
            enterpriseName: query,
          });
          this.enterpriseLoading = false;
          if (err) return;

          this.enterpriseNameList = res.data.map((item) => ({
            label: item.enterpriseName,
            value: item.enterpriseId,
          }));
        }, 200);
      } else {
        this.enterpriseNameList = [];
      }
    },
    onClickRefundExport() {
      const params = {
        ...this.refundParams,
      };
      if (params.cardBindTime && params.cardBindTime.length > 0) {
        params.cardBindTimeStart = params.cardBindTime[0];
        params.cardBindTimeEnd = params.cardBindTime[1];
      }

      this.download(
        '/vehicle-charging-admin/card/export',
        {
          ...params,
        },
        `充电卡管理记录.xlsx`
      );
    },
    onClickBusinessExport() {
      const params = {
        ...this.businessParams,
      };
      if (params.operateTime && params.operateTime.length > 0) {
        params.operateTimeStart = params.operateTime[0];
        params.operateTimeEnd = params.operateTime[1];
      }

      this.download(
        '/vehicle-charging-admin/card/bizType/export',
        {
          ...params,
        },
        `充电卡业务记录.xlsx`
      );
    },
    onClickPurchaseExport() {
      const params = {
        ...this.purchaseParams,
      };
      if (params.transactionTime && params.transactionTime.length > 0) {
        params.transactionTimeStart = params.transactionTime[0];
        params.transactionTimeEnd = params.transactionTime[1];
      }

      this.download(
        '/vehicle-charging-admin/card/record/return/export',
        {
          ...params,
        },
        `充电卡退卡记录.xlsx`
      );
    },
    onClickMealExport() {
      const params = {
        ...this.mealParams,
      };
      if (params.transactionTime && params.transactionTime.length > 0) {
        params.transactionTimeStart = params.transactionTime[0];
        params.transactionTimeEnd = params.transactionTime[1];
      }

      this.download(
        '/vehicle-charging-admin/card/record/purchase/export',
        {
          ...params,
        },
        `充电卡购卡记录.xlsx`
      );
    },
    // 充电卡流水
    chargingCardFlow(row) {
      this.$refs.chargingCardFlow.cardId = row?.cardId || '';

      this.$refs.chargingCardFlow.dialogVisible = true;
    },
    // 注销
    logOff(row) {
      this.$refs.logOff.info = {
        cardId: row?.cardId,
        cardNo: row?.cardNo,
        cardAmount: row?.cardAmount,
      };
      this.$refs.logOff.dialogVisible = true;
    },
    // 补卡
    cardReplacement(row) {
      this.$refs.cardReplacement.info = {
        cardId: row?.cardId || '',
        cardNo: row?.cardNo || '',
        cardAmount: row?.cardAmount || '',
      };

      this.$refs.cardReplacement.dialogVisible = true;
    },

    // 挂失
    reportLoss(row) {
      this.$refs.reportLoss.info = {
        cardId: row?.cardId || '',
        enterpriseId: row?.enterpriseId,
        enterpriseName: row?.enterpriseName,
        cardBindUserId: row?.cardBindUserId,
        cardBindUserName: row?.cardBindUserName,
        cardNo: row?.cardNo,
        cardAmount: row?.cardAmount,
      };
      this.$refs.reportLoss.dialogVisible = true;
    },
    // 取消挂失
    cancelLossReport(row) {
      this.$refs.cancelLossReportModal.cardId = row?.cardId || '';
      this.$refs.cancelLossReportModal.dialogVisible = true;
    },

    // 充值
    cardRecharge(row) {
      this.$refs.cardRechargeModal.info = {
        cardId: row?.cardId || '',
        enterpriseId: row?.enterpriseId,
        enterpriseName: row?.enterpriseName,
        cardBindUserId: row?.cardBindUserId,
        cardBindUserName: row?.cardBindUserName,
        cardNo: row?.cardNo,
        instruction: row?.cardAmount,
      };
      this.$refs.cardRechargeModal.dialogVisible = true;
    },

    // 绑定用户
    bindUser(row) {
      this.$refs.bindUserModal.info = {
        cardId: row?.cardId || '',
      };
      this.$refs.bindUserModal.dialogVisible = true;
    },

    // 解绑用户
    releaseUser(row) {
      this.$confirm(`确定解绑用户${row?.cardBindUserName || ''}吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const [err, res] = await cardRelieve({
          cardId: row?.cardId || '',
        });
        if (err) return;
        this.$message({
          type: 'success',
          message: '解绑成功!',
        });
        this.loadRefundData();
      });
    },
    // 退卡
    handleCardRefund(row) {
      this.$refs.cardRefundModal.info = {
        cardId: row?.cardId || '',
        cardProcessFee: row?.cardProcessFee || 0,
      };
      this.$refs.cardRefundModal.dialogVisible = true;
    },
    async handleClickTab({ index }) {
      if (index === '0') {
        this.mealTablePage = {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        };

        this.mealTableData = [];
        this.loadMealData();
      } else if (index === '1') {
        this.purchaseTablePage = {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        };

        this.purchaseTableData = [];
        this.loadPurchaseData();
      } else if (index === '2') {
        this.refundTablePage = {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        };

        this.refundTableData = [];
        this.loadRefundData();
      } else if (index === '3') {
        this.businessTablePage = {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        };

        this.businessTableData = [];
        this.loadBusinessData();
      }
    },

    async loadMealData() {
      const params = {
        ...this.mealParams,
        pageNum: this.mealTablePage.currentPage,
        pageSize: this.mealTablePage.pageSize,
      };
      if (params.transactionTime && params.transactionTime.length > 0) {
        params.transactionTimeStart = params.transactionTime[0];
        params.transactionTimeEnd = params.transactionTime[1];
      }

      this.mealLoading = true;

      const [err, res] = await queryCardPurchasePage(params);

      this.mealLoading = false;

      if (err) return;

      const { data, total } = res;

      this.mealTableData = data;
      this.mealTablePage.total = total;
    },

    // 新增套餐
    handleAddMeal() {
      this.$router.push({
        path: '/v2g-charging/baseInfo/userManage/chargePackageManage/createMeal',
      });
    },

    async loadPurchaseData() {
      const params = {
        ...this.purchaseParams,
        pageNum: this.purchaseTablePage.currentPage,
        pageSize: this.purchaseTablePage.pageSize,
      };
      if (params.transactionTime && params.transactionTime.length > 0) {
        params.transactionTimeStart = params.transactionTime[0];
        params.transactionTimeEnd = params.transactionTime[1];
      }

      this.purchaseLoading = true;

      const [err, res] = await cardReturnPage(params);

      this.purchaseLoading = false;

      if (err) return;

      const { data, total } = res;

      this.purchaseTableData = data;
      this.purchaseTablePage.total = total;
    },

    // 购卡
    handleBuyCard() {
      this.$refs.buyCardModal.dialogVisible = true;
    },

    // 退卡
    handleRefundCard(row) {
      this.$refs.refundCardModal.info = {
        chargeCardId: row?.chargeCardId || '',
      };
      this.$refs.refundCardModal.dialogVisible = true;
      this.$refs.refundCardModal.queryCardRefundCardDetail(
        row?.chargeCardId || ''
      );
    },

    async loadRefundData() {
      const params = {
        ...this.refundParams,
        pageNum: this.refundTablePage.currentPage,
        pageSize: this.refundTablePage.pageSize,
      };
      if (params.cardBindTime && params.cardBindTime.length > 0) {
        params.cardBindTimeStart = params.cardBindTime[0];
        params.cardBindTimeEnd = params.cardBindTime[1];
      }

      this.refundLoading = true;

      const [err, res] = await getCardPage(params);

      this.refundLoading = false;

      if (err) return;

      const { data, total } = res;

      this.refundTableData = data;
      this.refundTablePage.total = total;
    },

    async loadBusinessData() {
      const params = {
        ...this.businessParams,
        pageNum: this.businessTablePage.currentPage,
        pageSize: this.businessTablePage.pageSize,
      };
      if (params.operateTime && params.operateTime.length > 0) {
        params.operateTimeStart = params.operateTime[0];
        params.operateTimeEnd = params.operateTime[1];
      }

      this.businessLoading = true;

      const [err, res] = await cardBizTypePage(params);

      this.businessLoading = false;

      if (err) return;

      const { data, total } = res;

      this.businessTableData = data;
      this.businessTablePage.total = total;
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

::v-deep .el-tabs {
  margin-top: 16px;
  background-color: #f5f6f9;
  .el-tabs__header {
    padding-left: 0;
    display: flex;
    justify-content: center;
    text-align: center;
    margin-bottom: 1px;
    .el-tabs__item {
      padding: 0;
      width: 164px;
      font-size: 18px;
      font-weight: 400;
      background-color: #fff;
    }
    .el-tabs__item.is-active {
      background-color: #1677fe;
      color: #fff;
    }
    .el-tabs__nav-scroll {
      border-radius: 25px;
      border: solid 1px #dfe1e5;
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__nav-wrap::after {
      width: 0;
    }
  }
}

.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }
  margin: 16px;

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
    .set-btn {
      background-color: #ffffff;
      color: #292b33;
      border-color: #dfe1e5;
    }
  }
}

.button-border {
  border: 0.01rem solid #217aff;
  color: #217aff;
  background-color: #fff;
}
</style>
