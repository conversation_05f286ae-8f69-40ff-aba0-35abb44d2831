<template>
    <div>
        <div :class="isEdit?'card-head-edit': 'card-head'" >
                <div class="before-icon"></div>
                <div class="card-head-text">场站建设信息</div>
                <el-radio-group v-model="activeTab"  >
                    <el-radio-button label="transformer">变压器</el-radio-button>
                    <el-radio-button label="monitor">监控设备</el-radio-button>
                    <el-radio-button label="fireFight">消防设备</el-radio-button>
                    <el-radio-button label="ancillary">场站附属设施</el-radio-button>
                    <el-radio-button label="chargePile">充电堆</el-radio-button>

                </el-radio-group>
        </div>

            <div :class="isEdit? '': 'card-body'">
                <!--变压器-->
                <div v-if="activeTab === 'transformer'">
                    <vxe-table
                            border
                            :data="stationTransformerDtoList"
                            :edit-config="{ trigger: 'manual', mode: 'row' }"
                            ref="transformerTable"
                            >
                            <vxe-column type="seq" width="60"></vxe-column>
                            <vxe-column :key="'transformer-size'" field="transformerScale" title="变压器规模(KVA)" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.transformerScale" placeholder="请输入变压器规模(VA)" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.transformerScale }}</span>
                                </template>
                            </vxe-column>
                            <vxe-column :key="'transformer-model'" field="transformerModel" title="变压器型号" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.transformerModel" placeholder="请输入变压器型号" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.transformerModel }}</span>
                                </template>
                            </vxe-column>
                            <vxe-column :key="'transformer-manufacturer'" field="manufacturer" title="变压器厂家" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.manufacturer" placeholder="请输入变压器厂家" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.manufacturer }}</span>
                                </template>
                            </vxe-column>
                            <vxe-column :key="'transformer-quantity'" field="transformerQuantity" title="变压器数量" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.transformerQuantity" placeholder="请输入变压器数量" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.transformerQuantity }}</span>
                                </template>
                            </vxe-column>
                            <vxe-column :key="'transformer-attribute'" field="transformerAttribute" title="变压器属性">
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.transformerAttribute" placeholder="请输入变压器属性" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.transformerAttribute }}</span>
                                </template>
                            </vxe-column>
                            <vxe-column :key="'transformer-manufacture-date'" field="factoryDate" title="出厂日期">
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-date-picker
                                        v-model="row.factoryDate"
                                        type="date"
                                        value-format="yyyy-MM-dd"
                                        placeholder="选择日期"
                                        size="mini">
                                        </el-date-picker>
                                    </template>
                                    <span v-else>{{ row.factoryDate }}</span>
                                </template>
                            </vxe-column>
                            <vxe-column :key="'transformer-maintainer'" field="maintenancePerson" title="维护人" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.maintenancePerson" placeholder="请输入维护人" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.maintenancePerson }}</span>
                                </template>
                            </vxe-column>
                            <vxe-column :key="'transformer-operator'" title="操作"  width="60"  v-if="activeTab === 'transformer' && isEdit">
                                <template #default="{ row }">

                                <div class="delete-icon" @click="deleteRow(row, 'transformer')"></div>

                                </template>
                            </vxe-column>
                        </vxe-table>
                </div>
                    
                <!--监控设备-->
                <div v-if="activeTab === 'monitor'">
                    <vxe-table
                            
                            border
                            :data="stationMonitorDtoList"
                            :edit-config="{ trigger: 'manual', mode: 'row' }"
                            ref="monitorTable"
                            >
                            <vxe-column type="seq" width="60"></vxe-column>
                            <vxe-column :key="'monitor-deviceName'" field="deviceName" title="设备名称">
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.deviceName" placeholder="请输入设备名称" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.deviceName }}</span>
                                </template>
                            </vxe-column>
                            <vxe-column :key="'monitor-deviceLocation'" field="deviceLocation" title="设备位置" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.deviceLocation" placeholder="请输入设备位置" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.deviceLocation }}</span>
                                </template>
                            </vxe-column>
                            <vxe-column :key="'monitor-deviceModel'" field="deviceModel" title="设备型号" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.deviceModel" placeholder="请输入设备型号" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.deviceModel }}</span>
                                </template>
                            </vxe-column>
                            <vxe-column :key="'monitor-deviceType'" field="deviceType" title="设备类型(本地摄像头/远程摄像头)" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.deviceType" placeholder="请输入设备类型" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.deviceType }}</span>
                                </template>
                            </vxe-column>
                            <vxe-column :key="'monitor-iotCardNumber'" field="iotCardNumber" title="对应物联卡卡号" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.iotCardNumber" placeholder="请输入对应物联卡卡号" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.iotCardNumber }}</span>
                                </template>
                            </vxe-column>
                            <vxe-column :key="'monitor-storageCapacity'" field="storageCapacity" title="存储容量" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.storageCapacity" placeholder="请输入存储容量" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.storageCapacity }}</span>
                                </template>
                            </vxe-column>
                            <vxe-column  :key="'monitor-deviceStatus'" field="deviceStatus" title="设备状态">
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.deviceStatus" placeholder="请输入设备状态" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.deviceStatus }}</span>
                                </template>
                            </vxe-column>
                            <vxe-column :key="'monitor-manufacturer'" field="manufacturer" title="生产厂家" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.manufacturer" placeholder="请输入生产厂家" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.manufacturer }}</span>
                                </template>
                            </vxe-column>
                            <vxe-column :key="'monitor-productionDate'" field="productionDate" title="生产日期">
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-date-picker
                                        v-model="row.productionDate"
                                        type="date"
                                        value-format="yyyy-MM-dd"
                                        placeholder="选择日期"
                                        size="mini">
                                        </el-date-picker>
                                    </template>
                                    <span v-else>{{ row.productionDate }}</span>
                                </template>
                            </vxe-column>
                            <vxe-column :key="'monitor-isBroadbandAccess'" field="isBroadbandAccess" title="是否宽带接入" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.isBroadbandAccess" placeholder="请输入是否宽带接入" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.isBroadbandAccess }}</span>
                                </template>
                            </vxe-column>
                            <vxe-column :key="'monitor-accessMethod'" field="accessMethod" title="接入方式" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.accessMethod" placeholder="请输入接入方式" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.accessMethod }}</span>
                                </template>
                            </vxe-column>
                            <vxe-column :key="'monitor-accessPlatform'" field="accessPlatform" title="接入平台" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.accessPlatform" placeholder="请输入接入平台" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.accessPlatform }}</span>
                                </template>
                            </vxe-column>
                            <vxe-column :key="'monitor-maintenancePerson'" field="maintenancePerson" title="维护人" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.maintenancePerson" placeholder="请输入维护人" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.maintenancePerson }}</span>
                                </template>
                            </vxe-column>
                            <vxe-column :key="'monitor-operator'" title="操作"  width="60"  v-if="activeTab === 'monitor'&& isEdit">
                                <template #default="{ row }">
                                    <div class="delete-icon" @click="deleteRow(row, 'monitor')"></div>
                                </template>
                            </vxe-column>
                        </vxe-table>
                </div>

                
                <!--消防设备-->
                <div v-if="activeTab === 'fireFight'">
                    <vxe-table
                        border
                        :data="stationFirefightDtoList"
                        :edit-config="{ trigger: 'manual', mode: 'row' }"
                        ref="fireFightTable"
                    >
                            <vxe-column type="seq" width="60"></vxe-column>
                            <vxe-column :key="'fireFight-equipmentName'" field="equipmentName" title="消防器材名称" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.equipmentName" placeholder="请输入消防器材名称" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.equipmentName }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'fireFight-equipmentModel'"  field="equipmentModel" title="消防器材型号">
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.equipmentModel" placeholder="请输入消防器材型号"></el-input>
                                    </template>
                                    <span v-else>{{ row.equipmentModel }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'fireFight-equipmentCapacity'" field="equipmentCapacity" title="消防器材容量" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.equipmentCapacity" placeholder="请输入消防器材容量" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.equipmentCapacity }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'fireFight-equipmentQuantity'" field="equipmentQuantity" title="消防器材数量" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.equipmentQuantity" placeholder="请输入消防器材数量" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.equipmentQuantity }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'fireFight-manufacturer'" field="manufacturer" title="生产厂家" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.manufacturer" placeholder="请输入生产厂家" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.manufacturer }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'fireFight-productionDate'" field="productionDate" title="生产日期" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-date-picker
                                        v-model="row.productionDate"
                                        type="date"
                                        value-format="yyyy-MM-dd"
                                        placeholder="选择日期"
                                        size="mini">
                                        </el-date-picker>
                                    </template>
                                    <span v-else>{{ row.productionDate }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'fireFight-lastInspectionDate'" field="lastInspectionDate" title="最近检验时间" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-date-picker
                                        v-model="row.lastInspectionDate"
                                        type="date"
                                        value-format="yyyy-MM-dd"
                                        placeholder="选择日期"
                                        size="mini">
                                        </el-date-picker>
                                    </template>
                                    <span v-else>{{ row.lastInspectionDate }}</span>
                                    
                                </template>
                            </vxe-column>

                            <vxe-column :key="'fireFight-lifespanYears'" field="lifespanYears" title="寿命周期(年)" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.lifespanYears" placeholder="请输入寿命周期(年)" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.lifespanYears }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'fireFight-maintenancePerson'" field="maintenancePerson" title="维护人" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.maintenancePerson" placeholder="请输入维护人" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.maintenancePerson }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'fireFight-operator'" title="操作"  width="60"  v-if="activeTab === 'fireFight'&& isEdit">
                                <template #default="{ row }">
                                    <div class="delete-icon" @click="deleteRow(row, 'fireFight')"></div>
                                </template>
                            </vxe-column>
                    </vxe-table>
                </div>

                <!--场站附属设施-->
                <div v-if="activeTab === 'ancillary'">
                    <vxe-table
                        border
                        :data="stationSubsidiaryDtoList"
                        :edit-config="{ trigger: 'manual', mode: 'row' }"
                        ref="ancillaryTable"
                    >
                            <vxe-column type="seq" width="60"></vxe-column>
                        
                            <vxe-column :key="'ancillary-deviceName'" field="deviceName" title="设备名称" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.deviceName" placeholder="请输入设备名称" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.deviceName }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'ancillary-deviceQuantity'" field="deviceQuantity" title="设备数量" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.deviceQuantity" placeholder="请输入设备数量" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.deviceQuantity }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'ancillary-deviceUnit'" field="deviceUnit" title="设备单位" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.deviceUnit" placeholder="请输入设备单位" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.deviceUnit }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'ancillary-deviceModel'" field="deviceModel" title="设备型号" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.deviceModel" placeholder="请输入设备型号" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.deviceModel }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'ancillary-deviceLocation'" field="deviceLocation" title="设备位置" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.deviceLocation" placeholder="请输入设备位置" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.deviceLocation }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'ancillary-deviceType'" field="deviceType" title="设备类型" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.deviceType" placeholder="请输入设备类型" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.deviceType }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'ancillary-iotCardNumber'" field="iotCardNumber" title="设备物联卡号" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.iotCardNumber" placeholder="请输入设备物联卡号" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.iotCardNumber }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'ancillary-manufacturer'" field="manufacturer" title="生产厂家" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.manufacturer" placeholder="请输入生产厂家" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.manufacturer }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'ancillary-productionDate'" field="productionDate" title="生产日期" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-date-picker
                                        v-model="row.productionDate"
                                        type="date"
                                        value-format="yyyy-MM-dd"
                                        placeholder="选择日期"
                                        size="mini">
                                        </el-date-picker>
                                    </template>
                                    <span v-else>{{ row.productionDate }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'ancillary-commissioningDate'" field="commissioningDate" title="投运日期" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-date-picker
                                        v-model="row.commissioningDate"
                                        type="date"
                                        value-format="yyyy-MM-dd"
                                        placeholder="选择日期"
                                        size="mini">
                                        </el-date-picker>
                                    </template>
                                    <span v-else>{{ row.commissioningDate }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'ancillary-isNetworkAccessed'" field="isNetworkAccessed" title="是否接入网络" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.isNetworkAccessed" placeholder="请输入是否接入网络" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.isNetworkAccessed }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'ancillary-networkAccessMethod'" field="networkAccessMethod" title="接入网络方式" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.networkAccessMethod" placeholder="请输入接入网络方式" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.networkAccessMethod }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'ancillary-remarks'" field="remarks" title="备注" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.remarks" placeholder="请输入备注" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.remarks }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'ancillary-maintenancePerson'" field="maintenancePerson" title="维护人" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.maintenancePerson" placeholder="请输入维护人" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.maintenancePerson }}</span>
                                </template>
                            </vxe-column>


                            <vxe-column :key="'ancillary-operator'" title="操作"  width="60"  v-if="activeTab === 'ancillary'&& isEdit">
                                <template #default="{ row }">
                                    <div class="delete-icon" @click="deleteRow(row, 'ancillary')"></div>
                                </template>
                            </vxe-column>
                    </vxe-table>
                </div>

                <!--充电堆-->
                <div v-if="activeTab === 'chargePile'">
                    <vxe-table
                        border
                        :data="chargingStacksDtoList"
                        :edit-config="{ trigger: 'manual', mode: 'row' }"
                        ref="ancillaryTable"
                    >
                            <vxe-column type="seq" width="60"></vxe-column>

                            <vxe-column :key="'chargePile-chargerPileId'" field="chargerPileId" title="充电堆编号" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.chargerPileId" placeholder="请输入充电堆编号" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.chargerPileId }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'chargePile-chargerPileModel'" field="chargerPileModel" title="充电堆型号" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.chargerPileModel" placeholder="请输入充电堆型号" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.chargerPileModel }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'chargePile-chargerPilePower'" field="chargerPilePower" title="充电堆功率(kw)" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.chargerPilePower" placeholder="请输入充电堆功率(kw)" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.chargerPilePower }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'chargePile-pileGunRatio'" field="pileGunRatio" title="充电堆堆桩枪比" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.pileGunRatio" placeholder="请输入充电堆堆桩枪比" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.pileGunRatio }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'chargePile-manufacturer'" field="manufacturer" title="生产厂家" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.manufacturer" placeholder="请输入生产厂家" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.manufacturer }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'chargePile-productionDate'" field="productionDate" title="生产日期" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-date-picker
                                        v-model="row.productionDate"
                                        type="date"
                                        value-format="yyyy-MM-dd"
                                        placeholder="选择日期"
                                        size="mini">
                                        </el-date-picker>
                                    </template>
                                    <span v-else>{{ row.productionDate }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'chargePile-maintenancePerson'" field="maintenancePerson" title="维护人" >
                                <template #default="{ row }">
                                    <template v-if="isEdit">
                                        <el-input v-model="row.maintenancePerson" placeholder="请输入维护人" clearable></el-input>
                                    </template>
                                    <span v-else>{{ row.maintenancePerson }}</span>
                                </template>
                            </vxe-column>

                            <vxe-column :key="'chargePile-operator'" title="操作"  width="60"  v-if="activeTab === 'chargePile'&& isEdit">
                                <template #default="{ row }">
                                    <div class="delete-icon" @click="deleteRow(row, 'chargePile')"></div>
                                </template>
                            </vxe-column>
                    </vxe-table>
                </div>
            </div>
           



        <div v-if="isEdit" class="add-wrap" @click="addRow">+ 添加</div>
    </div>
   
</template>
<script>  

  export default {
    props: {
        isEdit: {
            type: Boolean,
            default: true
        },
        data: {
            type: Object,
            default: () => ({})
        }
    },
    components: {
        
    },
    data() {
        return {
            activeTab: 'transformer',
            // editingRow: null, // 当前正在编辑的行
            stationTransformerDtoList: [],
            stationMonitorDtoList:[],
            stationFirefightDtoList: [],
            stationSubsidiaryDtoList: [],
            chargingStacksDtoList: [],
        };
    },
    watch: {
        stationTransformerDtoList: {
            deep: true,
            handler(newVal) {
                this.$emit('stationTransformerDtoListChange', newVal)
            }
        },
        stationMonitorDtoList: {
            deep: true,
            handler(newVal) {
                this.$emit('stationMonitorDtoListChange', newVal)
            }
        },
        stationFirefightDtoList: {
            deep: true,
            handler(newVal) {
                this.$emit('stationFirefightDtoListChange', newVal)
            }
        },
        stationSubsidiaryDtoList: {
            deep: true,
            handler(newVal) {
                this.$emit('stationSubsidiaryDtoListChange', newVal)
            }
        },
        chargingStacksDtoList: {
            deep: true,
            handler(newVal) {
                this.$emit('chargingStacksDtoListChange', newVal)
            }
        },
        data: {
            deep: true,
            handler(newVal) {
                console.log('newVal', newVal)
                const {
                    stationTransformerDtoList,
                    stationMonitorDtoList,
                    stationFirefightDtoList,
                    stationSubsidiaryDtoList,
                    chargingStacksDtoList
                } = newVal;
                if (stationTransformerDtoList) {
                    this.stationTransformerDtoList = stationTransformerDtoList;
                } else {
                    this.stationTransformerDtoList = [];
                }

                if (stationMonitorDtoList) {
                    this.stationMonitorDtoList = stationMonitorDtoList;
                } else {
                    this.stationMonitorDtoList = [];
                }

                if (stationFirefightDtoList) {
                    this.stationFirefightDtoList = stationFirefightDtoList;
                } else {
                    this.stationFirefightDtoList = [];
                }

                if (stationSubsidiaryDtoList) {
                    this.stationSubsidiaryDtoList = stationSubsidiaryDtoList;
                } else {
                    this.stationSubsidiaryDtoList = [];
                }

                if (chargingStacksDtoList) {
                    this.chargingStacksDtoList = chargingStacksDtoList;
                } else {
                    this.chargingStacksDtoList = [];
                }
            }
        }
    },
    computed: {},
    mounted() {

        const {
                    stationTransformerDtoList,
                    stationMonitorDtoList,
                    stationFirefightDtoList,
                    stationSubsidiaryDtoList,
                    chargingStacksDtoList
                } = this.data;
                if (stationTransformerDtoList) {
                    this.stationTransformerDtoList = stationTransformerDtoList;
                } else {
                    this.stationTransformerDtoList = [];
                }

                if (stationMonitorDtoList) {
                    this.stationMonitorDtoList = stationMonitorDtoList;
                } else {
                    this.stationMonitorDtoList = [];
                }

                if (stationFirefightDtoList) {
                    this.stationFirefightDtoList = stationFirefightDtoList;
                } else {
                    this.stationFirefightDtoList = [];
                }

                if (stationSubsidiaryDtoList) {
                    this.stationSubsidiaryDtoList = stationSubsidiaryDtoList;
                } else {
                    this.stationSubsidiaryDtoList = [];
                }

                if (chargingStacksDtoList) {
                    this.chargingStacksDtoList = chargingStacksDtoList;
                } else {
                    this.chargingStacksDtoList = [];
                }
    },
    methods: {
        // 切换 Tab
        handleTabClick(tab) {
            this.activeTab = tab.name;
            // this.editingRow = null; // 切换 Tab 时清空编辑状态
        },
        // 新增一行
        addRow() {
            if (this.activeTab === 'transformer') {
                this.stationTransformerDtoList.push({
                    transformerScale: '',
                    transformerModel: '',
                    manufacturer: '',
                    transformerQuantity: '',
                    transformerAttribute: '',
                    factoryDate: '',
                    maintenancePerson: '',
                });
            } else if (this.activeTab === 'monitor') {
                this.stationMonitorDtoList.push({
                    deviceName: '',
                    deviceLocation: '',
                    deviceModel: '',
                    deviceType: '',
                    iotCardNumber: '',
                    storageCapacity: '',
                    deviceStatus: '',
                    manufacturer: '',
                    productionDate: '',
                    isBroadbandAccess: '',
                    accessMethod: '',
                    accessPlatform: '',
                    maintenancePerson: '',
                });
            } else if (this.activeTab === 'fireFight') {
                this.stationFirefightDtoList.push({
                    equipmentName: '',
                    equipmentModel: '',
                    equipmentCapacity: '',
                    equipmentQuantity: '',
                    manufacturer: '',
                    productionDate: '',
                    lastInspectionDate: '',
                    lifespanYears: '',
                    maintenancePerson: ''
                });
            } else if (this.activeTab === 'ancillary') {
                this.stationSubsidiaryDtoList.push({
                    deviceName: '',
                    deviceQuantity: '',
                    deviceUnit: "",
                    deviceModel: "",
                    deviceLocation: "",
                    deviceType: "",
                    iotCardNumber: "",
                    manufacturer: "",
                    productionDate: "",
                    commissioningDate: "",
                    isNetworkAccessed: "",
                    networkAccessMethod: "",
                    remarks: "",
                    maintenancePerson: ""
                })
            } else if (this.activeTab === 'chargePile') {
                this.chargingStacksDtoList.push({
                    chargerPileId: "",
                    chargerPileModel: "",
                    chargerPilePower: "",
                    pileGunRatio: "",
                    manufacturer: "",
                    productionDate: "",
                    maintenancePerson: ""
                })
            }
        },
        // 删除行
        deleteRow(row, tableRef) {
            if (this.activeTab === 'transformer') {
                const index = this.stationTransformerDtoList.findIndex(item => item === row);
                if (index !== -1) {
                    this.stationTransformerDtoList.splice(index, 1); // 从数据源中删除
                }
            } else if (this.activeTab === 'monitor') {
                const index = this.stationMonitorDtoList.findIndex(item => item === row);
                if (index !== -1) {
                    this.stationMonitorDtoList.splice(index, 1); // 从数据源中删除
                }
            } else  if (this.activeTab === 'fireFight') {
                const index = this.stationFirefightDtoList.findIndex(item => item === row)
                if (index !== -1) {
                    this.stationFirefightDtoList.splice(index, 1)
                }
            } else if (this.activeTab === 'ancillary') {
                const index = this.stationSubsidiaryDtoList.findIndex(item => item === row)
                if (index !== -1) {
                    this.stationSubsidiaryDtoList.splice(index, 1)
                }
            }    else if (this.activeTab === 'chargePile') {
                const index = this.chargingStacksDtoList.findIndex(item => item === row)
                if (index !== -1) {
                    this.chargingStacksDtoList.splice(index, 1)
                }
            }
        },
        
    },
  };
  </script>
  
  <style lang="scss" scoped>
 .card-head {
    height: 56px;
    padding: 0 ;
    display: flex;
    align-items: center;
    background: linear-gradient(180deg, #E9F2FF 0%, #FFFFFF 100%);
    padding-right: 16px;
    .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
    }
    .card-head-text {
        flex:1;
        font-weight: 500;
        font-size: 16px;
        color: #12151A;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
          background-color: #1ab2ff;
          border-color: #1ab2ff;
        }
        ::v-deep .el-button--small {
          font-size: 14px;
        }
      .distribution {
          margin-left: 24px;
          margin-right: 24px;
          display: flex;
          align-items: center;
        }
    }
  }

  .card-head-edit {
    height: 56px;
    padding: 0 ;
    display: flex;
    align-items: center;
    
    .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
    }
    .card-head-text {
        flex:1;
        font-weight: 500;
        font-size: 16px;
        color: #12151A;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
          background-color: #1ab2ff;
          border-color: #1ab2ff;
        }
        ::v-deep .el-button--small {
          font-size: 14px;
        }
      .distribution {
          margin-left: 24px;
          margin-right: 24px;
          display: flex;
          align-items: center;
        }
    }
  }
  .card-body {
    padding: 0 16px 0 16px;
  }

  .add-wrap {
    width: 100%;
    height: 34px;
    border: 1px  solid #DFE1E5  ;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 400;
    color: #217AFF;
    margin-top: 12px;
  }
  .delete-icon {
        width: 13px;
        height: 13px;
        background-image: url('~@/assets/station/station-build-delete.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
</style>
  