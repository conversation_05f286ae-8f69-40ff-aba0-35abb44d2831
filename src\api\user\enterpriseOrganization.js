import request from '@/utils/request';

const baseUrl = '/vehicle-charging-admin';

/**
 * @typedef {Object} NextLevelDepartmentRequest
 * @property {string} parentDepartmentId - 父级机构id
 */

/**
 * @typedef {Object} DepartmentItem
 * @property {string} departmentId - 部门id
 * @property {string} name - 部门名称
 * @property {string} address - 部门地址
 * @property {string} level - 部门层级
 * @property {string} status - 状态（ENABLE：启用；DISABLE：禁用）
 * @property {number} usingNum - 在用人员数量
 * @property {number} unUsedNum - 停用人员数量
 * @property {string} adminName - 管理员名称
 * @property {string} adminMobile - 管理员手机号
 * @property {string} adminEmail - 管理员邮箱
 * @property {string} remark - 备注
 * @property {string} enterpriseId - 企业id
 * @property {string} parentDepartmentId - 父级机构id
 */

/**
 * @typedef {Object} NextLevelDepartmentResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {Array<DepartmentItem>} data - 返回数据
 * @property {number} total - 总条数
 */
// 下一级机构查询
export function nextLevelDepartment(data) {
  return request({
    url: baseUrl + '/enterpriseDepartment/nextLevelDepartment',
    method: 'post',
    data,
  });
}

/**
 * @typedef {Object} QueryPageRequest
 * @property {number} [pageNum=1] - 当前页码，默认为1
 * @property {number} [pageSize=10] - 每页显示条数，默认为10
 * @property {string} [name] - 机构名称
 * @property {string} [departmentId] - 机构编号
 * @property {string} [status] - 机构状态（ENABLE：启用；DISABLE：禁用）
 * @property {string} [parentDepartmentId] - 父级机构id
 */

/**
 * @typedef {Object} QueryPageResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {Array<DepartmentItem>} data - 返回数据
 * @property {number} total - 总条数
 */
// 企业机构分页查询
export function queryPage(data) {
  return request({
    url: baseUrl + '/enterpriseDepartment/queryPage',
    method: 'post',
    data,
  });
}

/**
 * @typedef {Object} ExportRequest
 * @property {number} [pageNum=1] - 当前页码，默认为1
 * @property {number} [pageSize=10] - 每页显示条数，默认为10
 * @property {string} [name] - 机构名称
 * @property {string} [departmentId] - 机构编号
 * @property {string} [status] - 机构状态（ENABLE：启用；DISABLE：禁用）
 * @property {string} [parentDepartmentId] - 父级机构id
 */
// 企业机构导出
export function exportDepartment(data) {
  return request({
    url: baseUrl + '/enterpriseDepartment/export',
    method: 'post',
    data,
  });
}

/**
 * @typedef {Object} BatchModifyStatusRequest
 * @property {Array<string>} departmentIds - 企业机构id集合
 * @property {string} type - 状态（ENABLE 启用 DISABLE 禁用）
 */

/**
 * @typedef {Object} BatchModifyStatusResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {string} data - 返回数据
 * @property {number} total - 总条数
 */
// 企业机构状态批量修改
export function batchModifyStatus(data) {
  return request({
    url: baseUrl + '/enterpriseDepartment/batchModifyStatus',
    method: 'post',
    data,
  });
}

/**
 * @typedef {Object} QueryDetailRequest
 * @property {string} departmentId - 部门id
 */

/**
 * @typedef {Object} QueryDetailResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {DepartmentItem} data - 返回数据
 * @property {number} total - 总条数
 */
// 企业用户明细
export function queryDetail(data) {
  return request({
    url: baseUrl + '/enterpriseDepartment/queryDetail',
    method: 'post',
    data,
  });
}

/**
 * @typedef {Object} DeleteRequest
 * @property {string} departmentId - 部门id
 */

/**
 * @typedef {Object} DeleteResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {string} data - 返回数据
 * @property {number} total - 总条数
 */
// 企业部门删除
export function delDepartment(data) {
  return request({
    url: baseUrl + '/enterpriseDepartment/del',
    method: 'post',
    data,
  });
}

/**
 * @typedef {Object} AddDepartmentRequest
 * @property {string} name - 部门名称
 * @property {string} address - 部门地址
 * @property {string} level - 部门层级（2：一级部门；3：二级部门）
 * @property {string} status - 机构状态（ENABLE:启用；DISABLE:禁用）
 * @property {string} adminName - 管理员名称
 * @property {string} adminMobile - 管理员联系方式
 * @property {string} adminEmail - 管理员邮箱
 * @property {string} [remark] - 备注
 * @property {string} enterpriseId - 企业id
 * @property {string} parentDepartmentId - 父级机构id
 */

/**
 * @typedef {Object} AddDepartmentResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {string} data - 返回数据
 * @property {number} total - 总条数
 */
// 企业部门新增
export function addDepartment(data) {
  return request({
    url: baseUrl + '/enterpriseDepartment/add',
    method: 'post',
    data,
  });
}

/**
 * @typedef {Object} EditDepartmentRequest
 * @property {string} departmentId - 部门id
 * @property {string} name - 部门名称
 * @property {string} address - 部门地址
 * @property {string} adminName - 管理员名称
 * @property {string} adminMobile - 管理员联系方式
 * @property {string} adminEmail - 管理员邮箱
 * @property {string} [remark] - 备注
 */

/**
 * @typedef {Object} EditDepartmentResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {string} data - 返回数据
 * @property {number} total - 总条数
 */
// 企业部门编辑
export function editDepartment(data) {
  return request({
    url: baseUrl + '/enterpriseDepartment/edit',
    method: 'post',
    data,
  });
}

// 企业客户不分页查询
export function enterpriseList(data) {
  return request({
    url: baseUrl + '/enterprise/enterpriseList',
    method: 'post',
    data,
  });
}
