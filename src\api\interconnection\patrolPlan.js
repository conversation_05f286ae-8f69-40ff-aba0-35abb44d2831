/**
 * @file 巡视计划管理API
 */

import request from '@/utils/request';

const baseUrl = '/vehicle-charging-admin';

/**
 * @typedef {Object} PlanPageRequest
 * @property {number} [pageNum=1] - 当前页码，默认为1
 * @property {number} [pageSize=10] - 每页显示条数，默认为10
 * @property {string} [patrolPlanId] - 巡查计划id
 * @property {string} [patrolPlanName] - 巡查计划名称
 * @property {string} [groupId] - 分组id
 * @property {string} [maintenanceTeamId] - 运维班组id
 * @property {number} [patrolType] - 巡视类型
 */

/**
 * @typedef {Object} PlanInfo
 * @property {string} patrolPlanId - 巡查计划id
 * @property {string} patrolPlanName - 巡查计划名称
 * @property {string} groupId - 分组id
 * @property {string} groupName - 分组名称
 * @property {string} maintenanceTeamId - 运维班组id
 * @property {string} maintenanceTeamName - 运维班组名称
 * @property {number} patrolType - 巡视类型（0例行巡视 1特殊巡视）
 * @property {number} patrolFrequenciesType - 巡视频次类型（0按月 1按周 2按天数）
 * @property {number} patrolFrequenciesTime - 巡视次数
 * @property {string} patrolContentIds - 巡视内容ID（逗号分隔，如"id1,id2"）最多10个
 * @property {number} patrolDay - 巡视时效(天)
 * @property {number} status - 状态（0启用 1停用）
 */

/**
 * @typedef {Object} ApiResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {Array<PlanInfo>|PlanInfo|string} data - 返回数据
 * @property {number} total - 总条数
 */

/**
 * 巡视计划分页查询
 * @param {PlanPageRequest} params - 查询参数
 * @returns {Promise<ApiResponse>} 返回包含巡视计划列表的Promise
 */
export function getPlanPage(params) {
  return request({
    url: baseUrl + '/patrol/plan/page',
    method: 'post',
    data: params,
  });
}

/**
 * 巡视计划新增
 * @param {PlanInfo} data - 巡视计划数据
 * @returns {Promise<ApiResponse>} 返回创建结果的Promise
 */
export function createPlan(data) {
  return request({
    url: baseUrl + '/patrol/plan/add',
    method: 'post',
    data: data,
  });
}

/**
 * 巡视计划编辑
 * @param {PlanInfo} data - 巡视计划数据
 * @returns {Promise<ApiResponse>} 返回更新结果的Promise
 */
export function updatePlan(data) {
  return request({
    url: baseUrl + '/patrol/plan/update',
    method: 'post',
    data: data,
  });
}

/**
 * 巡视计划删除
 * @param {Object} params - 删除参数
 * @param {string} params.patrolPlanId - 巡查计划id
 * @returns {Promise<ApiResponse>} 返回删除结果的Promise
 */
export function deletePlan(params) {
  return request({
    url: baseUrl + '/patrol/plan/delete',
    method: 'post',
    data: params,
  });
}

/**
 * 巡视计划详情
 * @param {Object} params - 查询参数
 * @param {string} params.patrolPlanId - 巡查计划id
 * @returns {Promise<ApiResponse>} 返回计划详情数据的Promise
 */
export function getPlanDetail(params) {
  return request({
    url: baseUrl + '/patrol/plan/info',
    method: 'post',
    data: params,
  });
}

/**
 * 站点分组查询
 * @param {Object} params - 查询参数
 */
export function getGroupList(params) {
  return request({
    url: baseUrl + '/ops/stationGroup/groupList',
    method: 'post',
    data: params,
  });
}

/**
 * 班组管理分页查询
 * @param {Object} params - 查询参数
 */
export function getTeamPage(params) {
  return request({
    url: baseUrl + '/ops/team/page',
    method: 'post',
    data: params,
  });
}
