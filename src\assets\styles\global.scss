.container {
  position: relative;
  padding: 16px;
  background-color: #f4f6f9;
  height: calc(100% - 40px);
  overflow-y: auto;
}
.container-float {
  // display: flex;
  // flex-direction: row;
}
.container-region {
  width: 100%;
  display: flex;
  // display: -webkit-box;
}
.container-right {
  margin-left: 316px;
}
.container-info {
  flex: 1;
  min-width: 0;
}
.bd3001-flex {
  display: flex;
}
.bd3001-auto-filters-container,
.bd3001-content {
  max-width: 100%;
  flex: 1;
  background-color: #fff;
  border-radius: 6px;
  padding: 16px;
}
.bd3001-auto-filters-container {
  margin-bottom: 16px;
}
.bd3001-page-wrapper-container {
  .vxe-table--render-default.border--none .vxe-table--header-wrapper {
    background-color: transparent;
    border-bottom: 1px solid #e9ebf0;
  }
  .vxe-header--row {
    background: linear-gradient(
        180deg,
        rgba(0, 149, 255, 0.5) 0%,
        rgba(87, 152, 255, 0) 100%
      ),
      #f5faff;
    background-repeat: no-repeat;
  }
  .vxe-table .vxe-table--header-wrapper .vxe-cell--title {
    color: #21252e;
    font-size: 14px;
    line-height: 18px;
    display: inline-block;
  }
  .vxe-table--render-default {
    color: #292b33;
  }
  .vxe-table--render-default .vxe-body--row.row--stripe {
    background-color: #f5faff;
  }

  .vxe-pager {
    .vxe-pager--jump-next,
    .vxe-pager--jump-prev,
    .vxe-pager--next-btn,
    .vxe-pager--num-btn,
    .vxe-pager--prev-btn {
      border-radius: 0;
    }
    .vxe-pager--sizes .vxe-input {
      height: 30px;
      line-height: 30px;
      .vxe-input--inner {
        border-radius: 0;
      }
    }
  }
}

.region-select {
  line-height: 26px;
  .vue-treeselect__control {
    height: 26px;
  }
  .vue-treeselect__placeholder,
  .vue-treeselect__single-value {
    line-height: 26px;
  }
}
.bd3001-auto-filters-container {
  .el-form-item__label {
    font-size: 15px;
    color: rgba(18, 21, 26, 1);
    font-weight: 400;
  }
  .el-form--label-top .el-form-item__label {
    padding-bottom: 0;
  }
  .el-input__inner,
  .vue-treeselect__control {
    border-radius: 2px;
  }
  .el-input--mini {
    font-size: 14px;
  }
  .el-form-item--mini.el-form-item {
    margin-bottom: 12px;
  }
}
.el-button--mini {
  font-size: 14px !important;
}
.el-dialog__header {
  height: 80px;
  background-color: #217aff;
  .el-dialog__title {
    font-family: PingFang SC;
    font-size: 24px;
    font-weight: 500;
    line-height: 40px;
    color: #fff;
    padding-left: 12px;
  }
  .el-dialog__close {
    color: #fff;
    font-size: 24px;
  }
  .el-dialog__headerbtn:focus .el-dialog__close, .el-dialog__headerbtn:hover .el-dialog__close {
    color: #fff;
  }
}
.el-tabs {
  background-color: #fff;
  border-radius: 5px;
  .el-tabs__header {
    height: auto;
    padding-left: 16px;
    font-family: PingFang SC;
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 0;
  }
}
.el-tabs__content {
  .bd3001-content {
    padding-top: 0;
  }
}
.header {
  .label {
    color: #505363;
  }
  .value {
    color: #292b33;
  }
}
.bd3001-header {
  justify-content: flex-start !important;
}

.vxe-header--row {
  background: linear-gradient(
      180deg,
      rgba(0, 149, 255, 0.5) 0%,
      rgba(87, 152, 255, 0) 100%
    ),
    #f5faff;
  background-repeat: no-repeat;
}
.vxe-table .vxe-table--header-wrapper .vxe-cell--title {
  color: #21252e;
  font-size: 14px;
  line-height: 18px;
  display: inline-block;
}
.vxe-table .vxe-table--body-wrapper {
  .row--current {
    background-color: #EBF3FF !important;
  }
}


.page-head-tab {
   .el-tabs__header {
    height: auto;
    padding: 8px 16px;
  }
  .el-tabs__item {
    font-size: 18px;
  }
}

// 覆盖主应用默认样式
//默认loading 样式更改
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.6);
  overflow: hidden;

  .el-loading-spinner {
    height: 80px;
    width: 90px;
    left: calc((100% - 90px) / 2);
    top: 40%;
    background: url("~@/assets/images/loading.gif") no-repeat center/100%;
    .circular {
      display: none;
    }
  }
}

.el-dialog__wrapper .el-dialog {
  top: 0;
}

.el-tabs .el-tabs__content .el-tab-pane {
  padding: 0;
  height: auto;
}

.el-button.button-border {
  border: 1px solid #217AFF;
  color: #217AFF;
  &:hover {
    border-color: #5798FF;
    color: #5798FF;
  }
  &:active {
    border-color: #1A60D9;
    color: #1A60D9;
  }
}
.buse-wrap {
  .bd3001-content {
    height: calc(100vh - 250px);
    .bd3001-table {
      height: calc(100% - 56px);
    }
  }
}
.buse-wrap2 {
  .bd3001-content {
    height: calc(100vh - 320px);
    .bd3001-table {
      height: calc(100% - 56px);
    }
  }
}

.buse-wrap-organization {
  .bd3001-content {
    min-height: calc(100vh - 300px);
    .bd3001-table {
      height: calc(100% - 56px);
    }
  }
}

.buse-wrap-station {
  .bd3001-content {
    min-height: calc(100vh - 350px);
    .bd3001-table {
      height: calc(100% - 56px);
    }
  }
}


.buse-wrap-user {
  .bd3001-content {
    min-height: calc(100vh - 380px);
    .bd3001-table {
      height: calc(100% - 56px);
    }
  }
}

.buse-wrap-account {
  .bd3001-content {
    min-height: calc(100vh - 440px);
    .bd3001-table {
      height: calc(100% - 56px);
    }
  }
}