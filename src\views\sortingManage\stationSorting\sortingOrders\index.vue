<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
         class="buse-wrap-station"
        @loadData="loadData"
        :tableOn="{
          'checkbox-change': handleCheckboxChange,
          'checkbox-all': handleCheckboxChange,
        }"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <!-- <div class="card-head-text">清分订单管理</div> -->
            <div class="tabs">
              <div
                v-for="(item, index) in activeTab"
                :key="index"
                class="tab-item"
                :class="{ active: selectedItem === item.value }"
                @click="selectTab(item.value)"
              >
                {{ item.label }}
              </div>
            </div>
            <div class="top-button-wrap">
              <el-button type="primary" @click="handleVehicleConfiguration">
                内部车辆配置
              </el-button>
              <el-button type="primary" @click="handleInput">导入</el-button>
              <el-button type="primary" @click="handleOutput">导出</el-button>
            </div>
          </div>
          <div class="card-head-after"></div>
        </template>
      </BuseCrud>
    </div>
    <!-- 导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="60%"
      height="398px"
      append-to-body
      @close="handleCancel"
    >
      <div>模板下载：</div>
      <div class="box link-box">
        <el-link
          type="primary"
          :underline="false"
          style="font-size: 16px; vertical-align: baseline"
          @click="downloadTemplate"
        >
          导入模板.xlsx
        </el-link>
      </div>
      <div>上传文件：</div>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :auto-upload="false"
        :data="upload.data"
        :on-exceed="handleExceed"
        :http-request="customUpload"
      >
        <div class="box el-upload__text">
          <img
            class="upload-icon"
            src="@/assets/icons/responseAndRegulation/upload.png"
          />
          选择要导入上传的文件
        </div>
      </el-upload>
      <div v-if="upload.title == '内部车辆配置'">
        <el-form :model="form" ref="form" label-position="top">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="备注" prop="goodsCode">
                <el-input
                  v-model="form.remark"
                  placeholder="请输入备注"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth';

export default {
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableColumn: [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
        },
        { type: 'seq', title: '序号', minWidth: 60 },
        { field: 'tradeNo', title: '交易流水号', minWidth: 150 },
        { field: 'clearingNo', title: '清分编号', minWidth: 140 },
        { field: 'clearingTime', title: '清分时间', minWidth: 150 },
        { field: 'tradeUnit', title: '交易单位', minWidth: 130 },
        { field: 'tradeOrgCode', title: '交易单位组织机构编码', minWidth: 200 },
        { field: 'assetType', title: '资产属性', minWidth: 120 },
        { field: 'stationName', title: '充电站名称', minWidth: 150 },
        { field: 'city', title: '地市', minWidth: 120 },
        { field: 'stationCode', title: '充电站编号', minWidth: 140 },
        { field: 'pileCode', title: '充电桩编号', minWidth: 140 },
        { field: 'orderNo', title: '订单编号', minWidth: 150 },
        { field: 'orderStatus', title: '订单状态', minWidth: 120 },
        { field: 'orderSource', title: '下单方式', minWidth: 120 },
        { field: 'orderCreateTime', title: '订单下单时间', minWidth: 150 },
        { field: 'chargeStartTime', title: '充电开始时间', minWidth: 150 },
        { field: 'chargeEndTime', title: '充电结束时间', minWidth: 150 },
        { field: 'endReason', title: '充电结束原因', minWidth: 160 },
        { field: 'payTime', title: '支付时间', minWidth: 150 },
        { field: 'chargeEnergy', title: '充电电量（KWH）', minWidth: 160 },
        { field: 'electricityFee', title: '充电电费（元）', minWidth: 140 },
        { field: 'serviceFee', title: '充电服务费（元）', minWidth: 150 },
        { field: 'totalAmount', title: '充电总金额（元）', minWidth: 150 },
        { field: 'discountAmount', title: '优惠金额（元）', minWidth: 140 },
        {
          field: 'afterDiscountAmount',
          title: '优惠后交易金额（元）',
          minWidth: 180,
        },
        {
          field: 'headquartersDiscountFee',
          title: '总部营销优惠金额（元）',
          minWidth: 200,
        },
        {
          field: 'headquartersServiceDiscount',
          title: '总部营销优惠服务费（元）',
          minWidth: 220,
        },
        {
          field: 'receiptDiscountAmount',
          title: '收款单位营销优惠金额（元）',
          minWidth: 240,
        },
        {
          field: 'receiptElectricityDiscount',
          title: '收款单位营销优惠电费（元）',
          minWidth: 240,
        },
        {
          field: 'receiptServiceDiscount',
          title: '收款单位营销优惠服务费（元）',
          minWidth: 260,
        },
        { field: 'clearingEnergy', title: '清分电量（KWH）', minWidth: 160 },
        {
          field: 'clearingElectricityFee',
          title: '清分充电电费（元）',
          minWidth: 180,
        },
        {
          field: 'clearingServiceFee',
          title: '清分充电服务费（元）',
          minWidth: 200,
        },
        {
          field: 'clearingTotalAmount',
          title: '清分充电总金额（元）',
          minWidth: 200,
        },
        { field: 'vin', title: 'VIN码', minWidth: 160 },
        { field: 'cardNo', title: '卡号', minWidth: 140 },
        { field: 'phone', title: '手机号', minWidth: 130 },
        { field: 'enterpriseName', title: '企业名称', minWidth: 150 },
        { field: 'operationMode', title: '运营模式', minWidth: 140 },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      // 检索条件参数
      params: {
        chargingStation: '', // 充电站
        clearingNo: '', // 清分编号
        tradeUnit: '', // 交易单位
        assetType: '', // 资产属性
        orderNo: '', // 订单编号
        clearingTimeRange: [], // 清分时间范围
        orderCreateTimeRange: [], // 订单下单时间范围
      },
      // 下拉选项数据
      tradeUnitOptions: [
        { label: '城市公共', value: '1' },
        { label: '单位内部', value: '2' },
        { label: '高速', value: '3' },
        { label: '公交', value: '4' },
        { label: '全口径', value: '5' },
      ],
      assetTypeOptions: [
        { label: '合资', value: '1' },
        { label: '省电动', value: '2' },
        { label: '三产', value: '3' },
        { label: '车网及建设', value: '4' },
        { label: '主业', value: '5' },
        { label: '其他', value: '6' },
      ],
      selectList: [],
      activeTab: [
        {
          label: '普通清分订单',
          value: '1',
        },
        {
          label: '车电包清分订单',
          value: '2',
        },
      ],
      selectedItem: '1',
      // 导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '',
        //   '/vehicle-charging-admin/insp/soc/import',
        data: {
          requireId: '',
        },
      },
      form: {
        remark: '',
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'chargingStation',
            title: '充电站',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'clearingNo',
            title: '清分编号',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'tradeUnit',
            title: '交易单位',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.tradeUnitOptions,
            },
          },
          {
            field: 'assetType',
            title: '资产属性',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.assetTypeOptions,
            },
          },
          {
            field: 'orderNo',
            title: '订单编号',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'clearingTimeRange',
            title: '清分时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
          {
            field: 'orderCreateTimeRange',
            title: '订单下单时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
          {
            field: 'city',
            title: '城市',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    loadData(page = this.tablePage) {
      // TODO: 请求接口加载数据
      console.log('加载数据', page);
      this.loading = true;
      setTimeout(() => {
        this.tableData = [{ name: 1 }, { name: 2 }];
        this.tablePage.total = 0;
        this.loading = false;
      }, 500);
    },
    // 内部车辆配置
    handleVehicleConfiguration() {
      this.upload.title = '内部车辆配置';
      this.upload.open = true;
    },
    // 导入
    handleInput() {
      this.upload.title = '导入';
      this.upload.open = true;
    },
    // 导入关闭
    handleCancel() {
      this.upload.open = false;
      this.$refs.upload.clearFiles();
    },
    // 下载模板
    downloadTemplate() {
      //   this.download(
      //     '/vehicle-charging-admin/insp/soc/import/template',
      //     {},
      //     `场站SOC导入模板.xlsx`
      //   );
      if (this.upload.title == '导入') {
      } else if (this.upload.title == '内部车辆配置') {
      }
    },
    // 用户选择的文件数量超过 limit 限制
    handleExceed(files, fileList) {
      console.log(files, fileList);
      // 清空已选文件列表
      this.$refs.upload.clearFiles();
      // 手动添加新选择的文件（首个文件）
      this.$refs.upload.handleStart(files[0]);
    },
    async customUpload({ action, file, data }) {
      if (this.upload.title == '导入') {
      } else if (this.upload.title == '内部车辆配置') {
      }
      //   this.upload.isUploading = true;
      //   this.upload.open = false;
      //   const formData = new FormData();
      //   formData.append('file', file);
      //   try {
      //     const [err, res] = await uploadClearingResult(formData);
      //     if (err) return;
      //     if (res.success) {
      //       this.$message.success('导入成功');
      //       this.handleCancel();
      //       this.loadData();
      //     } else {
      //       this.$message.error(res.subMsg || '导入失败');
      //     }
      //   } finally {
      //     this.upload.isUploading = false;
      //   }
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 导出
    handleOutput() {},
    // 表格勾选
    handleCheckboxChange({ records }) {
      console.log(records);
      this.selectList = records;
    },
    // 切换tab
    selectTab(value) {
      this.selectedItem = value;
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  // background: #fff;
  // padding: 20px;
  // border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }

  ::v-deep .bd3001-header {
    display: block;
  }

  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px;
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1);
      }
    }

    .tabs {
      display: flex;
      gap: 5rpx;
      .tab-item {
        cursor: pointer;
        font-size: 18px;
        color: #606266; /* 默认颜色 */
        transition: color 0.3s; /* 平滑过渡 */
        padding: 0 16px;
        margin: 0 6px;
      }

      .tab-item.active {
        color: #409eff; /* 选中时的颜色 */
        border-bottom: 2px solid #409eff; /* 选中时的下划线 */
        padding-bottom: 4px; /* 调整下划线的位置 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
}
</style>
