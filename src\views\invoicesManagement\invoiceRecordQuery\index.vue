<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        class="buse-wrap-station"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">开票记录查询</div>
            <div class="top-button-wrap">
              <el-button
                type="primary"
                icon="el-icon-download"
                @click="handleOutput"
              >
                导出
              </el-button>
            </div>
          </div>
          <div class="card-head-after"></div>
        </template>
        <template slot="orderCount" slot-scope="{ row }">
          <a @click="handleDetail(row)" style="color: blue">
            {{ row.orderCount }}
          </a>
        </template>
        <template slot="invType" slot-scope="{ row }">
          <div v-if="row.invType == '1'" class="red-invType">红票</div>
          <div v-if="row.invType == '0'" class="blue-invType">蓝票</div>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <!-- <div class="menu-box">
            <el-button type="primary" plain @click="handlePreview(row)">
              预览
            </el-button>
            <el-button
              type="primary"
              plain
              @click="handleRed(row)"
              v-if="row.invType == '0'"
            >
              冲红
            </el-button>
            <el-button type="primary" plain @click="handleReissue(row)">
              开具发票
            </el-button>
            <el-button type="primary" plain @click="handleReissue(row)">
              重新开具
            </el-button>
          </div> -->
          <el-dropdown trigger="click">
            <el-button class="button-border" type="primary" plain>
              操作
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <div @click="handlePreview(row)">预览</div>
              </el-dropdown-item>
              <el-dropdown-item
                v-if="row.invType == '0' && row.openStatus == '1'"
              >
                <div @click="handleRed(row)">冲红</div>
              </el-dropdown-item>
              <el-dropdown-item v-else-if="row.openStatus == '0'">
                <div @click="handleReissue(row)">重新开具</div>
              </el-dropdown-item>
              <el-dropdown-item v-else>
                <div @click="handleReissue(row)">开具发票</div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </BuseCrud>
    </div>
    <el-dialog
      :title="redData.title"
      :visible.sync="redData.open"
      width="760px"
      height="398px"
      append-to-body
      @close="handleCancel"
    >
      <el-form size="small" :model="redData" :rules="rules" ref="redData">
        <el-form-item
          label="冲红原因"
          prop="remark"
          :label-width="formLabelWidth"
        >
          <el-input
            v-model="redData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入冲红原因"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="previewData.title"
      :visible.sync="previewData.open"
      width="80%"
      append-to-body
    >
      <img :src="previewData.imgUrl" style="width: 100%" />
    </el-dialog>
  </div>
</template>

<script>
import {
  queryInvoiceRecordPage,
  makeOutBlueInvoice,
  makeOutRedInvoice,
} from '@/api/invoicesManagement/invoiceRecordQuery';

export default {
  dicts: [
    'ls_inv_category', // 发票种类
    'ls_inv_type', // 开票状态
  ],
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          minWidth: 60,
        },
        {
          field: 'createTime',
          title: '开票日期',
          minWidth: 140,
        },
        {
          field: 'invCategory',
          title: '发票种类',
          minWidth: 130,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_inv_category,
              cellValue
            );
          },
        },
        {
          field: 'invoiceNo',
          title: '发票编号',
          minWidth: 150,
        },
        {
          field: 'invoiceCode',
          title: '发票代码',
          minWidth: 140,
        },
        {
          field: 'openStatus',
          title: '开票状态',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              [
                { label: '开票失败', value: '0' },
                { label: '开票成功', value: '1' },
                { label: '开票中', value: '2' },
              ],
              cellValue
            );
          },
        },
        {
          field: 'buyerName',
          title: '购方名称',
          minWidth: 180,
        },
        {
          field: 'sellerName',
          title: '销方名称',
          minWidth: 180,
        },
        {
          title: '开票类型',
          slots: { default: 'invType' },
          minWidth: 120,
          align: 'center',
        },
        {
          field: 'includeTaxAmt',
          title: '开票金额(元)',
          minWidth: 130,
        },
        {
          slots: { default: 'orderCount' },
          title: '关联订单数',
          minWidth: 120,
        },
        {
          field: 'drawer',
          title: '开票申请人',
          minWidth: 130,
        },
        {
          field: 'receiveEmail',
          title: '接收邮箱',
          minWidth: 160,
        },
        {
          field: 'receivePhone',
          title: '接收手机号',
          minWidth: 130,
        },
        {
          field: 'reverseTime',
          title: '冲红日期',
          minWidth: 140,
        },
        {
          field: 'redReason',
          title: '发票冲红原因',
          minWidth: 180,
        },
        {
          field: 'redPerson',
          title: '发票冲红人',
          minWidth: 130,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          minWidth: 160,
          align: 'center',
          fixed: 'right',
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        searchCreateTime: [], // 开票日期
        invoiceCode: '', // 发票代码
        invCategory: '', // 发票种类
        openStatus: '', // 开票状态
        invType: '', // 开票类型
        drawer: '', // 开票申请人
        buyerName: '', // 购方名称
        invoiceNo: '', // 发票编号
        redPerson: '', // 冲红人
        redDate: [], // 冲红日期
      },
      redData: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: '申请冲红',
        // 冲红原因
        remark: '',
      },
      selectData: {},
      formLabelWidth: '100px',
      rules: {
        remark: [
          { required: true, message: '请输入冲红原因', trigger: 'blur' },
        ],
      },
      previewData: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: '预览',
        // 图片地址
        imgUrl:
          'https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png',
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'searchCreateTime',
            title: '开票日期',
            element: 'el-date-picker',
            required: true,
            props: {
              type: 'daterange',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
              options: [],
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'invoiceCode',
            title: '发票代码',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'invCategory',
            title: '发票种类',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_inv_category,
            },
          },
          {
            field: 'openStatus',
            title: '开票状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [
                { label: '开票失败', value: '0' },
                { label: '开票成功', value: '1' },
                { label: '开票中', value: '2' },
              ],
            },
          },
          {
            field: 'invType',
            title: '开票类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_inv_type,
            },
          },
          {
            field: 'drawer',
            title: '开票申请人',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'buyerName',
            title: '购方名称',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'invoiceNo',
            title: '发票编号',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'redPerson',
            title: '冲红人',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [
                { label: '张三', value: 'zhangsan' },
                { label: '李四', value: 'lisi' },
              ],
            },
          },
          {
            field: 'redDate',
            title: '冲红日期',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      let params = {
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        ...this.params,
        invBeginTime: '',
        invEndTime: '',
        reverseBeginTime: '',
        reverseEndTime: '',
      };
      if (this.params.searchCreateTime.length > 0) {
        params.invBeginTime = this.params.searchCreateTime[0];
        params.invEndTime = this.params.searchCreateTime[1];
      }
      if (this.params.redDate.length > 0) {
        params.reverseBeginTime = this.params.redDate[0];
        params.reverseEndTime = this.params.redDate[1];
      }
      this.loading = true;
      const [err, res] = await queryInvoiceRecordPage(params);
      this.loading = false;
      if (err) {
        return;
      }
      const { data, total } = res;
      this.tableData = data;
      this.tablePage.total = total;
    },
    handleOutput() {},
    // 订单详情
    handleDetail(row) {
      this.$router.push({
        path: '/v2g-charging/invoicesManagement/invoiceRecordQuery/orderDetails',
        query: {
          invoiceId: row.invoiceId,
        },
      });
    },
    // 预览
    handlePreview(row) {
      this.previewData.imgUrl = row.invoicePdfUrl;
      this.previewData.open = true;
    },
    // 冲红
    handleRed(row) {
      this.selectData = row;
      this.redData.open = true;
    },
    // 冲红弹窗关闭
    handleCancel() {
      this.redData.open = false;
    },
    // 弹窗确定
    async submitFileForm() {
      console.log('selectData', this.selectData);
      this.$refs.redData.validate(async (valid) => {
        if (valid) {
          console.log('redData', this.redData.remark);
          let params = {
            invoiceId: this.selectData.invoiceId,
            bizType: this.selectData.bizType,
          };
          const [err, res] = await makeOutRedInvoice(params);
          if (err) {
            return;
          }
          this.$message({
            type: 'success',
            message: '冲红成功',
          });
          this.handleCancel();
        }
      });
    },
    // 重新开具
    async handleReissue(row) {
      let params = {
        invoiceId: row.invoiceId,
        bizType: row.bizType,
      };
      const [err, res] = await makeOutBlueInvoice(params);
      if (err) {
        return;
      }
      this.$message({
        type: 'success',
        message: '开具成功',
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  // background: #fff;
  // padding: 20px;
  // border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }

  .red-invType {
    color: red;
    padding: 3px;
    border-radius: 6px;
    background: #ff000024;
    width: 80px;
  }

  .blue-invType {
    color: blue;
    padding: 3px;
    border-radius: 6px;
    background: #0000ff24;
    width: 80px;
  }
}
</style>
