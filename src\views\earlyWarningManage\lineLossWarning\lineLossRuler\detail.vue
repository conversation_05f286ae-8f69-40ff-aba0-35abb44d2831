<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
        
    </div>
    
  </template>
  
  <script>
  
    export default {
    components: {
        
    },
    dicts: [],
    data() {
      return {
            
      };
    },

    computed: {
    },
    mounted() {

    },
    methods: {
       
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }
   
  

 
  </style>
  