import request from '@/utils/request';

const baseUrl = '/vehicle-charging-admin';

// 清分规则分页查询
export function clearRulePage(data) {
  return request({
    url: baseUrl + '/clearRule/page',
    method: 'post',
    data: data,
  });
}

// 清分规则详情
export function clearRuleDetail(data) {
  return request({
    url: baseUrl + '/clearRule/detail',
    method: 'post',
    data: data,
  });
}

// 清分规则新增
export function clearRuleCreate(data) {
  return request({
    url: baseUrl + '/clearRule/create',
    method: 'post',
    data: data,
  });
}

// 清分规则修改
export function clearRuleUpdate(data) {
  return request({
    url: baseUrl + '/clearRule/update',
    method: 'post',
    data: data,
  });
}

// 清分规则启用/停用
export function clearRuleOnOff(data) {
  return request({
    url: baseUrl + '/clearRule/onOff',
    method: 'post',
    data: data,
  });
}

// 清分规则删除
export function clearRuleRemove(data) {
  return request({
    url: baseUrl + '/clearRule/remove',
    method: 'post',
    data: data,
  });
}

// 车联网场站分成规则管理分页查询
export function stationClearRulePage(data) {
  return request({
    url: baseUrl + '/stationClearRule/page',
    method: 'post',
    data: data,
  });
}

// 清分规则详情
export function stationClearRuleDetail(data) {
  return request({
    url: baseUrl + '/stationClearRule/detail',
    method: 'post',
    data: data,
  });
}

// 清分规则新增
export function stationClearRuleCreate(data) {
  return request({
    url: baseUrl + '/stationClearRule/create',
    method: 'post',
    data: data,
  });
}

// 清分规则修改
export function stationClearRuleUpdate(data) {
  return request({
    url: baseUrl + '/stationClearRule/update',
    method: 'post',
    data: data,
  });
}

// 清分规则启用/停用
export function stationClearRuleOnOff(data) {
  return request({
    url: baseUrl + '/stationClearRule/onOff',
    method: 'post',
    data: data,
  });
}

// 清分规则删除
export function stationClearRuleRemove(data) {
  return request({
    url: baseUrl + '/stationClearRule/remove',
    method: 'post',
    data: data,
  });
}
