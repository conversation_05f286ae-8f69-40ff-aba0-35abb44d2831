<template>
    <div class="container container-float " style="padding: 0 ">
        <div class="table-wrap">
            <BuseCrud
                ref="crud"
                :loading="loading"
                :filterOptions="filterOptions"
                :tablePage="tablePage"
                :tableColumn="tableColumn"
                :tableData="tableData"
                :pagerProps="pagerProps"
                :modalConfig="modalConfig"
                 class="buse-wrap-station"
                @loadData="loadData"
            >
                <template slot="defaultHeader">
                    <div class="card-head">
                        <div class="card-head-text">提现管理列表</div>
                        <div class="top-button-wrap">
                            <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
                                新增提现申请
                            </el-button>
                        </div>
                    </div>
                </template>

                <template slot="operate" slot-scope="{ row }">
                    <div class="menu-box">
                        <el-button type="primary" plain @click="handleDetail(row)">
                            详情
                        </el-button>

                        <el-button type="primary" plain v-if="row.status === '0' && row.auditStatus === '2' && !row.clearVoucher " @click="handleUpload(row)">
                            上传凭证
                        </el-button>

                        <el-button type="primary" plain v-if="row.status === '1' && row.auditStatus === '2' && row.clearVoucher " @click="handleFile(row)">
                            查看凭证
                        </el-button>
                    </div>
                </template>
            </BuseCrud>
        </div>

        <WithdrawalModal ref="withdrawalModal"  @loadData="getOperatorsAccount"/>
        <UploadVoucherModal ref="uploadVoucherModal"   @loadData="getOperatorsAccount"/>
    </div>
    
  </template>
  
  <script>

  import WithdrawalModal from './components/withdrawalModal.vue'
  import UploadVoucherModal from './components/uploadVoucherModal.vue'
import StatusDot from '@/components/Business/StatusDot';
import StatusInfo from '@/components/Business/StatusInfo';



  import {
    getWithdrawalPage,
    getOperatorsAccount,
  } from '@/api/operator/index'
  
    export default {
    components: {
        WithdrawalModal, 
        UploadVoucherModal,
        StatusDot,
        StatusInfo,
    },
    dicts: [
        'ls_charging_operator_category',
    ],
    data() {
      return {
        operatorNo: '',
        operatorAccountId: '',
        balance: '',
        loading: false,
        tablePage: { total: 0, currentPage: 1, pageSize: 10 },
        tableData: [],
        tableColumn: [
            {
                type: 'seq',
                title: '序号',
                width: 60,
            },
            {
                field: 'payId',
                title: '提现编号',
                minWidth: 120,
                slots: {
                  default: ({ row }) => [
                      <el-tooltip 
                          content={row.payId} 
                          placement="top" 
                          disabled={!row.payId || row.payId.length < 10}
                      >
                          <span class="ellipsis-text">{row.payId}</span>
                      </el-tooltip>
                  ]
              }
            },
            {
                field: 'operatorName',
                title: '公司名称',
                minWidth: 120,
                slots: {
                  default: ({ row }) => [
                      <el-tooltip 
                          content={row.operatorName} 
                          placement="top" 
                          disabled={!row.operatorName || row.operatorName.length < 10}
                      >
                          <span class="ellipsis-text">{row.operatorName}</span>
                      </el-tooltip>
                  ]
              }
            },
            {
                field: 'operatorType',
                title: '公司类型',
                minWidth: 120,
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_operator_category,
                    cellValue
                  );
                },
            },
            {
                field: 'amount',
                title: '申请提现金额(元)',
                minWidth: 180,
            },
            {
                field: 'currentBalance',
                title: '当前账户余额(元)',
                minWidth: 180,
            },
            {
                field: 'realAmount',
                title: '提现后账户余额(元)',
                minWidth: 180,
            },
            {
                field: 'arrivalTime',
                title: '到账时间',
                minWidth: 180,
            },
            {
                field: 'createBy',
                title: '申请人',
                minWidth: 120,
            },
            {
                field: 'createTime',
                title: '申请时间',
                minWidth: 180,
            },

            {
                field: 'status',
                title: '提现状态',
                minWidth: 120,
                fixed: 'right',
                slots: {
                    // 自定义render函数
                    default: ({ row }) => {
                        return (
                            <StatusInfo
                                value={row.status}
                                dictValue={this.statusList}
                                colors={['medium', 'success','high' ]}
                            ></StatusInfo>
                            );
                        },
                    },
            },
            {
                field: 'auditStatus',
                title: '审核状态',
                minWidth: 120,
                fixed: 'right',
                slots: {
                  // 自定义render函数
                  default: ({ row }) => {
                    return (
                      <StatusDot
                        value={row.auditStatus}
                        dictValue={this.auditStatusList}
                        colors={['warning','warning', 'success', 'danger',]}
                      ></StatusDot>
                    );
                  },
                },
            },
            {
                title: '操作',
                slots: { default: 'operate' },
                width: 220,
                align: 'center',
                fixed: 'right',
            }
            
            
        ],
        pagerProps: {
            layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
        },
        params: {
            widthdrawalNo: '',
            companyType: '',
            companyName: '',
            auditStatus: '',
            applyTime: [],
            inBankTime: [],
        },
        statusList: [
            {
                label: '提现中',
                value: '0',
            },
            {
                label: '提现成功',
                value: '1',
            },
            {
                label: '提现失败',
                value: '2',
            },
        ], // 提现状态
        auditStatusList: [
            {
                label: '待审核',
                value: '0',
            },
            {
                label: '审核中',
                value: '1',
            },
            {
                label: '审核通过',
                value: '2',
            },
            {
                label: '审核不通过',
                value: '3',
            },
        ],
      };
    },

    computed: {
        filterOptions() {
            return {
                config: [
                    {
                        field: 'widthdrawalNo', 
                        title: '提现编号',
                        element: 'el-input',
                        placeholder: '请输入',
                    },
                    {
                        field: 'companyType',
                        title: '公司类型',
                        element: 'el-select',
                        props: {
                            placeholder: '请选择',
                            options: this.dict.type.ls_charging_operator_category,
                        },
                    },
                    {
                        field: 'companyName', 
                        title: '公司名称',
                        element: 'el-input',
                        placeholder: '请输入',
                    },
                    {
                        field: 'auditStatus',
                        title: '审核状态',
                        element: 'el-select',
                        props: {
                        placeholder: '请选择',
                        options: this.auditStatusList,
                        },
                    },
                    {
                        field: 'applyTime',
                        title: '申请时间',
                        element: 'el-date-picker',
                        props: {
                            type: 'daterange',
                            startPlaceholder: '开始日期',
                            endPlaceholder: '结束日期',
                            valueFormat: 'yyyy-MM-dd',
                        },
                    },
                    {
                        field: 'inBankTime',
                        title: '到账时间',
                        element: 'el-date-picker',
                        props: {
                            type: 'daterange',
                            startPlaceholder: '开始日期',
                            endPlaceholder: '结束日期',
                            valueFormat: 'yyyy-MM-dd',
                        },
                    },
                    
                ],
                params: this.params,
            };
        },
        modalConfig() {
        return {
            addBtn: false,
            viewBtn: false,
            menu: false,
            editBtn: false,
            delBtn: false,
        };
        },
    },
    mounted() {
        const operatorNo = this.$route.query.operatorNo;
        this.operatorNo = operatorNo;
        
        this.getOperatorsAccount();
    },
    methods: {
        // 运营商账户查询
    async getOperatorsAccount(){
      const [err, res] = await getOperatorsAccount({
        operatorNo: this.operatorNo
      })

      if (err) return 
      const  {
        operatorAccountId,
        operatorBalance,
        
      } = res.data

    
      this.balance = operatorBalance
     
      this.operatorAccountId = operatorAccountId

      this.tablePage = { total: 0, currentPage: 1, pageSize: 10 },

      this.loadData();
    },
        async loadData() {
            const {
                widthdrawalNo,
                companyType,
                companyName,
                auditStatus,
                applyTime,
                inBankTime,
            } = this.params;

            let applyTimeBeg = ''
            let applyTimeEnd = ''
            if(applyTime && applyTime.length > 0) {
                applyTimeBeg = applyTime[0]
                applyTimeEnd = applyTime[1]
            }

            let arrivalTimeBeg = ''
            let arrivalTimeEnd = ''
            if(inBankTime && inBankTime.length > 0) {
                arrivalTimeBeg = inBankTime[0]
                arrivalTimeEnd = inBankTime[1]
            }


            const params = {
                payId: widthdrawalNo,
                operatorType: companyType,
                operatorNo: companyName,
                auditStatus,
                applyTimeBeg,
                applyTimeEnd,
                arrivalTimeBeg,
                arrivalTimeEnd,
                pageNum: this.tablePage.currentPage,
                pageSize: this.tablePage.pageSize,
                operatorAccountId: this.operatorAccountId
            }

            this.loading = true;

            const [err, res] = await getWithdrawalPage(params)

            this.loading = false;

            if (err) return 
            const { data, total } = res;


            this.tableData = data;
            this.tablePage.total = total;

            

        },
        handleAdd() {
            this.$refs.withdrawalModal.operatorNo = this.operatorNo;
            this.$refs.withdrawalModal.operatorAccountId = this.operatorAccountId;

            this.$refs.withdrawalModal.useableBalance = this.balance;
            this.$refs.withdrawalModal.dialogVisible = true;
        },

        // 上传凭证
        handleUpload(row) {
            const {recordId} = row
            this.$refs.uploadVoucherModal.operatorAccountId = this.operatorAccountId;
            this.$refs.uploadVoucherModal.recordId = recordId;
            this.$refs.uploadVoucherModal.dialogVisible = true;
        },

        // 查看凭证
        handleFile(row) {
            const link = document.createElement('a')
            link.href = 'http://hunanchongdian-test.oss-cn-beijing.aliyuncs.com/vehicle-grid/202505/9da1325a-6242-48af-b90b-1ecf97fb11bb.jpg'
            // link.download = 'vehicle-image.jpg' // 自定义文件名
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
        },

        // 查看详情
        handleDetail(row) {
            const {recordId} = row
            this.$router.push({
                path: '/v2g-charging/baseInfo/operator/withdrawalDetail',
                query: {
                    recordId
                }
            });
        }
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }

  .table-wrap {
  background: #fff;
  margin: 16px;
  border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .btn-export {
    margin-bottom: 16px;
  }
}
   

.card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }
 

  .menu-box {
    .el-button {
      padding: 5px;
    }
  }


.ellipsis-text {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
  </style>
  