<template>
  <el-dialog title="巡视内容" :visible.sync="dialogVisible" width="630px">
    <div class="info-card">
      <div class="form-wrap" v-if="dialogVisible">
        <el-form
          :model="baseInfo.form"
          :rules="baseInfo.rules"
          ref="baseInfoForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="巡视内容编号："
                prop="patrolContentNo"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.patrolContentNo"
                  placeholder="请输入巡视内容编号"
                  :disabled="type === 'detail'"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="巡视内容名称："
                prop="patrolContentName"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.patrolContentName"
                  placeholder="请输入"
                  :disabled="type === 'detail'"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="巡视要求："
                prop="patrolRequire"
                :label-width="formLabelWidth"
              >
                <el-input
                  type="textarea"
                  :rows="4"
                  v-model="baseInfo.form.patrolRequire"
                  placeholder="请输入巡视要求"
                  :disabled="type === 'detail'"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="是否需要上传水印照片："
                prop="watermark"
                :label-width="formLabelWidth"
              >
                <el-radio-group
                  v-model="baseInfo.form.watermark"
                  :disabled="type === 'detail'"
                >
                  <el-radio
                    :label="Number(item.value)"
                    v-for="(item, index) in this.dict.type
                      .ls_charging_watermark"
                    :key="index"
                  >
                    {{ item.label || '' }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="排序："
                prop="sort"
                :label-width="formLabelWidth"
              >
                <el-input-number
                  v-model="baseInfo.form.sort"
                  :min="1"
                  :max="999999"
                  :disabled="type === 'detail'"
                  style="width: 100%"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="状态："
                prop="status"
                :label-width="formLabelWidth"
              >
                <el-radio-group
                  v-model="baseInfo.form.status"
                  :disabled="type === 'detail'"
                >
                  <el-radio
                    :label="Number(item.value)"
                    v-for="(item, index) in this.dict.type
                      .ls_charging_patrol_enabled_status"
                    :key="index"
                  >
                    {{ item.label || '' }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="bottom-wrap">
      <div>
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          @click="handleSave"
          :loading="submitLoading"
          type="primary"
          v-if="type !== 'detail'"
        >
          提交
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import {
  createContent,
  updateContent,
} from '@/api/interconnection/patrolContent';

export default {
  props: {
    type: {
      type: String,
      default: 'create',
    },
    detailObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  dicts: ['ls_charging_patrol_enabled_status', 'ls_charging_watermark'],
  data() {
    return {
      dialogVisible: false,
      formLabelWidth: '120px',
      baseInfo: {
        form: {
          patrolContentId: '',
          patrolContentNo: '',
          patrolContentName: '',
          patrolRequire: '',
          watermark: 0,
          sort: 1,
          status: 0,
        },
        rules: {
          patrolContentNo: [
            { required: true, message: '请输入巡视内容编号', trigger: 'blur' },
          ],
          patrolContentName: [
            { required: true, message: '请输入巡视内容名称', trigger: 'blur' },
          ],
          patrolRequire: [
            {
              required: true,
              message: '请输入巡视要求',
              trigger: 'blur',
            },
          ],
          watermark: [
            {
              required: true,
              message: '请选择是否需要上传水印照片',
              trigger: 'blur',
            },
          ],
          sort: [
            {
              required: true,
              message: '请输入排序',
              trigger: 'blur',
            },
          ],
          status: [
            {
              required: true,
              message: '请选择状态',
              trigger: 'blur',
            },
          ],
        },
      },
      submitLoading: false,
    };
  },
  watch: {
    dialogVisible(value) {
      if (!value) {
        this.baseInfo.form = {
          patrolContentId: '',
          patrolContentNo: '',
          patrolContentName: '',
          patrolRequire: '',
          watermark: 0,
          sort: 1,
          status: 0,
        };
      } else if (this.type !== 'create') {
        // 编辑或详情时，填充表单数据
        this.baseInfo.form = {
          ...this.detailObj,
        };
      }
    },
  },
  methods: {
    // 取消
    handleCancel() {
      this.dialogVisible = false;
    },

    // 保存
    handleSave() {
      this.$refs.baseInfoForm.validate(async (valid) => {
        if (!valid) return;

        this.submitLoading = true;
        try {
          const params = {
            ...this.baseInfo.form,
          };

          let err, res;
          if (this.type === 'create') {
            // 创建巡视内容
            [err, res] = await createContent(params);
          } else {
            // 更新巡视内容
            [err, res] = await updateContent(params);
          }

          if (err) return;

          this.$message({
            type: 'success',
            message: this.type === 'create' ? '创建成功!' : '更新成功!',
          });

          this.dialogVisible = false;
          this.$emit('contentAdd');
        } finally {
          this.submitLoading = false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.info-card {
  background-color: #fff;
  border-radius: 5px;
  overflow: hidden;

  .form-wrap {
    padding: 0 16px 16px 16px;
  }
}

.bottom-wrap {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-color: #ffffff;
  padding-right: 32px;
  box-sizing: border-box;
  margin-top: 20px;
}

::v-deep .hidden-label .el-form-item__label:before {
  visibility: hidden;
}
</style>
