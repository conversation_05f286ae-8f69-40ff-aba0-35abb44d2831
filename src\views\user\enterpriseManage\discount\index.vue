<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
      <div class="info-card">
            <div class="card-head">
                <div style=" display: flex; align-items: center;">
                  <div class="before-icon"></div>
                  <div class="card-head-text">基础信息</div>
                </div>
               
                <el-button 
                  type="primary" 
                  @click="() => handleStation()">
                  场站配置
                </el-button>
            </div>
            <div class="card-head-split"></div>
            

            <div class="form-wrap">
                <BuseCrud
                      ref="crud"
                      :loading="loading"
                      :tablePage="tablePage"
                      :tableColumn="tableColumn"
                      :tableData="tableData"
                      :modalConfig="modalConfig"
                      @loadData="loadData"
                  >

                      <template slot="status" slot-scope="{ row }">
                        <div style="display: flex;">
                            <el-switch
                                v-model="row.status"
                                @change="changeStatus(row)"
                            >
                            </el-switch>
                            <div style="margin-left: 8px;"> {{ row.status ? '启用' : '禁用'}}</div>
                        </div>
                          
                      </template>

                      <template slot="operate" slot-scope="{ row }">
                          <div class="menu-box">
                              <el-button
                                  class="button-border"
                                  type="primary"
                                  plain
                                  @click="handleConfig(row)"
                              >
                                修改
                              </el-button>
                              <el-button
                                  class="button-border"
                                  type="primary"
                                  plain
                                  @click="handleHistory(row)"
                              >
                                  定价历史记录
                              </el-button>



                          
                          </div>
                      
                      </template>

                </BuseCrud>
            </div>
            
        </div>

        <HistoryRecord ref="historyRecordModal" />
        <StationConfigModal ref="stationConfigModal"  @loadData="loadData" />

        <UpdateConfigModal ref="updateConfigModal"  @loadData="loadData"/>
    </div>
    
  </template>
  
  <script>

  import HistoryRecord from '../components/historyRecord.vue'

  import StationConfigModal from '../components/stationConfigModal.vue' 
  
  import UpdateConfigModal from '../components/updateConfigModal.vue'
  
  import {
    enterpriseDiscount,
    enterpriseDiscountStatus,

  } from '@/api/user/enterprise'
    export default {
    components: {
      HistoryRecord,
      StationConfigModal,
      UpdateConfigModal,
    },
    dicts: [],
    data() {
      return {
            enterpriseId: '',
            loading: false,
            tablePage: { total: 0, currentPage: 1, pageSize: 10 },
            tableColumn: [
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60,
                    fixed: 'left',
                },
                {
                    field: 'stationName',
                    title: '场站名称',
                    minWidth: 200,
                },
                {
                    title: '优惠方式',
                    field: 'discountDesc',
                    minWidth: 150,
                },
                // {
                //     title: '原电价',
                //     field: 'oriElecPrice',
                //     minWidth: 150,
                // },
                // {
                //     title: '原服务费单价',
                //     field: 'oriServicePrice',
                //     minWidth: 150,
                // },
                // {
                //     title: '优惠后电价',
                //     field: 'elecPrice',
                //     minWidth: 150,
                // },
                // {
                //     title: '优惠后服务费单价',
                //     field: 'servicePrice',
                //     minWidth: 150,
                // },
                {
                    title: '生效时间',
                    field: 'startTime',
                    minWidth: 200,
                },
                {
                    title: '结束时间',
                    field: 'endTime',
                    minWidth: 200,
                    formatter: ({ cellValue }) => {
                        return cellValue ? cellValue  : '长期有效';
                    },
                },
                {
                    field: 'status',
                    title: '状态',
                    minWidth: 100,
                    fixed: 'right',
                    slots: { default: 'status' },
                },
                {
                    title: '操作',
                    slots: { default: 'operate' },
                    width: 220,
                    align: 'center',
                    fixed: 'right',
                }
            ],
            tableData:[]
      };
    },

    computed: {
      modalConfig() {
            return {
                addBtn: false,
                viewBtn: false,
                menu: false,
                editBtn: false,
                delBtn: false,
            }
        },
    },
    mounted() {
        this.enterpriseId = this.$route.query.enterpriseId
        this.loadData()
    },
    methods: {
        // 定价历史记录
        handleHistory(row) {
            const stationId = row.stationId

            const enterpriseId = this.enterpriseId

            this.$refs.historyRecordModal.stationId = stationId
            this.$refs.historyRecordModal.enterpriseId = enterpriseId

            this.$refs.historyRecordModal.loadData()

            this.$refs.historyRecordModal.dialogVisible = true
        },

        // 场站配置
        handleStation() {
            this.$refs.stationConfigModal.enterpriseId = this.enterpriseId

            this.$refs.stationConfigModal.dialogVisible = true
        },

        // 修改配置
        handleConfig(row) {
            const {
                enterpriseId,
                stationId,
                stationName,
                priceNo,
                elecPriceMode,
                elecPriceValue,
                servicePriceMode,
                servicePriceValue,
                startTime,
                endTime,
            } = row
            this.$refs.updateConfigModal.enterpriseId = enterpriseId
            this.$refs.updateConfigModal.stationId = stationId
            this.$refs.updateConfigModal.stationName = stationName
            this.$refs.updateConfigModal.priceNo = priceNo

            if(elecPriceMode === 'NONE') {
                this.$refs.updateConfigModal.electricType = '1'
            } else if(elecPriceMode === 'FIXED') {
                this.$refs.updateConfigModal.electricType = '2'
                this.$refs.updateConfigModal.onePirce = elecPriceValue
            } else if(elecPriceMode === 'RATE') {
                this.$refs.updateConfigModal.electricType = '3'
                this.$refs.updateConfigModal.discount = elecPriceValue
            } else if(elecPriceMode === 'ADJUSTMENT') {
                this.$refs.updateConfigModal.electricType = '4'
                if(elecPriceValue >= 0) {
                    this.$refs.updateConfigModal.electricChangeType = '1'
                    this.$refs.updateConfigModal.electricChange = elecPriceValue
                } else {
                    this.$refs.updateConfigModal.electricChangeType = '2'
                    this.$refs.updateConfigModal.electricChange = Math.abs(elecPriceValue)
                }
            }

            if(servicePriceMode === 'NONE') {
                this.$refs.updateConfigModal.serviceType = '1'
            } else if(servicePriceMode === 'FIXED') {
                this.$refs.updateConfigModal.serviceType = '2'
                this.$refs.updateConfigModal.serviceOnePirce = servicePriceValue
            } else if(servicePriceMode === 'RATE') {
                this.$refs.updateConfigModal.serviceType = '3'
                this.$refs.updateConfigModal.serviceDiscount = servicePriceValue
            } else if(servicePriceMode === 'ADJUSTMENT') {
                this.$refs.updateConfigModal.serviceType = '4'
                if(servicePriceValue>=0) {
                    this.$refs.updateConfigModal.serviceChangeType = '1'
                    this.$refs.updateConfigModal.serviceChange = servicePriceValue
                } else {
                    this.$refs.updateConfigModal.serviceChangeType = '2'
                    this.$refs.updateConfigModal.serviceChange = Math.abs(servicePriceValue)
                }
            }

            const checkList = []
            if (startTime) {
                checkList.push('beginTime')
                this.$refs.updateConfigModal.beginTime = startTime
            }

            if(endTime) {
                checkList.push('endTime')
                this.$refs.updateConfigModal.endTime = endTime
            }

            this.$refs.updateConfigModal.checkList = checkList

            this.$refs.updateConfigModal.dialogVisible = true
        },

        async loadData() {
            console.log('loadData')
            const params = {
                enterpriseId: this.enterpriseId,
                pageNum: this.tablePage.currentPage,
                pageSize: this.tablePage.pageSize,
            }
            this.loading = true
            const [err, res] = await enterpriseDiscount({
                ...params,
            });
            this.loading = false;
            if (err) {
                return this.$message.error(err.message);
            }

            const { data, total } = res;

            this.tableData =data.map((item) => {
                return { ...item, status: item.status === 'ENABLE' ? true : false };
            });
            this.tablePage.total = total;
            
        },

        // 优惠启用 禁用
        changeStatus(row) {
            const { status, stationName, stationId} = row;

            this.$confirm(
                `确定${!status ? '停用' : '启用'}${stationName}的优惠配置吗？`,
                '提示',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }
            )
            .then(async () => {
                const [err, res] = await enterpriseDiscountStatus({
                    enterpriseId: this.enterpriseId,
                    stationId: stationId,
                    status: !status ? 'DISABLE' : 'ENABLE',
                });
                if (err) {
                    return this.$message.error(err.message);
                }
                this.$message.success(`${!status ? '停用' : '启用'}成功`);
                this.loadData();
            })
            .catch(() => {
                row.status = !row.status;
            });
        },

    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }


  .info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  background-color: #fff;

  .card-head {
    height: 48px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }

    .card-head-text {
      font-weight: 500;
      font-size: 16px;
      color: #12151a;
    }
  }
  .card-head-split {
    height: 1px;
    background-color: #F9F9FB;

  }

  .form-wrap {
    padding: 24px;
  }


}

.button-border {
    border: 0.01rem solid #217AFF;
    color: #217AFF;
    background-color: #fff;
}
::v-deep .bd3001-content {
  padding: 0px !important;
}
   
 
  </style>
  