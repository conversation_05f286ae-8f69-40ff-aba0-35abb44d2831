<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
        <div class="device-head">
            <img src="@/assets/charge/price-period-title-icon.png" class="device-head-icon">
            
            <div class="device-info-wrap">
                <div class="device-title-wrap">
                    <div class="device-title">计费名称：{{ billModelName }}</div>
                    <div class="device-status" v-if="status">{{ status }}</div>
                </div>
                <div class="device-info-wrap">
                    <el-row>
                        <el-col :span="8">
                            <span class="label">计费编号：</span>
                            <span class="value">{{ billModelNo }}</span>
                        </el-col>
                        <el-col :span="8">
                            <span class="label">适用区域：</span>
                            <span class="value">{{ areaName }}</span>
                        </el-col>

                    </el-row>
                </div>
            </div>



            <!-- <el-button
                type="primary"
                 @click="drawer = true"
            >
                审核轨迹
            </el-button> -->
        </div>

        <div class="info-card" >
            <div class="card-head" style="margin-bottom: 8px;">
                <div class="before-icon"></div>
                <div class="card-head-text">计费基础信息</div>
            </div>

            <div class="form-wrap">
                <el-row :gutter="20" style="margin-bottom: 24px;">
                    <el-col :span="8">
                        <div style="display: flex;">
                            <div class="info-title">申请人：</div>
                            <div class="info-detail">{{ baseInfo.applyer }}</div>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div style="display: flex;">
                            <div class="info-title">申请单位：</div>
                            <div class="info-detail">{{ baseInfo.applyUnit }}</div>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div style="display: flex;">
                            <div class="info-title">申请时间：</div>
                            <div class="info-detail">{{ baseInfo.applyTime }}</div>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>

        <div class="info-card" >
            <div class="card-head" style="margin-bottom: 8px;">
                <div class="before-icon"></div>
                <div class="card-head-text">计费信息</div>
            </div>

            <div class="form-wrap">
                <el-row :gutter="20" style="margin-bottom: 24px;">
                    <el-col :span="6">
                        <div style="display: flex;">
                            <div class="info-title">计费模式：</div>
                            <div class="info-detail">{{ billModelInfo.billModelDesc }}</div>
                        </div>
                    </el-col>

                    <!-- <el-col :span="6">
                        <div style="display: flex;">
                            <div class="info-title">收费类型：</div>
                            <div class="info-detail">{{ billModelInfo.feeType }}</div>
                        </div>
                    </el-col> -->


                    <el-col :span="6" v-if="billModelInfo.billModel === '0201'">
                        <div style="display: flex;">
                            <div class="info-title">电价(元/kWh)：</div>
                            <div class="info-detail">{{ billModelInfo.elePrice }}</div>
                        </div>
                    </el-col>

                    <el-col :span="6" v-if="billModelInfo.billModel === '0201'">
                        <div style="display: flex;">
                            <div class="info-title">服务费(元/kWh)：</div>
                            <div class="info-detail">{{ billModelInfo.servicePrice }}</div>
                        </div>
                    </el-col>

                    <el-col :span="6" v-if="billModelInfo.billModel === '0201'">
                        <div style="display: flex;">
                            <div class="info-title">总价(元/kWh)：</div>
                            <div class="info-detail">{{ billModelInfo.totalPrice }}</div>   
                        </div>
                    </el-col>


                   
                </el-row>

                <!-- <el-row :gutter="20" style="margin-bottom: 24px;">
                     <el-col :span="6">
                        <div style="display: flex;">
                            <div class="info-title">营销电价类型：</div>
                            <div class="info-detail">{{ billModelInfo.priceType }}</div>
                        </div>
                    </el-col>

                    <el-col :span="6">
                        <div style="display: flex;">
                            <div class="info-title">营销电价名称：</div>
                            <div class="info-detail">{{ billModelInfo.priceName }}</div>
                        </div>
                    </el-col>
                    <el-col :span="6">
                        <div style="display: flex;">
                            <div class="info-title">是否与营销电价一致：</div>
                            <div class="info-detail">{{ billModelInfo.isSame }}</div>
                        </div>
                    </el-col>


                </el-row> -->

                <el-row :gutter="20" style="margin-bottom: 24px;" v-if="billModelInfo.billModel === '0202'">
                    <el-col :span="6">
                        <div style="display: flex;">
                            <div class="info-title">尖电价：</div>
                            <div class="info-detail">
                                <div class="info-price">{{ elePriceInfo.superPeak }}</div>
                                <div>元/kWh</div>
                            </div>
                        </div>
                    </el-col>

                    <el-col :span="6" >
                        <div style="display: flex;">
                            <div class="info-title">峰电价：</div>
                            <div class="info-detail">
                                <div class="info-price">{{ elePriceInfo.peak }}</div>
                                <div>元/kWh</div>
                            </div>
                        </div>
                    </el-col>

                    <el-col :span="6" >
                        <div style="display: flex;">
                            <div class="info-title">平电价：</div>
                            <div class="info-detail">
                                <div class="info-price">{{ elePriceInfo.normal }}</div>
                                <div>元/kWh</div>
                            </div>
                            
                        </div>
                    </el-col>

                    <el-col :span="6">
                        <div style="display: flex;">
                            <div class="info-title">谷电价：</div>
                            <div class="info-detail">
                                <div class="info-price">{{ elePriceInfo.valley }}</div>
                                <div>元/kWh</div>
                            </div>
                            
                        </div>
                    </el-col>
                </el-row>
                
                <el-row :gutter="20" style="margin-bottom: 24px;" v-if="billModelInfo.billModel === '0202'">
                    <el-col :span="6">
                        <div style="display: flex;">
                            <div class="info-title">尖服务费：</div>
                            <div class="info-detail">
                                <div class="info-price">{{ elePriceInfo.topServicePrice }}</div>
                                <div>元/kWh</div>
                            </div>
                        </div>
                    </el-col>

                    <el-col :span="6">
                        <div style="display: flex;">
                            <div class="info-title">峰服务费：</div>
                            <div class="info-detail">
                                <div class="info-price">{{ elePriceInfo.peakServicePrice }}</div>
                                <div>元/kWh</div>
                            </div>
                        </div>
                    </el-col>
                    <el-col :span="6">
                        <div style="display: flex;">
                            <div class="info-title">平服务费：</div>
                            <div class="info-detail">
                                <div class="info-price">{{ elePriceInfo.flatServicePrice }}</div>
                                <div>元/kWh</div>
                            </div>
                        </div>
                    </el-col>
                    <el-col :span="6">
                        <div style="display: flex;">
                            <div class="info-title">谷服务费：</div>
                            <div class="info-detail">
                                <div class="info-price">{{ elePriceInfo.valleyServicePrice }}</div>
                                <div>元/kWh</div>
                            </div>
                        </div>
                    </el-col>

                </el-row>

                <el-row :gutter="20" style="margin-bottom: 24px;" v-if="billModelInfo.billModel === '0202'">
                    <el-col :span="6">
                        <div style="display: flex;">
                            <div class="info-title">电价时段：</div>
                            <div class="info-detail">
                                {{ elePriceInfo.timePeriod }}
                            </div>
                        </div>
                    </el-col>
                </el-row>

                            <el-radio-group v-if="billModelInfo.billModel === '0202'" v-model="chooseTime"  @change="changeTime" style="margin-bottom: 16px;">
                                <el-radio-button v-for="(item, index) in timeList" :key="index" :label="item.month">{{ item.month }}</el-radio-button>
                            </el-radio-group>


                            <BuseCrud
                                v-if="billModelInfo.billModel === '0202'"
                                style="margin-bottom: 16px;"
                                ref="periodInfo"
                                :tableColumn="tableColumn"
                                :tableData="tableData"
                                :modalConfig="{ addBtn: false, menu: false }"
                            >
                            </BuseCrud>
            </div>
        </div>

        <div class="info-card" v-if="type!=='detail'">
            <div class="card-head" style="margin-bottom: 8px;">
                <div class="before-icon"></div>
                <div class="card-head-text">审核信息</div>
            </div>

            <div class="form-wrap">
                <el-form :model="form" :rules="rules" ref="form"  label-position="top">
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item
                                label="申请类型"
                                prop="result"
                                :label-width="formLabelWidth"
                            >
                                <el-select
                                    v-model="form.result"
                                    placeholder="请选择申请类型"
                                    style="width: 100%"
                                    >
                                    <el-option
                                        v-for="item in dict.type.ls_charging_approval"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item
                                label="申请说明"
                                prop="remark"
                                :label-width="formLabelWidth"
                            >
                                <el-input
                                    v-model="form.remark"
                                    type="textarea"
                                    :rows="3"
                                    placeholder="请输入申请说明"
                                ></el-input>
                            </el-form-item>
                        </el-col>  
                    </el-row>
                </el-form>
            </div>
        </div>


        <div class="bottom-wrap" v-if="type!=='detail'">
            <el-button
                @click="() => handleCancel()"
            >
                取消
            </el-button>
            <el-button
                type="primary"
                @click="() => handleConfirm()"
            >
                提交
            </el-button>
        </div>
    </div>
    
  </template>
  
  <script>

import {
    getBillModelDetail,
 } from "@/api/billingModel/index"; 
  
    export default {
    components: {
        
    },
    dicts: [
        'ls_charging_approval', // 审批状态
        'ls_charging_audit_type', // 审核状态
        'ls_charging_contracted_unit',
        'ls_charging_billing_charge_mode',
        'ls_charging_billing_charge_type',

    ],
    data() {
      return {
        type: 'index',
        chcNo: '',

        billModelName: '',
        status: '',
        billModelNo: '',
        areaName: '',

        baseInfo: {
            applyer: '',
            applyUnit: '',
            applyTime: '',
        },
        billModelInfo: {
            billModel: '', 
            feeType: '',
            priceType: '',
            priceName: '',
            isSame: '',
            elePrice: '',
            servicePrice: '',
            totalPrice: '',
        },

        elePriceInfo: {
            superPeak: '',
            peak: '',
            normal: '',
            valley: '',
            topServicePrice: '',
            peakServicePrice: '',
            flatServicePrice: '',
            valleyServicePrice: '',
            timePeriod: ''
        },

            timeList: [],
            chooseTime: '',
            tableColumn: [
                {
                    field: 'priceType',
                    title: '类型',
                    minWidth: 120, 
                    formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                            this.priceTypeList,
                            cellValue
                        );
                    },
                },
                {
                    field: 'startTime',
                    title: '开始时间',
                    minWidth: 120, 
                },
                {
                    field: 'endTime',
                    title: '结束时间',
                    minWidth: 120, 
                },
            ],


            tableData: [],

            priceTypeList: [
                { label: '尖', value: '1' },
                { label: '峰', value: '2' },
                { label: '平', value: '3' },
                { label: '谷', value: '4' },
            ],


        form: {
                result: '',
                remark: '',
        },
        rules: {
            result: [
               { required: true, message: '请选择审核结果', trigger: 'blur'}
            ],
        },
        formLabelWidth: '120px',

        drawer: false,
      };
    },

    computed: {
    },
    mounted() {
        this.type = this.$route.query.type
        this.chcNo = this.$route.query.chcNo
        this.getBillModelDetail();
    },
    methods: {
        async getBillModelDetail() {
            const [err, res] = await getBillModelDetail({
                chcNo: this.chcNo
            })
            if (err) return

            const {
                chcName,
                chcNo,
                suitCityName,
                approvalStatus,
                
                createUser,
                createUnit,
                createTime,

                chargeMode, // 计费模式
                itemNo,
                chargePrice,
                servicePrice,

                topChargePrice,
                peakChargePrice,
                flatChargePrice,
                valleyChargePrice,
                topServicePrice,
                peakServicePrice,
                flatServicePrice,
                valleyServicePrice,

                periodDetail
            } = res.data;

            this.billModelName = chcName
            this.billModelNo = chcNo
            this.areaName = suitCityName
            const approvalStatusDesc = this.selectDictLabel(
                            this.dict.type.ls_charging_audit_type,
                            approvalStatus
                        )
            this.status = approvalStatusDesc ? approvalStatusDesc : '待审核'

            this.baseInfo = {
                applyer: createUser,
                applyUnit: this.selectDictLabel(
                            this.dict.type.ls_charging_contracted_unit,
                            createUnit
                        ),
                applyTime:createTime
            }

            this.billModelInfo = {
                billModel:chargeMode,
                billModelDesc: this.selectDictLabel(
                            this.dict.type.ls_charging_billing_charge_mode,
                            chargeMode
                        ),
                feeType:  this.selectDictLabel(
                            this.dict.type.ls_charging_billing_charge_type,
                            itemNo
                        ), 
                elePrice:chargePrice,
                servicePrice: servicePrice,
                totalPrice: Number(chargePrice) + Number(servicePrice),
            }

            if(chargeMode === '0202'){
                const{
                    periodName,
                    periodDetailList,
                }  = periodDetail

                this.elePriceInfo = {
                    superPeak: topChargePrice,
                    peak: peakChargePrice,
                    normal: flatChargePrice,
                    valley: valleyChargePrice,
                    topServicePrice: topServicePrice || '0',
                    peakServicePrice: peakServicePrice || '0',
                    flatServicePrice: flatServicePrice || '0',
                    valleyServicePrice: valleyServicePrice || '0',
                    timePeriod: periodName
                }

                const list = Array.from(
                    periodDetailList.reduce((map, item) => {
                        const month = item.validMonth;
                        if (!map.has(month)) {
                        map.set(month, {
                            month,
                            priceList: [],
                            active: false
                        });
                        }
                        map.get(month).priceList.push({
                            priceType: item.timeFlag,
                            startTime: item.beginTime,
                            endTime: item.endTime
                        });
                        return map;
                    }, new Map()).values()
                ).map((group, index) => index === 0 ? { ...group, active: true } : group);
                
                this.timeList = list;

                this.chooseTime = this.timeList[0].month;
                this.tableData  = this.timeList[0].priceList;

            }

            




        },

        changeTime() {
            this.tableData = this.timeList.find(item => item.month === this.chooseTime).priceList;
        },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }
   

     
  .device-head {
    background-color: #fff;
    
    display: flex;
    height: 112px;
    display: flex;
    align-items: center;
    padding: 0 24px;
    box-sizing: border-box;
    .device-head-icon {
        width: 48px;
        height: 48px;
        margin-right: 24px;
    }
    .device-info-wrap {
        flex: 1;
        .device-title-wrap {
            height: 32px;
            display: flex;
            align-items: center;
            .device-title {
                font-weight: 500;
                font-size: 24px;
                color: #12151A;
            }
            .device-status {
                // width: 50px;
                padding: 0 10px;
                height: 24px;
                border-radius: 10px 0 10px 0;
                font-size: 16px;
                font-weight: 400;
                line-height: 24px;
                text-align: center;
                color: #fff;
                background: linear-gradient(321.01deg, #FFB624 8.79%, #FF8D24 100.27%);
                margin-left: 12px;
            }
        }
        .device-info-wrap {
            height: 16px;
            margin-top: 16px;
            font-size: 16px;
            font-weight: 400;
            color: #292B33;
        }
    }
    .device-status-wrap {
        display: flex;
        align-items: center;
        .device-status-item-wrap {
            width: 150px;
            .device-status-item-title {
                font-weight: 400;
                font-size: 14px;
                line-height: 14px;
                color: #505363;
                margin: 0 auto 12px auto;
                text-align: center;
            }
            .device-status {
                width: 86px;
                height: 34px;
                border-radius: 4px;
                display: flex;
                margin: 0 auto;
                align-items: center;
                justify-content: center;
                background-color: #EBF3FF;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 18px;
                color: #217AFF;
            }
            .device-status-success {
                width: 86px;
                height: 34px;
                border-radius: 4px;
                display: flex;
                margin: 0 auto;
                align-items: center;
                justify-content: center;
                background-color: #EBFFF1;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 18px;
                color: #00C864;

            }
        }
        .device-status-split{
            width: 1px;
            height: 36px;
            background-color: #E9EBF0;
        }
    }
    
  
  }

  .info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    background: linear-gradient(180deg, #E9F2FF 0%, #FFFFFF 100%);
    .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
    }
    .card-head-text {
        flex:1;
        font-weight: 500;
        font-size: 16px;
        color: #12151A;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }

   
    }

    .button-wrap {
      display: flex;
      .invite-btn {
          background-color: #1ab2ff;
          border-color: #1ab2ff;
        }
        ::v-deep .el-button--small {
          font-size: 14px;
        }
      .distribution {
          margin-left: 24px;
          margin-right: 24px;
          display: flex;
          align-items: center;
        }
    }
  }
  
  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
      padding: 0 16px 0 16px;
      .info-title {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #505363;
      }
      .info-detail {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #292B33;
        display: flex;
        .info-price {
            font-weight: 400;
            color: #FF8D24;
            margin-right: 5px;
        }
      }
      .info-amount {
        height: 28px;
        background-color: #FFF7E6;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 8px;
        font-weight: 400;
        font-size: 20px;
        color: #FE8921;
        margin-top: -6px;
        margin-right: 4px;

      }
      .info-img {
        width: 140px;
        height: 140px;
      }
    }
  }


  ::v-deep .bd3001-content {
    padding: 0;
  }


  .container {
      position: relative;
      padding-bottom: 100px;
      box-sizing: border-box;
      .bottom-wrap {
          position: fixed;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 86px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          background-color: #FFFFFF;
          padding-right: 32px;
          box-sizing: border-box;
          z-index: 100;
      }
  }


 
  </style>
  