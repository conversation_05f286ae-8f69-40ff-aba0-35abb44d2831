import request from '@/utils/request'

const baseUrl = '/vehicle-charging-admin'

// 运营商列表
export function getOperatorsList(data) {
  return request({
    url: baseUrl + '/operators/page',
    method: 'post',
    data: data
  })
}

// 新增
export function createOperators(data) {
  return request({
    url: baseUrl + '/operators/create',
    method: 'post',
    data: data
  })
}

// 更新
export function updateOperators(data) {
  return request({
    url: baseUrl + '/operators/update',
    method: 'post',
    data: data
  })
}

// 详情
export function getOperatorsDetail(query) {
  return request({
    url: baseUrl + '/operators/detail/'+ query.operatorId,
    method: 'get',
    // params: query
  })
}

// 启用
export function enableOperators(query) {
  return request({
    url: baseUrl + '/operators/enable',
    method: 'get',
    params: query
  })
}

// 禁用
export function disableOperators(query) {
  return request({
    url: baseUrl + '/operators/disable',
    method: 'get',
    params: query
  })
}

// 删除
export function deleteOperators(query) {
  return request({
    url: baseUrl + '/operators/delete',
    method: 'get',
    params: query
  })
}



// 运营商账户查询
export function getOperatorsAccount(query) {
  return request({
    url: baseUrl + '/withdrawal/operatorAccountQuery',
    method: 'post',
    data: query
  })
}

// 运营商流水分页查询
export function getOperatorsFlow(query) {
  return request({
    url: baseUrl + '/withdrawal/operatorAccountPage',
    method: 'post',
    data: query
  })
}

// 账户充值
export function rechargeOperators(query) {
  return request({
    url: baseUrl + '/withdrawal/recharge',
    method: 'post',
    data: query
  })
}

// 绑定银行卡
export function bindBankCard(query) {
  return request({
    url: baseUrl + '/withdrawal/bind',
    method: 'post',
    data: query
  })
}

// 提现分页
export function getWithdrawalPage(query) {
  return request({
    url: baseUrl + '/withdrawal/page',
    method: 'post',
    data: query
  })
}

// 提现
export function addWithdrawal(query) {
  return request({
    url: baseUrl + '/withdrawal/update',
    method: 'post',
    data: query
  })
}

// 上传凭证
export function uploadVoucher(query) {
  return request({
    url: baseUrl + '/withdrawal/uploadWithdraw',
    method: 'post',
    data: query
  })
}

// 获取提现详情
export function getWithdrawalDetail(query) {
  return request({
    url: baseUrl + '/withdrawal/getOne',
    method: 'post',
    data: query
  })
}