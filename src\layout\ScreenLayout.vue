<template>
  <div class="screen-layout">
    <app-main />
  </div>
</template>

<script>
import { AppMain} from "./components";

export default {
  name: "Layout",
  components: {
    AppMain,
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === "mobile",
      };
    },
  },
  methods: {
  },
};
</script>

<style lang="scss" scoped>
.screen-layout {

}
</style>
