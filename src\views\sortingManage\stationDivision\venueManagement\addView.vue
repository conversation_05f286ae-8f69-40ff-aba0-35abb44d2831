<template>
  <div class="container">
    <!-- 基础信息卡片 -->
    <div class="info-card">
      <div class="card-head" style="margin-top: 0">
        <div class="before-icon"></div>
        <div class="card-head-text">基础信息</div>
      </div>
      <div class="form-content" style="padding-top: 0">
        <el-form
          :model="baseInfo"
          :rules="rules"
          ref="baseInfoForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="地市" prop="city">
                <el-select v-model="baseInfo.city" placeholder="请选择">
                  <el-option label="长沙市" value="长沙市"></el-option>
                  <el-option label="株洲市" value="株洲市"></el-option>
                  <el-option label="湘潭市" value="湘潭市"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="充电站" prop="station">
                <el-select v-model="baseInfo.station" placeholder="请选择">
                  <el-option label="充电站1" value="充电站1"></el-option>
                  <el-option label="充电站2" value="充电站2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="分成方类型" prop="partyType">
                <el-select v-model="baseInfo.partyType" placeholder="请选择">
                  <el-option label="场地方" value="场地方"></el-option>
                  <el-option label="运营方" value="运营方"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="分成方" prop="party">
                <el-select v-model="baseInfo.party" placeholder="请选择">
                  <el-option label="长沙市" value="长沙市"></el-option>
                  <el-option label="株洲市" value="株洲市"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="分成类型" prop="type">
                <el-select v-model="baseInfo.type" placeholder="请选择">
                  <el-option label="固定费" value="固定费"></el-option>
                  <el-option label="比例分成" value="比例分成"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="分成周期" prop="period">
                <el-date-picker
                  v-model="baseInfo.period"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 100%"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="分成条款" prop="sharingNo">
                <el-input
                  v-model="baseInfo.sharingNo"
                  placeholder="请输入分成条款"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <!-- 编辑明细数据 -->
    <div class="info-card">
      <div class="card-head" style="margin-top: 0">
        <div class="before-icon"></div>
        <div class="card-head-text">编辑明细数据</div>
      </div>
      <div class="table-container">
        <BuseCrud
          ref="crud"
          :loading="loading"
          :tableColumn="tableColumn"
          :tableData="tableData"
          :modalConfig="modalConfig"
        ></BuseCrud>
      </div>
    </div>

    <div class="bottom-wrap">
      <el-button @click="() => handleCancel()">取消</el-button>
      <el-button type="primary" @click="() => handleConfirm()">保存</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SharingDetail',
  data() {
    return {
      id: null,
      baseInfo: {
        city: '长沙市',
        station: '充电站1',
        partyType: '场地方',
        party: '长沙市',
        type: '固定费',
        period: ['2023-01-01', '2023-01-31'],
        sharingNo: 'SN1000',
      },
      // 表单验证规则
      rules: {
        city: [{ required: true, message: '请选择地市', trigger: 'change' }],
        station: [
          { required: true, message: '请选择充电站', trigger: 'change' },
        ],
        partyType: [
          { required: true, message: '请选择分成方类型', trigger: 'change' },
        ],
        party: [{ required: true, message: '请选择分成方', trigger: 'change' }],
        // 分成类型不是必填项
        period: [
          { required: true, message: '请选择分成周期', trigger: 'change' },
        ],
        sharingNo: [
          { required: true, message: '请输入分成条款', trigger: 'blur' },
        ],
      },
      tableData: [
        {
          chargingPower: 7000,
          chargingFee: 1000,
          internalChargingFee: '-',
          externalChargingFee: '-',
          serviceFee: 800,
          internalServiceFee: '-',
          externalServiceFee: '-',
          totalIncome: 1800,
          supplyPower: 7200,
          supplyFee: 1100,
          powerLossPower: 100,
          powerLossType: '估算电损',
          powerLossFee: 100,
          operationCost: '-',
          platformFee: '-',
          siteFee: '-',
          sharingPartyLossFee: '-',
          pendingSharingFee: 500,
          settlementElectricityFee: '-',
          settlementServiceFee: 150,
          settlementRentFee: '-',
          settlementAmount: 150,
        },
      ],
      loading: false,
      modalConfig: { addBtn: false, menu: false },
      // 表格列配置
      tableColumn: [
        {
          field: 'chargingPower',
          title: '充电总电量(KWH)',
          minWidth: 160,
          align: 'center',
        },
        {
          field: 'chargingFee',
          title: '充电电费(元)',
          minWidth: 120,
          align: 'center',
        },
        {
          field: 'internalChargingFee',
          title: '内部车辆充电电费(元)',
          minWidth: 180,
          align: 'center',
        },
        {
          field: 'externalChargingFee',
          title: '外部车辆充电电费(元)',
          minWidth: 180,
          align: 'center',
        },
        {
          field: 'serviceFee',
          title: '充电服务费(元)',
          minWidth: 140,
          align: 'center',
        },
        {
          field: 'internalServiceFee',
          title: '内部车辆充电服务费(元)',
          minWidth: 200,
          align: 'center',
        },
        {
          field: 'externalServiceFee',
          title: '外部车辆充电服务费(元)',
          minWidth: 200,
          align: 'center',
        },
        {
          field: 'totalIncome',
          title: '充电总收入(元)',
          minWidth: 140,
          align: 'center',
        },
        {
          field: 'supplyPower',
          title: '供电电量(KWH)',
          minWidth: 140,
          align: 'center',
        },
        {
          field: 'supplyFee',
          title: '供电电费(元)',
          minWidth: 120,
          align: 'center',
        },
        {
          field: 'powerLossPower',
          title: '电损电量(KWH)',
          minWidth: 140,
          align: 'center',
        },
        {
          field: 'powerLossType',
          title: '电损计量类型',
          minWidth: 120,
          align: 'center',
        },
        {
          field: 'powerLossFee',
          title: '电损电费(元)',
          minWidth: 120,
          align: 'center',
        },
        {
          field: 'operationCost',
          title: '运维成本(元)',
          minWidth: 120,
          align: 'center',
        },
        {
          field: 'platformFee',
          title: '平台服务费(元)',
          minWidth: 140,
          align: 'center',
        },
        {
          field: 'siteFee',
          title: '场地租金(元)',
          minWidth: 120,
          align: 'center',
        },
        {
          field: 'sharingPartyLossFee',
          title: '分成方承担损耗电费(元)',
          minWidth: 200,
          align: 'center',
        },
        {
          field: 'pendingSharingFee',
          title: '待分成费用(元)',
          minWidth: 140,
          align: 'center',
        },
        {
          field: 'settlementElectricityFee',
          title: '结算电费(元)',
          minWidth: 120,
          align: 'center',
        },
        {
          field: 'settlementServiceFee',
          title: '结算服务费(元)',
          minWidth: 140,
          align: 'center',
        },
        {
          field: 'settlementRentFee',
          title: '结算租金(元)',
          minWidth: 120,
          align: 'center',
        },
        {
          field: 'settlementAmount',
          title: '结算金额(元)',
          minWidth: 120,
          align: 'center',
        },
      ],
    };
  },
  created() {
    this.id = this.$route.query.id;
    if (this.id) {
      this.getDetailData();
    }
  },
  methods: {
    // 获取详情数据
    getDetailData() {
      // 模拟API请求
      // 实际项目中应该调用真实的API
      console.log('获取ID为', this.id, '的详情数据');
    },

    // 返回上一页
    handleCancel() {
      this.$router.go(-1);
    },

    // 保存
    handleConfirm() {
      this.$refs.baseInfoForm.validate(async (valid) => {
        if (valid) {
          this.$message.success('提交成功！');
          THIS.handleCancel();
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .bottom-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
    z-index: 5;
  }
}

.info-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  padding: 0 0 20px 0;
}

.card-head {
  height: 56px;
  padding: 0 16px;
  display: flex;
  align-items: center;

  .before-icon {
    width: 3px;
    height: 16px;
    background-image: url('~@/assets/station/consno-before.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-right: 8px;
  }

  .card-head-text {
    font-weight: 500;
    font-size: 16px;
    color: #12151a;
  }
}

.card-title {
  padding: 15px 20px;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
}

.form-content {
  padding: 20px;
}

.form-item {
  margin-bottom: 20px;
}

.form-label {
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.table-container {
  padding: 0 20px;
  overflow-x: auto;
}

.bottom-buttons {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  gap: 20px;
}

::v-deep .el-table {
  .cell {
    padding: 8px 5px;
  }

  th.is-leaf {
    font-weight: 500;
  }
}

::v-deep .el-select {
  width: 100%;
}

::v-deep .el-input.is-disabled .el-input__inner,
::v-deep .el-select .el-input.is-disabled .el-input__inner,
::v-deep .el-date-editor.is-disabled .el-range-input {
  color: #606266;
  background-color: #f5f7fa;
}
</style>
