<template>
    <div>
      <!-- <a-spin v-if="isLoad" fix>
        <a-icon type="ios-loading" size="18" class="demo-spin-icon-load"></a-icon>
      </a-spin> -->
      <div
        id="container"
        :class="className"
        :style="{
          width: width,
          height: height,
        }"
      >
        <div class="search" :style="{ left: isNeedFullscreen ? '50px' : '10px' }">
          <input :disabled="disabled" type="text" id="searchInput" :placeholder="disabled ? '' : '请输入搜索关键字'" />
          <div id="searchres" />
        </div>
  
        <div class="coordinate" :style="{ right: isNeedFullscreen ? '50px' : '10px' }">
          <input
            :disabled="disabled"
            type="text"
            id="coordinateinput"
            :placeholder="disabled ? '' : '坐标值'"
            @change="handleLatClick"
          />
        </div>
  
        <div class="fullscreen" v-if="isNeedFullscreen">
          <Tooltip transfer content="展开到全屏">
            <mat-icon size="24" style="line-height: 30px" @click="toggleFulle">{{
              fullscreen ? 'fullscreen_exit' : 'fullscreen'
            }}</mat-icon>
          </Tooltip>
        </div>
      </div>
    </div>
  </template>
  <script>
  import AMapLoader from '@/utils/map/AMapLoader';
  
  export default {
    name: 'sm-map',
    props: {
      //禁用
      disabled: {
        type: Boolean,
        default: false,
      },
  
      // 初始化中心点
      center: Array,
      // 级别
      zoom: { type: Number, default: 15 },
      // 标记点坐标
      // https://lbs.amap.com/api/javascript-api/reference/overlay#marker
      markers: [],
      // 绘制图形
      cover: {},
      width: { type: [String, Number], default: '100%' },
      height: { type: [String, Number], default: '100%' },
      className: String,
      isNeedAutoComplete: { type: Boolean, default: false },
      isNeedMouseTool: { type: Boolean, default: false },
      mouseToolType: {
        defaults: 'polygon',
        validator: (value) => {
          return ['polyline', 'polygon', 'rectangle', 'circle'].indexOf(value) !== -1;
        },
      },
      isNeedFullscreen: { type: Boolean, default: false },
    },
    data() {
      return {
        map: null,
        key: 'd36a13128cb9b8ee92bcd0567098dfa6',
        isLoad: true,
        geocoder: null,
        mouseTool: null,
        AMap: null,
        polygons: [],
        handler: null,
        markerIcon: require('@/assets/images/poi-marker-default.png'),
      };
    },
    watch: {
      center(n) {
        this.setLatLon(n);
      },
    },
    mounted() {
      this.map && this.map.destroy();
      this.initMap();
    },
    beforeDestroy() {
      this.map && this.map.destroy();
      window.removeEventListener('fullscreenchange', this.handler);
    },
    methods: {
      handleLatClick(e) {
        // console.log('❤️❤️arguments', arguments);
        let mapArr = e.target.value.split(',');
        if (mapArr.length !== 2 || isNaN(parseFloat(mapArr[0])) || isNaN(parseFloat(mapArr[1]))) {
          this.$message.error('请输入正确的经纬度，英文逗号隔开');
        }
        this.setCenter([parseFloat(mapArr[0]), parseFloat(mapArr[1])]);
        this.setAndClick(parseFloat(mapArr[0]), parseFloat(mapArr[1]));
      },
  
      initMap() {
        this.map && this.map.destroy();
        let { key, zoom, center } = this;
        window._AMapSecurityConfig = {
            securityJsCode: 'cba362edb71c5c1f2806ddd1297c544a',
        };
        AMapLoader.load()
          .then((AMap) => {
            this.AMap = AMap;
            const params = {
              resizeEnable: true, // 监听容器尺寸变化
              zoom,
              plugin: ['AMap.MouseTool'],
            };
            if (center && center[0] && center[1]) {
              params.center = center;
            }
            this.map = new AMap.Map('container', params);
            // this.map.setMapStyle('amap://styles/darkblue');
            this.onInit(AMap);
            !this.fisNeedMouseTool && this.onBindClick();
            this.onBindGeocoder(AMap);
            this.isNeedAutoComplete && this.onBindSearch(AMap);
          })
          .catch((e) => {
            console.log(e);
            window.location.reload();
          });
      },
      onInit(AMap) {
        this.map.on('complete', () => {
          this.isLoad = false;
          this.setLatLon(this.center);
          this.$emit('onComplete', {
            map: this.map,
            AMap,
          });
        });
      },
      setLatLon(center) {
        if (center && center[0] && center[1]) {
          this.createMarker([{ position: center }]);
          document.getElementById('coordinateinput').value = `${center[0]},${center[1]}`;
        }
      },
      onBindClick() {
        if (this.disabled) {
          return;
        }
        this.map.on('click', (e) => {
          const { lat = '', lng = '' } = e?.lnglat || {};
          this.setAndClick(lng, lat);
        });
      },
      setAndClick(lng, lat) {
        this.geocoder.getAddress([lng, lat], (status, result) => {
          let address = '';
          if (result.info === 'OK') {
            // console.log('❤️❤️result', result);
            address = result?.regeocode?.formattedAddress;
            const { adcode, city, province, district } = result?.regeocode?.addressComponent || {};
            if (address.includes(province + city + district)) {
              address = address.replace(province + city + district, '');
            }
            let areaCode = {};
            if (adcode) {
              areaCode = {
                province: adcode.substring(0, 2) + '0000',
                city: adcode.substring(0, 4) + '00',
                district: adcode,
              };
            }
            document.getElementById('searchInput').value = address;
            this.createMarker([{ position: [lng, lat] }]);
            this.$emit('click', {
              longitude: lng,
              latitude: lat,
              address,
              areaCode,
            });
            document.getElementById('coordinateinput').value = `${lng},${lat}`;
          }
        });
      },
      onBindSearch(AMap) {
        AMap.plugin(['AMap.AutoComplete', 'AMap.PlaceSearch'], () => {
          let autocomplete = new AMap.AutoComplete({
            input: 'searchInput',
            output: 'searchres',
          });
          let placeSearch = new AMap.PlaceSearch();
          autocomplete.on('select', (e) => {
            // console.log('❤️❤️poi', e);
            placeSearch.setCity(e.poi.adcode);
            placeSearch.search(e.poi.name); //关键字查询查询
            const location = e.poi?.location;
            if (!location) {
              this.$message.info('选择更详细地址信息');
              return;
            }
            const { lat, lng } = location;
            this.setCenter([lng, lat]);
            this.map.setZoom(16);
            this.setAndClick(lng, lat);
            // this.createMarker([{ position: [lng, lat] }]);
            // this.$emit('click', {
            //   longitude: lng,
            //   latitude: lat,
            //   address: e.poi?.name || '',
            // });
            document.getElementById('coordinateinput').value = `${lng},${lat}`;
          });
        });
      },
      onBindGeocoder(AMap) {
        AMap.plugin('AMap.Geocoder', () => {
          this.geocoder = new AMap.Geocoder();
        });
      },
      createMarker(markers) {
        if (!this.map) {
          return;
        }
        this.map.clearMap();
        markers.forEach((element) => {
          if (element.position && element.position.length === 2) {
            element.position[0] = element.position[0] ? Number(element.position[0]) : 0;
            element.position[1] = element.position[1] ? Number(element.position[1]) : 0;
            this.map.add(
              new this.AMap.Marker({
                position: element.position,
                icon: this.markerIcon,
                size: new this.AMap.Size(40, 40),
                offset: new this.AMap.Pixel(-10, -30),
              })
            );
            this.map.setFitView();
          }
        });
      },
      setCenter(position) {
        this.map.setCenter(position);
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  #container {
    width: 100vw;
    height: 700px !important;
    margin: 0 auto;
  }
  .search {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 1;
    #searchInput {
      width: 180px;
      height: 30px;
      border: none;
      border-radius: 4px;
      padding: 0 5px;
      &:focus {
        outline: none;
      }
    }
    #searchres {
      width: 180px;
      height: auto;
      background: #fff;
      overflow: hidden;
      line-height: 15px;
    }
  }
  
  .coordinate {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 1;
    #coordinateinput {
      width: 180px;
      height: 30px;
      border: none;
      border-radius: 4px;
      padding: 0 5px;
      &:focus {
        outline: none;
      }
    }
  }
  .fullscreen {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 9999;
  }
  </style>
  