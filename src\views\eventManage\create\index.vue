<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <el-form
      :model="baseInfo.form"
      :rules="baseInfo.rules"
      ref="baseInfoForm"
      label-position="top"
    >
      <div class="info-card">
        <div class="card-head">
          <div class="before-icon"></div>
          <div class="card-head-text">基础信息</div>
        </div>
        <div class="card-head-split"></div>
        <div class="form-wrap">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item
                label="活动ID"
                prop="timePeriodId"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.timePeriodId"
                  placeholder="自动生成"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="活动名称"
                prop="periodName"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.periodName"
                  placeholder="请输入活动名称"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="展示标题"
                prop="periodName"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.periodName"
                  placeholder="请输入展示标题"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="活动类型"
                prop="activeType"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.activeType"
                  placeholder="请选择活动类型"
                  style="width: 100%"
                  @change="activeTypeChange"
                >
                  <el-option
                    v-for="item in activeTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item
                label="活动起始时间"
                prop="periodName"
                :label-width="formLabelWidth"
              >
                <el-date-picker
                  v-model="baseInfo.form.periodName"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 100%"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="活动单位"
                prop="periodName"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.periodName"
                  placeholder="请输入活动单位"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="活动区域"
                prop="createUnit"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.createUnit"
                  placeholder="请选择活动区域"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dict.type.ls_charging_contracted_unit"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item
                label="活动描述"
                prop="periodName"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.periodName"
                  placeholder="请输入活动描述"
                  type="textarea"
                  :rows="3"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="info-card">
        <div class="card-head">
          <div class="before-icon"></div>
          <div class="card-head-text">奖品配置</div>
        </div>
        <div class="card-head-split"></div>
        <div class="table-wrap">
          <BuseCrud
            ref="crud"
            :tableColumn="tableColumnConfig"
            :tableData="tableData"
            :modalConfig="modalConfig"
          >
            <template slot="defaultHeader">
              <div>
                <div class="card-head">
                  <div class="top-button-wrap">
                    <el-button type="primary" @click="() => handleAdd()">
                      新增
                    </el-button>
                  </div>
                </div>
              </div>
            </template>
            <template slot="operate" slot-scope="{ row }">
              <div class="menu-box">
                <el-button type="primary" plain>修改</el-button>
                <el-button v-if="row.showDelBtn" type="danger" plain>
                  删除
                </el-button>
              </div>
            </template>
          </BuseCrud>
        </div>
      </div>
      <div class="info-card">
        <div class="card-head">
          <div class="before-icon"></div>
          <div class="card-head-text">活动规则</div>
        </div>
        <div class="card-head-split"></div>
        <el-tabs v-model="rulerTab" @tab-click="handleTabsClick">
          <el-tab-pane label="用户管理" name="bussinessRuler">
            <div class="form-wrap form-wrap-top">
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item
                    label="使用范围"
                    prop="scopeOfUse"
                    :label-width="formLabelWidth"
                  >
                    <el-radio-group v-model="baseInfo.form.scopeOfUse">
                      <el-radio
                        v-for="(item, index) in scopeOfUseList"
                        :label="item.value"
                        :key="index"
                      >
                        {{ item.label }}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item
                    label=""
                    prop="limitations"
                    :label-width="formLabelWidth"
                  >
                    <el-radio-group v-model="baseInfo.form.limitations">
                      <el-radio
                        v-for="(item, index) in limitationsList"
                        :label="item.value"
                        :key="index"
                      >
                        {{ item.label }}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <div
                    class="table-wrap"
                    v-if="baseInfo.form.limitations !== '1'"
                  >
                    <div
                      class="upload-wrap"
                      v-if="baseInfo.form.limitations === '3'"
                    >
                      <el-upload
                        class="upload-demo"
                        drag
                        action="https://jsonplaceholder.typicode.com/posts/"
                        multiple
                      >
                        <div class="upload-icon"></div>
                        <div class="upload-text">
                          点击或将文件拖拽到这里上传
                        </div>
                        <div class="upload-tip">支持扩展名：.xlsx .xls</div>
                      </el-upload>
                      <div class="upload-info">
                        请 
                        <block style="color: #217aff" @click="onClickTemplate">
                          点此下载Excel模板
                        </block>
                        ，
                        并按照模板进行填写后上传，模板数据不超过500条，导入过程请耐心等待
                      </div>
                    </div>
                    <BuseCrud
                      ref="crud"
                      :tableColumn="stationTableColumnConfig"
                      :tableData="stationTableData"
                      :modalConfig="modalConfig"
                    >
                      <template slot="defaultHeader">
                        <div>
                          <div class="card-head">
                            <div class="top-button-wrap">
                              <el-button
                                v-if="baseInfo.form.limitations === '2'"
                                type="primary"
                                @click="() => handleChoose()"
                              >
                                {{ chooseNameFilter }}
                              </el-button>
                              <div class="choose-box">
                                已选择
                                <span>10</span>
                                个充电站,
                                <span>100</span>
                                个充电桩
                              </div>
                            </div>
                          </div>
                        </div>
                      </template>
                      <template slot="operate" slot-scope="{ row }">
                        <div class="menu-box">
                          <el-button v-if="row.showDelBtn" type="danger" plain>
                            删除
                          </el-button>
                        </div>
                      </template>
                    </BuseCrud>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
          <el-tab-pane label="配置管理" name="userRuler">
            <div class="form-wrap form-wrap-top">
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item
                    label="用户类型"
                    prop="userType"
                    :label-width="formLabelWidth"
                  >
                    <el-radio-group v-model="baseInfo.form.userType">
                      <el-radio
                        v-for="(item, index) in userTypeList"
                        :label="item.value"
                        :key="index"
                      >
                        {{ item.label }}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <div class="table-wrap" v-if="baseInfo.form.userType === '3'">
                  <div class="upload-wrap">
                    <el-upload
                      class="upload-demo"
                      drag
                      action="https://jsonplaceholder.typicode.com/posts/"
                      multiple
                    >
                      <div class="upload-icon"></div>
                      <div class="upload-text">点击或将文件拖拽到这里上传</div>
                      <div class="upload-tip">支持扩展名：.xlsx .xls</div>
                    </el-upload>
                    <div class="upload-info">
                      请 
                      <block style="color: #217aff" @click="onClickTemplate">
                        点此下载Excel模板
                      </block>
                      ，
                      并按照模板进行填写后上传，模板数据不超过500条，导入过程请耐心等待
                    </div>
                  </div>
                  <BuseCrud
                    ref="crud"
                    :tableColumn="userTableColumn"
                    :tableData="userTableData"
                    :modalConfig="modalConfig"
                  >
                    <template slot="defaultHeader">
                      <div>
                        <div class="card-head">
                          <div class="top-button-wrap">
                            <div class="choose-box">
                              圈选
                              <span>10</span>
                              个用户
                            </div>
                          </div>
                        </div>
                      </div>
                    </template>
                    <template slot="operate" slot-scope="{ row }">
                      <div class="menu-box">
                        <el-button v-if="row.showDelBtn" type="danger" plain>
                          删除
                        </el-button>
                      </div>
                    </template>
                  </BuseCrud>
                </div>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item
                    label="是否新用户注册"
                    prop="newUserRegister"
                    :label-width="formLabelWidth"
                  >
                    <el-radio-group v-model="baseInfo.form.newUserRegister">
                      <el-radio
                        v-for="(item, index) in newUserRegisterList"
                        :label="item.value"
                        :key="index"
                      >
                        {{ item.label }}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <div class="flex-end">
                    <el-form-item
                      label="常用充电区域"
                      prop="chargingArea"
                      :label-width="formLabelWidth"
                    >
                      <el-radio-group v-model="baseInfo.form.chargingArea">
                        <el-radio
                          v-for="(item, index) in chargingAreaList"
                          :label="item.value"
                          :key="index"
                        >
                          {{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item
                      v-if="baseInfo.form.chargingArea === '2'"
                      label=""
                      prop="activeType"
                      :label-width="formLabelWidth"
                    >
                      <el-select
                        style="margin-left: 20px"
                        v-model="baseInfo.form.activeType"
                        placeholder="请选择"
                        multiple
                      >
                        <el-option
                          v-for="item in activeTypeList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </div>
                </el-col>
                <el-col :span="24">
                  <div class="flex-end">
                    <el-form-item
                      label="充电情况"
                      prop="chargingSituation"
                      :label-width="formLabelWidth"
                    >
                      <el-radio-group v-model="baseInfo.form.chargingSituation">
                        <el-radio
                          v-for="(item, index) in chargingSituationList"
                          :label="item.value"
                          :key="index"
                        >
                          {{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <div
                      class="flex-end"
                      style="margin-left: 10px"
                      v-if="baseInfo.form.chargingSituation !== '1'"
                    >
                      <el-form-item
                        label=""
                        prop="activeType"
                        :label-width="formLabelWidth"
                      >
                        <span style="margin-right: 4px">近</span>
                        <el-input
                          style="width: 70% !important"
                          v-model="baseInfo.form.activeType"
                          placeholder="请输入"
                        ></el-input>
                        <span style="margin-left: 4px">天达到</span>
                      </el-form-item>

                      <el-form-item
                        label=""
                        prop="activeType"
                        :label-width="formLabelWidth"
                      >
                        <el-input
                          v-model="baseInfo.form.activeType"
                          placeholder="请输入"
                        ></el-input>
                      </el-form-item>
                      <el-form-item
                        label=""
                        prop="activeType"
                        :label-width="formLabelWidth"
                      >
                        <el-select
                          style="margin-left: 20px"
                          v-model="baseInfo.form.activeType"
                          placeholder="请选择单位"
                        >
                          <el-option
                            v-for="item in activeTypeList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
          <el-tab-pane label="角色管理" name="limitRuler">
            <div class="form-wrap form-wrap-top">
              <el-row :gutter="20">
                <el-col :span="24">
                  <div class="flex-end">
                    <el-form-item
                      label="活动限制"
                      prop="activityLimitation"
                      :label-width="formLabelWidth"
                    >
                      <el-radio-group
                        v-model="baseInfo.form.activityLimitation"
                      >
                        <el-radio
                          v-for="(item, index) in activityLimitationList"
                          :label="item.value"
                          :key="index"
                        >
                          {{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </div>
                </el-col>
                <el-col :span="24">
                  <div
                    class="flex-end"
                    style="margin-left: 10px"
                    v-if="baseInfo.form.activityLimitation !== '1'"
                  >
                    <el-form-item
                      label=""
                      prop="activeType"
                      :label-width="formLabelWidth"
                    >
                      <span style="margin-right: 4px">活动期间每个用户</span>
                      <el-select
                        v-model="baseInfo.form.activeType"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in activeTypeList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>

                    <el-form-item
                      label=""
                      prop="activeType"
                      :label-width="formLabelWidth"
                    >
                      <span style="margin: 0 4px 0 10px">限制参与</span>

                      <el-input
                        style="width: 60% !important"
                        v-model="baseInfo.form.activeType"
                        placeholder="请输入"
                      ></el-input>
                      <span style="margin-left: 4px">次</span>
                    </el-form-item>
                  </div>
                </el-col>
                <el-col :span="24">
                  <div
                    class="flex-end"
                    style="margin-left: 10px"
                    v-if="baseInfo.form.activityLimitation !== '1'"
                  >
                    <el-form-item
                      label=""
                      prop="activeType"
                      :label-width="formLabelWidth"
                    >
                      <span style="margin-right: 4px">活动期间限制</span>
                      <el-select
                        v-model="baseInfo.form.activeType"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in activeTypeList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>

                    <el-form-item
                      label=""
                      prop="activeType"
                      :label-width="formLabelWidth"
                    >
                      <span style="margin: 0 4px 0 10px">发放优惠券</span>

                      <el-input
                        style="width: 60% !important"
                        v-model="baseInfo.form.activeType"
                        placeholder="请输入"
                      ></el-input>
                      <span style="margin-left: 4px">张</span>
                    </el-form-item>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <div class="bottom-wrap">
        <div>
          <el-button @click="handleCancel">取消</el-button>
          <el-button
            @click="handleSave('save')"
            type="primary"
            v-if="approvalStatus !== '02'"
          >
            保存
          </el-button>
          <el-button @click="handleSave('audit')" type="primary">
            提交审核
          </el-button>
        </div>
      </div>
    </el-form>
    <addPrizes ref="addPrizes" @orderRuleAdd="addTableData"></addPrizes>
    <addNewPrizes
      ref="addNewPrizes"
      @orderRuleAdd="addTableData"
    ></addNewPrizes>
    <addDiscounts
      ref="addDiscounts"
      @orderRuleAdd="addTableData"
    ></addDiscounts>
    <chooseStationModal ref="chooseStationModal" @confirm="chooseTableData" />
    <choosePileModal ref="choosePileModal" @confirm="chooseTableData" />
    <chooseInstitutionModal
      ref="chooseInstitutionModal"
      @confirm="chooseTableData"
    />
  </div>
</template>

<script>
import {
  getAreaList,
  addElectricPricePeriod,
  getElectricPricePeriodDetail,
  updateElectricPricePeriod,
} from '@/api/electricPricePeriod/index';
import addPrizes from '../components/addPrizes';
import addDiscounts from '../components/addDiscounts';
import addNewPrizes from '../components/addNewPrizes';
import choosePileModal from '../components/choosePileModal';
import chooseStationModal from '../components/chooseStationModal';
import chooseInstitutionModal from '../components/chooseInstitutionModal';

export default {
  components: {
    addPrizes,
    addDiscounts,
    addNewPrizes,
    choosePileModal,
    chooseStationModal,
    chooseInstitutionModal,
  },
  dicts: ['ls_charging_contracted_unit'],
  data() {
    return {
      type: 'create',
      formLabelWidth: '120px',
      areaList: [],
      chargePeriodId: '', // 时段id
      approvalStatus: '', // 审批状态
      baseInfo: {
        form: {
          timePeriodId: '',
          periodName: '',
          suitCityCode: '',
          createUnit: '',
          activeType: '',
          scopeOfUse: '1',
          limitations: '1',
          userType: '1',
          newUserRegister: '1',
          chargingArea: '1',
          chargingSituation: '1',
          activityLimitation: '1',
        },
        rules: {
          activeType: [
            { required: true, message: '请选择活动类型', trigger: 'blur' },
          ],
          periodName: [
            { required: true, message: '请输入活动名称', trigger: 'blur' },
          ],
          suitCityCode: [
            { required: true, message: '请选择活动类型', trigger: 'blur' },
          ],
          createUnit: [
            { required: true, message: '请选择活动区域', trigger: 'blur' },
          ],
        },
      },
      periodDetailList: [
        {
          month: this.getCurrentMonth(),
          priceList: [{ priceType: '1', startTime: '', endTime: '' }],
          active: true,
        },
      ],
      periodInfoRules: {
        month: [{ required: true, message: '请选择月份', trigger: 'blur' }],
        priceType: [
          { required: true, message: '请选择电价区间', trigger: 'blur' },
        ],
        // startTime: [
        //     { required: true, message: '请选择开始时间', trigger: 'change' },
        // ],
        // endTime: [
        //     { required: true, message: '请选择结束时间', trigger: 'change' },
        // ],
      },
      priceTypeList: [
        { label: '尖', value: '1' },
        { label: '峰', value: '2' },
        { label: '平', value: '3' },
        { label: '谷', value: '4' },
      ],
      activeTypeList: [
        { label: '领券活动', value: '1' },
        { label: '定向发券活动', value: '2' },
        { label: '立减活动', value: '3' },
      ],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'periodName',
          title: '优惠券编码',
          minWidth: 150,
        },
        {
          field: 'validMonthSummary',
          title: '优惠券名称',
          minWidth: 150,
        },
        {
          field: 'createBy',
          title: '投放库存',
          minWidth: 120,
        },

        {
          title: '操作',
          slots: { default: 'operate' },
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],
      reductionTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'periodName',
          title: '立减时间段',
          minWidth: 150,
        },
        {
          field: 'validMonthSummary',
          title: '优惠范围',
          minWidth: 150,
        },
        {
          field: 'createBy',
          title: '立减方式',
          minWidth: 120,
        },

        {
          title: '操作',
          slots: { default: 'operate' },
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],
      userTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'periodName',
          title: '用户ID',
          minWidth: 150,
        },
        {
          field: 'validMonthSummary',
          title: '用户昵称',
          minWidth: 150,
        },
        {
          field: 'createBy',
          title: '手机号码',
          minWidth: 120,
        },

        {
          title: '操作',
          slots: { default: 'operate' },
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],
      stationTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'periodName',
          title: '充电站编号',
          minWidth: 150,
        },
        {
          field: 'validMonthSummary',
          title: '充电站名称',
          minWidth: 150,
        },
        {
          field: 'createBy',
          title: '站点类型',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '运营模式',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '资产属性',
          minWidth: 120,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],
      pileTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'periodName',
          title: '所属充电站',
          minWidth: 150,
        },
        {
          field: 'validMonthSummary',
          title: '运营模式',
          minWidth: 150,
        },
        {
          field: 'createBy',
          title: '充电桩编号',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '充电桩名称',
          minWidth: 120,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],
      institutionTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'periodName',
          title: '产权机构',
          minWidth: 250,
          align: 'center',
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],
      tableData: [],
      stationTableData: [],
      userTableData: [],
      rulerTab: 'bussinessRuler',
      scopeOfUseList: [
        { label: '按场站', value: '1' },
        { label: '按充电桩', value: '2' },
        { label: '按产权机构', value: '3' },
      ],
      limitationsList: [
        { label: '不限制', value: '1' },
        { label: '指定', value: '2' },
        { label: '批量导入', value: '3' },
      ],
      userTypeList: [
        { label: '个人用户', value: '1' },
        { label: '企业用户', value: '2' },
        { label: '批量导入', value: '3' },
      ],
      newUserRegisterList: [
        { label: '不限制', value: '1' },
        { label: '是', value: '2' },
        { label: '否', value: '3' },
      ],
      chargingAreaList: [
        { label: '不限制', value: '1' },
        { label: '限制', value: '2' },
      ],
      chargingSituationList: [
        { label: '不限制', value: '1' },
        { label: '充电量', value: '2' },
        { label: '充电金额', value: '3' },
      ],
      activityLimitationList: [
        { label: '不限制', value: '1' },
        { label: '限制', value: '2' },
      ],
    };
  },

  computed: {
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
    tableColumnConfig() {
      return this.baseInfo.form.activeType === '3'
        ? this.reductionTableColumn
        : this.tableColumn;
    },
    stationTableColumnConfig() {
      return this.baseInfo.form.scopeOfUse === '1'
        ? this.stationTableColumn
        : this.baseInfo.form.scopeOfUse === '2'
        ? this.pileTableColumn
        : this.institutionTableColumn;
    },
    chooseNameFilter() {
      return this.baseInfo.form.scopeOfUse === '1'
        ? '充电站选择'
        : this.baseInfo.form.scopeOfUse === '2'
        ? '充电桩选择'
        : '产权机构选择';
    },
  },
  mounted() {
    const chargePeriodId = this.$route.query.chargePeriodId;
    if (chargePeriodId) {
      this.chargePeriodId = chargePeriodId;
      this.getElectricPricePeriodDetail();
    }
    this.getAreaList();
  },
  methods: {
    onClickTemplate() {
      console.log('点击模板');
    },

    onDownloadFile(row) {
      console.log('下载文件', row);
    },
    handleTabsClick() {},
    handleAdd() {
      if (this.baseInfo.form.activeType === '1') {
        this.$refs.addPrizes.dialogVisible = true;
      } else if (this.baseInfo.form.activeType === '2') {
        this.$refs.addNewPrizes.dialogVisible = true;
      } else if (this.baseInfo.form.activeType === '3') {
        this.$refs.addDiscounts.dialogVisible = true;
      }
    },
    handleChoose() {
      if (this.baseInfo.form.scopeOfUse === '1') {
        this.$refs.chooseStationModal.dialogVisible = true;
      } else if (this.baseInfo.form.scopeOfUse === '2') {
        this.$refs.choosePileModal.dialogVisible = true;
      } else if (this.baseInfo.form.scopeOfUse === '3') {
        this.$refs.chooseInstitutionModal.dialogVisible = true;
      }
    },
    activeTypeChange() {
      this.tableData = [];
    },
    chooseTableData(list) {
      console.log(list, 999);
      this.stationTableData.concat(list);
    },
    addTableData(obj) {
      console.log(obj, 999);
      this.tableData.push(obj);
    },
    // 获取计费详情
    async getElectricPricePeriodDetail() {
      const [err, res] = await getElectricPricePeriodDetail({
        operateId: this.chargePeriodId,
      });
      if (err) return;

      const {
        periodNo,
        periodName,
        suitCityCode,
        createUnit,
        periodDetailList,
        approvalStatus,
      } = res.data;

      this.baseInfo.form = {
        timePeriodId: periodNo,
        periodName: periodName,
        suitCityCode: suitCityCode,
        createUnit: createUnit,
      };

      this.approvalStatus = approvalStatus;

      console.log(periodDetailList, 'periodDetailList');

      const list = Array.from(
        periodDetailList
          .reduce((map, item) => {
            const month = item.validMonth;
            if (!map.has(month)) {
              map.set(month, {
                month,
                priceList: [],
                active: false,
              });
            }
            map.get(month).priceList.push({
              priceType: item.timeFlag,
              startTime: item.beginTime,
              endTime: item.endTime,
            });
            return map;
          }, new Map())
          .values()
      ).map((group, index) =>
        index === 0 ? { ...group, active: true } : group
      );

      this.periodDetailList = list;
    },
    // 获取适用地市
    async getAreaList() {
      const [err, res] = await getAreaList({
        areaLevel: '03',
        huNanOnly: true,
      });
      if (err) return;
      const { data } = res;
      const list = [];
      data.forEach((item) => {
        list.push({
          label: item.areaName,
          value: item.areaCode,
        });
      });
      this.areaList = list;
    },

    // 获取当前月的YYYY-MM格式
    getCurrentMonth() {
      const date = new Date();
      return `${date.getFullYear()}-${(date.getMonth() + 1)
        .toString()
        .padStart(2, '0')}`;
    },
    // 添加下个月
    addNextMonth() {
      const lastMonth =
        this.periodDetailList[this.periodDetailList.length - 1].month;
      const [year, month] = lastMonth.split('-').map(Number);

      // 计算下个月
      let nextYear = year;
      let nextMonth = month + 1;
      if (nextMonth > 12) {
        nextYear += 1;
        nextMonth = 1;
      }

      const nextMonthStr = `${nextYear}-${nextMonth
        .toString()
        .padStart(2, '0')}`;

      this.periodDetailList.push({
        month: nextMonthStr,
        priceList: [
          {
            priceType: '1',
            startTime: '',
            endTime: '',
          },
        ],
        active: false,
      });
    },

    // 选中月份
    onClickMonthTag(index) {
      const list = [];
      this.periodDetailList.forEach((item, i) => {
        if (i === index) {
          list.push({
            ...item,
            active: true,
          });
        } else {
          list.push({
            ...item,
            active: false,
          });
        }
      });
      this.periodDetailList = list;
    },

    // 删除月份
    removeMonth(index) {
      if (this.periodDetailList[index].active) {
        this.periodDetailList.splice(index, 1);
        this.periodDetailList[0].active = true;
      } else {
        this.periodDetailList.splice(index, 1);
      }
    },

    // 添加电价区间
    onClickAddPrice(index) {
      this.periodDetailList[index].priceList.push({
        priceType: '',
        startTime: '',
        endTime: '',
      });
    },

    // 删除电价区间
    onClickDeletePrice(index, priceIndex) {
      this.periodDetailList[index].priceList.splice(priceIndex, 1);
    },
    // 取消
    handleCancel() {
      this.$router.back();
    },

    // 保存/提交
    handleSave: _.debounce(async function (type) {
      Promise.all([
        this.validateForm('baseInfoForm'),
        // this.validateForm('periodForm'),
        // ...this.periodDetailList.map((item, index) => this.validateForm(`periodForm${index}`)),
      ])
        .then(async () => {
          // 手动校验时段信息
          for (const [
            monthIndex,
            monthItem,
          ] of this.periodDetailList.entries()) {
            const { month, priceList } = monthItem;

            // 校验每个电价区间
            for (const [priceIndex, priceItem] of priceList.entries()) {
              const { priceType, startTime, endTime } = priceItem;

              // 手动校验必填项
              if (!priceType) {
                return this.$message.warning(
                  `${month} 第${priceIndex + 1}个电价区间类型未选择`
                );
              }
              if (!startTime) {
                return this.$message.warning(
                  `${month} 第${priceIndex + 1}个电价区间开始时间未填写`
                );
              }
              if (!endTime) {
                return this.$message.warning(
                  `${month} 第${priceIndex + 1}个电价区间结束时间未填写`
                );
              }

              // 校验时间有效性
              const start = this.timeToMinutes(startTime);
              const end = this.timeToMinutes(endTime);
              if (start >= end) {
                return this.$message.warning(
                  `${month} 第${
                    priceIndex + 1
                  }个电价区间结束时间必须晚于开始时间`
                );
              }
            }

            // 校验时间段重叠
            if (this.hasTimeOverlap(priceList)) {
              return this.$message.warning(`${month} 中存在时间段重叠，请调整`);
            }
          }

          const { periodName, suitCityCode, createUnit } = this.baseInfo.form;

          const list = [];

          this.periodDetailList.forEach((item, index) => {
            const { month, priceList } = item;

            priceList.forEach((priceItem) => {
              const { startTime, endTime, priceType } = priceItem;
              list.push({
                validMonth: month,
                beginTime: startTime,
                endTime,
                timeFlag: priceType,
              });
            });
          });

          const params = {
            periodName,
            suitCityCode,
            createUnit,
            isSubmitAudit: type === 'audit' ? true : false,
            periodDetailList: list,
          };

          if (!this.chargePeriodId) {
            // 新增
            const [err, res] = await addElectricPricePeriod(params);

            if (err) {
              return this.$message.error(err.message || '新增电价时段失败');
            }

            this.$message({
              type: 'success',
              message: '新增成功!',
            });

            setTimeout(() => {
              this.$router.back();
            }, 500);
          } else {
            // 修改
            const [err, res] = await updateElectricPricePeriod({
              ...params,
              chargePeriodId: this.chargePeriodId,
            });

            if (err) {
              return this.$message.error(err.message || '修改电价时段失败');
            }

            this.$message({
              type: 'success',
              message: '修改成功!',
            });

            setTimeout(() => {
              this.$router.back();
            }, 500);
          }
        })
        .catch((err) => {
          console.log(err);
          // 有表单校验失败
          this.$message.error('表单校验失败，请检查输入');
        });
    }, 300),

    // 校验单个表单
    validateForm(formRef) {
      return new Promise((resolve, reject) => {
        this.$refs[formRef].validate((valid) => {
          if (valid) {
            resolve(); // 校验通过
          } else {
            reject(new Error(`${formRef} 校验失败`)); // 校验失败
          }
        });
      });
    },

    timeToMinutes(timeStr) {
      if (!timeStr) return 0;
      const [hours, minutes] = timeStr.split(':').map(Number);
      return hours * 60 + minutes;
    },
    hasTimeOverlap(intervals) {
      // 转换为分钟并排序
      const sorted = intervals
        .map((item) => ({
          start: this.timeToMinutes(item.startTime),
          end: this.timeToMinutes(item.endTime),
        }))
        .sort((a, b) => a.start - b.start);

      // 检查是否有重叠
      for (let i = 1; i < sorted.length; i++) {
        const prev = sorted[i - 1];
        const current = sorted[i];
        if (prev.end > current.start) {
          return true;
        }
      }
      return false;
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 48px;
    padding: 0;
    display: flex;
    align-items: center;
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
        background-color: #1ab2ff;
        border-color: #1ab2ff;
      }
      ::v-deep .el-button--small {
        font-size: 14px;
      }
      .distribution {
        margin-left: 24px;
        margin-right: 24px;
        display: flex;
        align-items: center;
      }
    }
  }
  .card-head-split {
    width: 100%;
    height: 1px;
    background-color: #e9ebf0;
    margin-bottom: 24px;
  }

  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
    padding: 0 16px 16px 16px;
    .custom-header {
      background: -webkit-gradient(
          linear,
          left top,
          left bottom,
          from(rgba(0, 149, 255, 0.5)),
          to(rgba(87, 152, 255, 0))
        ),
        #f5faff;
      background: linear-gradient(
          180deg,
          rgba(0, 149, 255, 0.5) 0%,
          rgba(87, 152, 255, 0) 100%
        ),
        #f5faff;
      background-repeat: no-repeat;
    }
  }
  .form-wrap-top {
    margin-top: 16px;
  }
}

.period-warp {
  padding: 16px 24px 24px 24px;
}

.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .bottom-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
  }
}

.month-picker {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 0 32px;
  .month-tag-choose {
    height: 32px;
    display: inline-flex;
    align-items: center;
    padding: 8px 10px;
    border-radius: 5px;
    border: 1px solid #217aff;
    background-color: #217aff;
    color: #ffffff;
    .remove {
      margin-left: 8px;
      cursor: pointer;
      color: #ffffff;
      &:hover {
        color: #f56c6c;
      }
    }
  }
  .month-tag {
    height: 32px;
    display: inline-flex;
    align-items: center;
    padding: 8px 10px;
    border-radius: 5px;
    border: 1px solid #a6c6ff;
    background-color: #ebf3ff;
    color: #292b33;
    .remove {
      margin-left: 8px;
      cursor: pointer;
      color: #505363;
      &:hover {
        color: #f56c6c;
      }
    }
  }

  .add-btn {
    width: 32px;
    height: 32px;
    background-image: url('~@/assets/charge/add-btn.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
}

::v-deep .el-input {
  width: 100% !important;
}

.price-add {
  width: 100%;
  height: 34px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #dfe1e5;
  border-radius: 2px;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #217aff;
}
.price-delete {
  width: 16px;
  height: 16px;
  background-image: url('~@/assets/station/station-build-delete.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin-top: 44px;
}

.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
  }
}
.choose-box {
  margin-left: 16px;
  background: #ebf3ff;
  height: 32;
  border-radius: 2px;
  padding: 3px 10px;
  font-family: 'PingFang SC';
  font-weight: 400;
  font-size: 14px;
  line-height: 26px;
  color: #217aff;
  text-align: center;
  span {
    font-family: 'Oswald Regular';
    font-size: 16px;
    margin: 0 4px;
  }
}
.upload-wrap {
  padding: 24px 24px 0 24px;
  ::v-deep .el-upload-dragger {
    width: 732px;
    height: 180px;
    border: 1px dashed #5798ff;
    border-radius: 2px;
    .upload-icon {
      width: 24px;
      height: 24px;
      background-image: url('~@/assets/station/upload-icon.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin: 46px auto 0 auto;
    }
    .upload-text {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      margin: 16px auto;
      color: #292b33;
    }
    .upload-tip {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      margin: 16px auto;
      color: #818496;
    }
  }
  .upload-info {
    font-weight: 400;
    font-size: 16px;
    line-height: 16px;
    margin: 16px 0 24px 0;
    color: #292b33;
  }
}
.flex-end {
  display: flex;
  align-items: flex-end;
}
</style>
