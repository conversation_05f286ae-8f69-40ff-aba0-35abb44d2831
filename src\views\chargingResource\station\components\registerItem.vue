<template>
    <div class="register-wrap">
        <div class="register-title-wrap">
            <div class="register-title">注册信息:</div>
            <div class="add-button" @click="addRule"></div>
        </div>
       
        <el-row
            v-for="(row, index) in ruleList"
            :key="row.sn"
            style="flex-wrap: nowrap; margin-bottom: 8px"
            type="flex"
            :gutter="20"
            >
            
            <el-col :span="11">
                <el-form-item
                    label="电网侧注册信息："
                    prop="gridInformation"
                    label-width="150px"
                >
                    <el-select
                        v-model="row.gridInformation"
                        placeholder="请选择类型"
                        style="width: 100%"
                    >
                        <el-option
                            v-for="row in dict.type.ls_charging_station_gridInformation"
                            :key="row.value"
                            :label="row.label"
                            :value="row.value"
                        ></el-option>
                    </el-select>
                </el-form-item>
            </el-col>

            <el-col :span="11">
                <el-form-item
                    label="注册时间："
                    prop="powerGridTime"
                    label-width="120px"
                    style="width: 100%;"
                >
                    <el-date-picker
                        v-model="row.powerGridTime" 
                        value-format="yyyy-MM-dd"
                    >

                    </el-date-picker>
                </el-form-item>
            </el-col>


            <el-col v-if="!readonly"  :span="2" style="margin-left: 4px;">
                <!-- <el-button v-if="index===ruleList.length-1" icon="plus" type="primary" @click="addRule" style="margin-right: 10px;">添加</el-button> -->
                <el-popover
                    v-show="ruleList.length > 1"
                    placement="top"
                    title="是否删除当前规则"
                    width="200"
                    trigger="click"
                    :visible.sync="popoverVisible"
                >
                    <template #reference>
                        <!-- <el-button type="danger">删除</el-button> -->
                        <div class="delete-button"></div>
                    </template>
                    <div style="text-align: right; margin-top: 10px;">
                        <el-button size="mini" @click="popoverVisible = false">取消</el-button>
                        <el-button size="mini" type="primary" @click="removeChargePeriods(index)">确定</el-button>
                    </div>
                </el-popover>
            </el-col>
        </el-row>  
    </div>
</template>
<script>  
  export default {
    props: {
        value: {
            type: Array,
            default: () => [],
        },

        // readonly为true时, 只读
        readonly: {
            type: Boolean,
            default: false,
        },
    },
    components: {

    },
    dicts: [
        'ls_charging_station_gridInformation', //电网侧注册信息
    
    
    ],
    data() {
        return {
            ruleList: [],
            typeList: [
                { label: '新型电力负荷系统', value: '02' },
                { label: '配电云', value: '03' },
                { label: '虚拟电厂', value: '01' },
            ],
            // ruleChooseList: [
            //     { label: '按用电量', value: '1' },
            //     { label: '按用电时长', value: '2' },
            //     { label: '按用电功率', value: '3' },
            // ],
            popoverVisible: false,
            dayResopneList: [
                { label: '是', value: '1' },
                { label: '否', value: '2' },
            ],

        };
    },
    watch: {
        ruleList: {
            deep: true,
            handler(val) {
                this.$emit('input', this.ruleList);
            }
        },
        value: {
            deep: true,
            handler(val) {
                console.log(val,'watch value');
                this.ruleList = val;
            }
        }
    },
    computed: {},
    mounted() {
        this.ruleList = this.value;
    },
    methods: {
        // 添加结算规则
        addRule() {
            const list = this.ruleList;
            list.push({
                sn: list.length + 1,
                gridInformation: '',
                powerGridTime: '',
            })

            this.ruleList = list;
        },

        // 清空规则
        deleteRuleList() {
            this.ruleList = [];
        },

         // * 删除指定规则
         removeChargePeriods (index){
            this.popoverVisible = false;
            this.ruleList.splice(index, 1);
        },

        
    },
  };
  </script>
  <style lang="scss" scoped>
.form-item-tip {
    color: #999;
  font-size: 12px;
  margin-top: 0px;  
}
.register-wrap {
    background-color: #F9F9FB;
    padding: 16px;
    border-radius: 5px;
    margin-bottom: 24px;
    .register-title-wrap {
        display: flex;
        height: 32px;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
        .register-title {
            font-weight: 500;
            font-size: 16px;
            color: #12151A;
        }
        .add-button {
            width: 64px;
            height: 24px;
            background-image: url('~@/assets/station/add-button.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
    .delete-button {
        margin-top: 36px;
        width: 32px;
        height: 32px;
        background-image: url('~@/assets/station/delete-button.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}

::v-deep .el-input {
    width: 100%;
}

  </style>
  