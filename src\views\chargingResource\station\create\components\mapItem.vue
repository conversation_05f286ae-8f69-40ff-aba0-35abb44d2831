<template>
    <div>
        <el-row :gutter="5">
            <el-col :span="8">
                <el-form-item 
                    label=""
                    prop="stationArea"
                    :label-width="formLabelWidth">
                    <el-cascader ref="cascaderRef" style="width: 100%;" v-model="formModel.area" :options="options" 
                        :props="{
                            label: 'label',
                            children: 'children',
                            value: 'value',
                            checkStrictly: true,
                            expandTrigger: 'hover'
                        }"  
                        @change="updateData">
                    </el-cascader>
                </el-form-item>
            </el-col>
            <el-col :span="16">
                <el-form-item
                    label=""
                    prop="stationAddress"
                    :label-width="formLabelWidth"
                >
                    <el-input 
        
                        v-model="formModel.stationAddress"  
                        placeholder="详细地址，小于等于200个字符"
                        @change="updateData"
                    ></el-input>
                </el-form-item>
            </el-col>


                            
        </el-row>

        <el-row>
            <el-col :span="24">
                <MyMap 
                    ref="map"
                    style="margin-bottom: 24px"
                    :center="mapCenter"
                    height="600px"
                    :disabled="isDetail"
                    :isNeedAutoComplete="true"
                    @click="onClickMap"></MyMap>
            </el-col>
        </el-row>
    </div>
   
</template>
<script>  

import MyMap from '@/components/Map/index.vue';
import { getMapAreaList , } from '@/api/bigScreen';

  export default {
    props: {
        value: {
            type: Object,
            default: () => {
               
            },
        },
        isDetail: {
            type: Boolean,
            defaults: false
        }, // 是否纯展示

    },
    components: {
        MyMap
    },
    data() {
        return {
            formLabelWidth: '120px',
            options: [],
            mapCenter: [112.982279,28.19409],
            dict: {
                addressOptions: [],
            },
            formModel: {
                area: [],
                stationAddress: '',
                lon: 112.982279,
                lat: 28.19409,
            },
        };
    },
    watch: {
        value: {
            handler(val) {
                if (val) {
                if (val?.province && val?.city && val?.county) {
                    this.formModel.area = [val?.province, val?.city, val?.county];
                }
                this.formModel.stationAddress = val?.stationAddress;
                this.formModel.lon = val?.lon;
                this.formModel.lat = val?.lat;
                if (val?.lon && val?.lat) {
                    this.mapCenter = [val.lon, val.lat];
                }
                }
            },
            immediate: true,
            deep: true,
        },
    },
    computed: {
        result() {
            const {
                area: [province, city, county],
                stationAddress,
                lon,
                lat,
            } = JSON.parse(JSON.stringify(this.formModel));
            return {
                province,
                city,
                county,
                stationAddress,
                lon,
                lat,
            };
        },
    },
    mounted() {
        this.getOptions();
    },
    methods: {
        async getOptions() {
            // todo 省市区数据新增lon,lat字段,用于切换时地图定位
            const [err, res] = await getMapAreaList({});
            // if (result.code === '10000') {
                // this.options = this.filterEmptyChildren([res.data]);
            // }

            if (res?.data?.length && Array.isArray(res.data)) {
                const areaList = res.data;
                const countryList = areaList
                .filter((item) => item.areaLevel === '04')
                .map((x) => ({
                    value: x.areaCode,
                    label: x.areaName,
                    parentCode: x.upAreaCode,
                    lat: x.lat,
                    lon: x.lon,
                }));
                const cityList = areaList
                .filter((item) => item.areaLevel === '03')
                .map((x) => ({
                    children: countryList.filter((y) => y.parentCode === x.areaCode),
                    value: x.areaCode,
                    label: x.areaName,
                    parentCode: x.upAreaCode,
                    lat: x.lat,
                    lon: x.lon,
                }));
                this.options = areaList
                .filter((item) => item.areaLevel === '02')
                .map((x) => ({
                    children: cityList.filter((y) => y.parentCode === x.areaCode),
                    value: x.areaCode,
                    label: x.areaName,
                    parentCode: x.upAreaCode,
                    lat: x.lat,
                    lon: x.lon,
                }));
            }

            console.log(this.options, 'options');
        },
        filterEmptyChildren(options) {
            return options.map(option => {
                const newOption = { ...option };
                if (newOption.list && newOption.list.length > 0) {
                // 递归过滤子节点
                newOption.list = this.filterEmptyChildren(newOption.list);
                if (newOption.list.length === 0) {
                    // 如果过滤后子节点为空，移除 children 属性
                    delete newOption.list;
                }
                } else if (newOption.list && newOption.list.length === 0) {
                // 如果 children 为空数组，移除 children 属性
                    delete newOption.list;
                }
                return newOption;
            });
        },

        onClickMap(e) {
            console.log(e)
            const { province, city, district } = e?.areaCode || {};
            this.formModel.area = [province, city, district];
            this.formModel.lon = e.longitude;
            this.formModel.lat = e.latitude;
            this.updateData();
        },

        updateData(value, selectedOptions) {
            console.log(value, selectedOptions,this.result, '11111')
            if (this.result.province === '900000') {
                this.formModel.lon = 112.982279;
                this.formModel.lat = 28.19409;
            } else if (selectedOptions && selectedOptions.length === 3) {
                this.formModel.lon = selectedOptions[2].lon ? selectedOptions[2].lon : 116.397128;
                this.formModel.lat = selectedOptions[2].lat ? selectedOptions[2].lat : 39.916527;
            }
            this.$emit('input', this.result);
        },
        
    },
  };
  </script>

<style lang="scss" scoped>

  </style>
  