<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="630px"
    @close="handleCancel"
  >
    <el-form :model="form" :rules="rules" ref="ruleForm" label-position="top">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="充电卡ID：" :label-width="formLabelWidth">
            <el-input v-model="info.cardNo" :disabled="true"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <div class="price-wrap">
            充电卡余额（元）：
            <div class="price">{{ info.cardAmount }}</div>
          </div>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="所属企业："
            prop="enterpriseId"
            :label-width="formLabelWidth"
          >
            <el-select
              v-model="form.enterpriseId"
              :loading="enterpriseLoading"
              filterable
              remote
              :remote-method="debouncedEnterpriseSearch"
              style="width: 100%"
            >
              <el-option
                v-for="item in enterpriseNameList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="绑定用户："
            prop="cardBindUserId"
            :label-width="formLabelWidth"
          >
            <el-select
              :loading="bindUserLoading"
              filterable
              remote
              :remote-method="debouncedBindUserSearch"
              v-model="form.cardBindUserId"
              style="width: 100%"
            >
              <el-option
                v-for="item in bindUserNameList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="新充电卡ID："
            prop="newCardNo"
            :label-width="formLabelWidth"
          >
            <el-input v-model="form.newCardNo"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="补卡工本费(元)："
            prop="transactionAmount"
            :label-width="formLabelWidth"
          >
            <el-input-number
              :min="1"
              :precision="0"
              :step="1"
              v-model="form.transactionAmount"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="充电卡支付方式："
            prop="paymentMethod"
            :label-width="formLabelWidth"
          >
            <el-select v-model="form.paymentMethod" style="width: 100%">
              <el-option
                v-for="item in this.dict.type.ls_charging_card_payment_method"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="补卡说明："
            prop="instruction"
            :label-width="formLabelWidth"
          >
            <el-input
              type="textarea"
              :rows="3"
              v-model="form.instruction"
              placeholder="请输入补卡说明"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {
  cardReplacementCard,
  enterpriseList,
  queryEnterpriseUser,
} from '@/api/user/chargeCardManage';

export default {
  props: {
    dialogTitle: {
      type: String,
      default: '补卡',
    },
  },
  components: {},
  dicts: [
    'ls_charging_card_payment_method', // 充电卡支付方式
  ],
  data() {
    return {
      dialogVisible: false,
      form: {
        enterpriseId: '',
        cardBindUserId: '',
        transactionAmount: '',
        newCardNo: '',
        paymentMethod: '',
        instruction: '',
      },
      rules: {
        enterpriseId: [
          {
            required: true,
            message: '请选择企业',
            trigger: 'change',
          },
        ],
        cardBindUserId: [
          {
            required: true,
            message: '请绑定用户',
            trigger: 'change',
          },
        ],
        newCardNo: [
          {
            required: true,
            message: '请输入新充电卡ID',
            trigger: 'change',
          },
        ],
        transactionAmount: [
          {
            required: true,
            message: '请输入补卡工本费',
            trigger: 'change',
          },
        ],
        paymentMethod: [
          {
            required: true,
            message: '请选择充电卡支付方式',
            trigger: 'change',
          },
        ],
      },
      formLabelWidth: '120px',

      info: {
        cardId: '',
        cardNo: '',
        cardAmount: '',
      },
      enterpriseLoading: false,
      enterpriseNameList: [],
      submitLoading: false,
      bindUserNameList: [],
      bindUserLoading: false,
    };
  },
  computed: {},
  mounted() {},
  methods: {
    async debouncedBindUserSearch(query) {
      if (query !== '') {
        this.bindUserLoading = true;
        setTimeout(async () => {
          const [err, res] = await queryEnterpriseUser({
            enterpriseName: query,
          });
          this.bindUserLoading = false;
          if (err) return;

          this.bindUserNameList = res.data.map((item) => ({
            label: item.fullName,
            value: item.userId,
          }));
        }, 200);
      } else {
        this.bindUserNameList = [];
      }
    },
    async debouncedEnterpriseSearch(query) {
      if (query !== '') {
        this.enterpriseLoading = true;
        setTimeout(async () => {
          const [err, res] = await enterpriseList({
            enterpriseName: query,
          });
          this.enterpriseLoading = false;
          if (err) return;

          this.enterpriseNameList = res.data.map((item) => ({
            label: item.enterpriseName,
            value: item.enterpriseId,
          }));
        }, 200);
      } else {
        this.enterpriseNameList = [];
      }
    },
    resetForm() {
      Object.keys(this.form).forEach((key) => {
        this.form[key] = '';
      });
    },

    handleCancel() {
      this.$refs.ruleForm.resetFields();
      this.resetForm();
      console.log(this.form, 'this.form');
      this.dialogVisible = false;
    },

    // 新增按钮防抖
    handleConfirm() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          console.log(this.form, 'this.form');
          this.submitLoading = true;
          const params = {
            ...this.info,
            ...this.form,
          };

          console.log('params', params);
          const [err, res] = await cardReplacementCard(params);
          this.submitLoading = false;
          if (err) return;

          this.$message.success('补卡成功');
          this.dialogVisible = false;
          this.$emit('loadData');
        }
      });
    },
  },
  watch: {},
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__content {
  display: flex !important;
}

::v-deep .el-input-number {
  width: 100% !important;
}

.info-wrap {
  display: flex;
  height: 20px;
  margin-bottom: 24px;
  align-items: center;
  .info-title {
    font-weight: 400;
    font-size: 16px;
    line-height: 16px;
    color: #505363;
  }
  .info-detail {
    font-weight: 400;
    font-size: 16px;
    line-height: 16px;
    color: #292b33;
  }
}

.price-wrap {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  background-color: #ebf3ff;
  border-radius: 2px;
  margin-bottom: 12px;
  font-weight: 400;
  font-size: 16px;
  padding-left: 16px;
  box-sizing: border-box;
  .price {
    font-family: Oswald Regular;
    color: #217aff;
  }
}
</style>
