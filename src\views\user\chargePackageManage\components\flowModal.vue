<template>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="80%"
      @close="handleCancel"
      :destroy-on-close="true"
    >
        <BuseCrud
            ref="crud"
            :loading="loading"
            :filterOptions="filterOptions"
            :tablePage="tablePage"
            :tableColumn="tableColumn"
            :tableData="tableData"
            :pagerProps="pagerProps"
            :modalConfig="modalConfig"
            @loadData="loadData"
        >
        </BuseCrud>

       
    </el-dialog>
  </template>
  <script>  

import {
    editPileControl
} from '@/api/pile/index';

  export default {
    props: {
        dialogTitle:{
            type: String,
            default: '流水'
        }
    },
    components: {
  
    },
    dicts: [
        
    ],
    data() {
        return {
            dialogVisible: false,
            
            tablePage: { total: 0, currentPage: 1, pageSize: 10 },
        
            loading: false,
            tableColumn:[
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                },
                {
                    field: 'businessId',
                    title: '充电订单号',
                    minWidth: 180,
                },
                {
                    field: 'flowId',
                    title: '交易流水号',
                    minWidth: 180,
                },
                {
                    field: 'amount',
                    title: '交易金额',
                    minWidth: 180,
                },
                {
                    field: 'tradeTime',
                    title: '交易时间',
                    minWidth: 180,
                },
                {
                    field: 'tradeType',
                    title: '交易类型',
                    minWidth: 180,
                },
                {
                    field: 'balance',
                    title: '卡内余额',
                    minWidth: 180,
                },
                {
                    field: 'tradeStatus',
                    title: '交易状态',
                    minWidth: 180,
                }
            ],
            tableData: [],
            pagerProps: {
                layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
            },
            params: {
                businessId: '',
                flowId: '',
                tradeDate: [],
                tradeType: '',
            }
        };
    },
    computed: {
        filterOptions() {
            return {
                config:[
                    {
                        field: 'businessId',
                        title: '业务订单号',
                        element: 'el-input',
                    },
                    {
                        field: 'flowId',
                        title: '流水号',
                        element: 'el-input',
                    },
                    {
                        field: 'tradeDate',
                        title: '交易时间',
                        element: 'el-date-picker',
                        default: [],
                        props: {
                            type: 'daterange',
                            valueFormat: 'yyyy-MM-dd',
                            format: 'yyyy-MM-dd',
                        },
                    },
                    {
                        field: 'tradeType',
                        title: '交易类型',
                        element: 'el-select',
                        default: '',
                        props:{
                            options: []
                        }
                    }
                ],
                params: this.params,
            };
        },
        modalConfig() {
            return {
                addBtn: false,
                viewBtn: false,
                menu: false,
                editBtn: false,
                delBtn: false,
            }
        },
    },

    mounted() {
        this.loadData()
    },
    methods: {
        handleCancel() {
            this.dialogVisible = false;
        },

        loadData() {
            this.tableData = [
                {
                    businessId: 'ORD-************',
                    flowId: 'FLOW-5X8A9B2C1D',
                    amount: 150.00,
                    tradeTime: '2024-03-15 09:30:45',
                    tradeType: '充值',
                    balance: 1150.00,
                    tradeStatus: '成功'
                },
                {
                    businessId: 'ORD-************',
                    flowId: 'FLOW-Z3Y4E7F6G',
                    amount: -80.50,
                    tradeTime: '2024-03-15 14:15:22',
                    tradeType: '消费',
                    balance: 1069.50,
                    tradeStatus: '成功'
                },
                {
                    businessId: 'ORD-************',
                    flowId: 'FLOW-P9Q0R2S1T',
                    amount: 200.00,
                    tradeTime: '2024-03-16 10:05:17',
                    tradeType: '充值',
                    balance: 1269.50,
                    tradeStatus: '处理中'
                },
            ]
        },

    },
    
  };
  </script>
  <style lang="scss" scoped>
::v-deep .el-form-item__content{
    display: flex !important;
}

::v-deep .el-input-number {
    width: 100% !important;
}


  </style>
  