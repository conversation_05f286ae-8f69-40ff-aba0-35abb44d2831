<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    @close="handleCancel"
    width="80%"
    :destroy-on-close="true"
  >
    <div class="settlement-doc">
      <!-- 头部信息 -->
      <div class="doc-header">
        <div class="station-icon">
          <img
            src="@/assets/stationDivision/settlement-doc-icon.png"
            alt="充电站"
          />
        </div>
        <div class="station-info">
          <div class="station-name">充电站1名称名名称</div>
          <div class="station-details">
            <el-row>
              <el-col :span="12" style="margin: 6px 0">
                <div class="detail-item">分成周期：2024-09-01~2024-09-30</div>
              </el-col>
              <el-col :span="12" style="margin: 6px 0">
                <div class="detail-item">场地方：场地1</div>
              </el-col>
              <el-col :span="12" style="margin: 6px 0">
                <div class="detail-item">地市：长沙市</div>
              </el-col>
              <el-col :span="12" style="margin: 6px 0">
                <div class="detail-item">分成类型：服务费</div>
              </el-col>
              <el-col :span="12" style="margin: 6px 0">
                <div class="detail-item">
                  分成条款：(充电服务费-指标电费) *30%
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>

      <!-- 结算数据表格 -->
      <div class="settlement-data">
        <div class="section-title">结算数据：</div>
        <div class="table-container">
          <table class="settlement-table">
            <tr>
              <th>充电总电量(KWH)</th>
              <th>充电电费(元)</th>
              <th>内部车辆充电电费(元)</th>
              <th>外部车辆充电电费(元)</th>
              <th>充电服务费(元)</th>
              <th>内部车辆充电服务费(元)</th>
            </tr>
            <tr>
              <td>2000</td>
              <td>1000</td>
              <td>500</td>
              <td>500</td>
              <td>600</td>
              <td>300</td>
            </tr>
            <tr>
              <th>外部车辆充电服务费(元)</th>
              <th>充电总收入(元)</th>
              <th>供电电量(KWH)</th>
              <th>供电电费(元)</th>
              <th>电损电量(KWH)</th>
              <th>电损计量类型</th>
            </tr>
            <tr>
              <td>300</td>
              <td>1000</td>
              <td>500</td>
              <td>500</td>
              <td>600</td>
              <td>电费电损</td>
            </tr>
            <tr>
              <th>外部车辆充电服务费(元)</th>
              <th>运维成本(元)</th>
              <th>平台服务费(元)</th>
              <th>场地租金(元)</th>
              <th>场地方承担损耗电费(元)</th>
              <th>待分成费用（元）</th>
            </tr>
            <tr>
              <td>100</td>
              <td>-</td>
              <td>-</td>
              <td>-</td>
              <td>-</td>
              <td>500</td>
            </tr>
          </table>
        </div>
        <div class="settlement-amount">
          <span class="amount-label">结算金额</span>
          <span class="amount-value">150</span>
        </div>
      </div>

      <!-- 签署信息 -->
      <div class="signature-section">
        <div class="signature-note">
          请核对以上账，如无问题，请在本页下端签字盖公章
        </div>
        <div class="signature-note">本函一式两份，对账双方各一份</div>

        <div class="signature-area">
          <div class="signature-party">
            <div class="party-name">湖南省电动汽车服务有限公司</div>
            <div class="signature-line">签章（财务专用章或公章）</div>
            <div class="signature-line">核对人：</div>
            <div class="signature-line">日期：</div>
          </div>
          <div class="signature-party">
            <div class="party-name">湖南能源大数据中心有限责任公司</div>
            <div class="signature-line">签章（财务专用章或公章）</div>
            <div class="stamp-image">
              <img
                src="@/assets/stationDivision/settlement-doc-icon.png"
                alt="公章"
              />
            </div>
            <div class="signature-line">核对人：</div>
            <div class="signature-line">日期：</div>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">返回</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '线上站点结算单',
    };
  },
  mounted() {},
  methods: {
    loadData() {
      console.log('初始化数据');
    },
    // 返回
    handleCancel() {
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.settlement-doc {
  background-color: #fff;
  padding: 20px;
  font-family: 'Microsoft YaHei', sans-serif;
  color: #333;

  .doc-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;
    background-color: #f8fbff;
    border-radius: 8px;
    padding: 16px;

    .station-icon {
      width: 48px;
      height: 48px;
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 16px;

      img {
        width: 48px;
        height: 48px;
      }
    }

    .station-info {
      flex: 1;

      .station-name {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        margin-bottom: 12px;
      }

      .station-details {
        .detail-row {
          margin-bottom: 8px;
          display: flex;
          flex-wrap: wrap;
        }

        .detail-item {
          margin-right: 24px;
          color: #666;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }

  .settlement-data {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }

    .table-container {
      width: 100%;
      //   margin-bottom: 20px;
      overflow-x: auto;
    }

    .settlement-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 0;
      table-layout: fixed;

      th,
      td {
        border: 1px solid #e8eef7;
        padding: 12px 8px;
        text-align: center;
        font-size: 14px;
        word-break: break-all;
      }

      th {
        background-color: #f5faff;
        color: #333;
        font-weight: 500;
      }

      td {
        color: #333;
        background-color: #fff;
      }
    }

    .settlement-amount {
      display: flex;
      height: 60px;
      border: 1px solid #ebeef5;
      border-top: none;
      margin-top: -1px;

      .amount-label {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 16.6666%;
        background-color: #f5faff;
        color: #333;
        font-size: 14px;
        font-weight: 500;
        border-right: 1px solid #ebeef5;
      }

      .amount-value {
        display: flex;
        align-items: center;
        padding-left: 20px;
        flex: 1;
        color: #ff9900;
        font-size: 18px;
        font-weight: bold;
      }
    }
  }

  .signature-section {
    margin-top: 40px;

    .signature-note {
      margin-bottom: 16px;
      color: #606266;
      font-size: 14px;
    }

    .signature-area {
      display: flex;
      justify-content: space-between;

      .signature-party {
        width: 45%;
        position: relative;

        .party-name {
          font-weight: bold;
          margin-bottom: 20px;
        }

        .signature-line {
          margin-bottom: 30px;
          position: relative;

          &:after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 100%;
            border-bottom: 1px solid #ebeef5;
          }
        }

        .stamp-image {
          position: absolute;
          right: 30%;
          top: 30%;
          width: 100px;
          height: 100px;
          opacity: 0.5;

          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
}

@media print {
  .el-dialog__header,
  .el-dialog__footer {
    display: none !important;
  }

  .settlement-doc {
    padding: 0;
  }
}
</style>
