<template>
    <div class="container container-float " style="padding: 0;">
        <div class="device-head">
            <img src="@/assets/operator/operator-withdrawl-detail-icon.png" class="device-head-icon">
            
            <div class="device-info-wrap">
                <div class="device-title-wrap">
                    <div class="device-title">申请编号：{{ applyNo }}</div>
                    <div class="device-status">{{ applyStatus }}</div>
                </div>
                <div class="device-info-wrap">
                    <el-row>
                        <el-col :span="6">
                            <span class="label">申请人：</span>
                            <span class="value">{{ applyName }}</span>
                        </el-col>

                        <el-col :span="6">
                            <span class="label">申请时间：</span>
                            <span class="value">{{ applyTime }}</span>
                        </el-col>

                        <el-col :span="6">
                            <span class="label">运营商名称：</span>
                            <span class="value">{{ operatorName }}</span>
                        </el-col>

                        <el-col :span="6">
                            <span class="label">公司类型：</span>
                            <span class="value">{{ companyType }}</span>
                        </el-col>
                     
                    </el-row>
                </div>
            </div>
        </div>

        <div class="info-card" >
            <div class="card-head" >
                <div class="before-icon"></div>
                <div class="card-head-text">提现信息</div>
            </div>

            <div class="form-wrap">
                <el-row :gutter="20">
                    <el-col :span="8" style="margin-bottom: 24px;">
                        <div style="display: flex; align-items: center;">
                            <div class="info-title">提现金额：</div>
                            <div class="info-detail-orange">{{ withdrawAmount }}</div>
                            <div class="info-detail">元</div>
                        </div>
                    </el-col>

                    <el-col :span="8" style="margin-bottom: 24px;">
                        <div style="display: flex;  align-items: center;">
                            <div class="info-title">当前账户余额：</div>
                            <div class="info-detail-blue">{{ balance }}</div>
                            <div class="info-detail">元</div>
                        </div>
                    </el-col>

                    <el-col :span="8" style="margin-bottom: 24px;">
                        <div style="display: flex;  align-items: center;">
                            <div class="info-title">提现后账户余额：</div>
                            <div class="info-detail-blue">{{ withdrawBalance }}</div>
                            <div class="info-detail">元</div>
                        </div>
                    </el-col>

                    <!-- <el-col :span="8" style="margin-bottom: 24px;">
                        <div style="display: flex;">
                            <div class="info-title">备注：</div>
                            <div class="info-detail">{{ remark }}</div>
                        </div>
                    </el-col> -->
                </el-row>
            </div>
        </div>

    </div>
    
  </template>
  
  <script>

import {
    getWithdrawalDetail
} from '@/api/operator/index'



  
    export default {
    components: {
        
    },
    dicts: [
        'ls_charging_operator_category',
    ],
    data() {
        return {
            recordId: '',

            applyNo: '',
            applyStatus: '',
            applyName: '',
            applyTime: '',
            operatorName: '',
            companyType: '',
            
            withdrawAmount: '',
            balance: '',
            withdrawBalance: '',
            // remark: '备注'

        };
    },

    computed: {
    },
    mounted() {
        const recordId = this.$route.query.recordId;
        this.recordId = recordId;

        this.getWithdrawDetail();
    },
    methods: {
        async getWithdrawDetail() {
            const [err, res] = await getWithdrawalDetail({
                recordId: this.recordId
            })

            if (err) return 
            
            const {
                payId,
                auditStatus,
                createBy,
                createTime,
                operatorName,
                operatorType,
                amount,
                currentBalance,
                realAmount,
            } = res.data

            const auditStatusMap = {
                '0': '待审核',
                '1': '审核中',
                '2': '审核通过',
                '3': '审核不通过'
            }

            this.applyNo = payId
            this.applyStatus = auditStatusMap[auditStatus]

            this.applyName = createBy
            this.applyTime = createTime
            this.operatorName = operatorName
            this.companyType = this.selectDictLabel(
                    this.dict.type.ls_charging_operator_category,
                    operatorType
                );

            this.withdrawAmount  = amount
            this.balance =  currentBalance
            this.withdrawBalance = realAmount
            

        }
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }
   

  .device-head {
    background-color: #fff;
    
    display: flex;
    height: 112px;
    display: flex;
    align-items: center;
    padding: 0 24px;
    box-sizing: border-box;
    .device-head-icon {
        width: 48px;
        height: 48px;
        margin-right: 24px;
    }
    .device-info-wrap {
        flex: 1;
        .device-title-wrap {
            height: 32px;
            display: flex;
            align-items: center;
            .device-title {
                font-weight: 500;
                font-size: 24px;
                color: #12151A;
            }
            .device-status {
                // width: 50px;
                padding: 0 10px;
                box-sizing: border-box;
                height: 24px;
                border-radius: 10px 0 10px 0;
                font-size: 16px;
                font-weight: 400;
                line-height: 24px;
                text-align: center;
                color: #fff;
                background: linear-gradient(321.01deg, #00C864 8.79%, #38F3CA 100.27%);
                margin-left: 12px;
            }
        }
        .device-info-wrap {
            height: 16px;
            margin-top: 16px;
            font-size: 16px;
            font-weight: 400;
            color: #292B33;
        }
    }
    
  
  }
 

  .info-card {
    margin: 16px;
    border-radius: 5px;
    border: 1px solid #fff;
    overflow: hidden;
    background-color: #fff;
    // min-height: 300px;
    .card-head {
        height: 56px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        background: linear-gradient(180deg, #E9F2FF 0%, #FFFFFF 100%);
        .before-icon {
            width: 3px;
            height: 16px;
            background-image: url('~@/assets/station/consno-before.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            margin-right: 8px;
        }
        .card-head-text {
            flex:1;
            font-weight: 500;
            font-size: 16px;
            color: #12151A;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: -3px; /* 调整这个值来改变边框的宽度 */
            width: 0;
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
            border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
        }

    
        }

    }


    .form-wrap {
      padding: 0 24px 0 24px;
      .info-title {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #505363;
      }
      .info-detail {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #292B33;
      }
      .info-detail-overflow {
        max-width: 90%;
        word-wrap: break-word;
        word-break: break-all;
      }
      .info-amount {
        height: 28px;
        background-color: #FFF7E6;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 8px;
        font-weight: 400;
        font-size: 20px;
        color: #FE8921;
        margin-top: -6px;
        margin-right: 4px;

      }
      .info-detail-orange {
        display: flex;
        align-items: center;
        height: 28px;
        padding: 4px 8px;
        box-sizing: border-box;
        background-color: #FFF7E6;
        color: #FE8921;
        font-family: Oswald;
        font-weight: 400;
        font-size: 20px;
        border-radius: 3px;
      }
      .info-detail-blue {
        display: flex;
        align-items: center;
        height: 28px;
        padding: 4px 8px;
        box-sizing: border-box;
        background-color: #EBF3FF;
        color: #217AFF;
        font-family: Oswald;
        font-weight: 400;
        font-size: 20px;
        border-radius: 3px;
      }

    }

    .table-wrap {
        padding: 8px 24px 24px 24px;
    }

  }
 
  </style>
  