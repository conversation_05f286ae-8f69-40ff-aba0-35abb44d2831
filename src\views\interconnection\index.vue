<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
         class="buse-wrap-station"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">互联互通管理列表</div>
            <div class="top-button-wrap">
              <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
                新增互联互通
              </el-button>
            </div>
          </div>
          <div class="card-head-after"></div>
        </template>

        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button type="text" @click="handleDetail(row)">详情</el-button>
            <el-button type="text" @click="handleEdit(row)">编辑</el-button>
            <el-button type="text" @click="handleUseOrNot(row)">
              {{ row.enableFlag === '0' ? '启用' : '停用' }}
            </el-button>
            <el-button type="text" @click="handleDataPermission(row)">
              数据权限
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
  </div>
</template>

<script>
import {
  getInterconnectionList,
  enableInterconnection,
  disableInterconnection,
} from '@/api/interconnection/index';

export default {
  name: 'InterconnectionManagement',
  dicts: [
    'ls_charging_hlht_agreement',
    'ls_charging_hlht_agreement_version',
    'ls_charging_hlht_our_operator_type',
    'ls_charging_hlht_status',
  ],
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
        },
        {
          field: 'operatorCode',
          title: '渠道ID',
          minWidth: 120,
        },
        {
          field: 'operatorName',
          title: '渠道名称',
          minWidth: 120,
        },
        {
          field: 'ourOperatorType',
          title: '平台运营商类型',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_hlht_our_operator_type,
              cellValue
            );
          },
        },
        {
          field: 'ourOperatorId',
          title: '平台ID',
          minWidth: 120,
        },
        {
          field: 'agreement',
          title: '协议类型',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_hlht_agreement,
              cellValue
            );
          },
        },
        {
          field: 'agreementVersion',
          title: '协议版本号',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_hlht_agreement_version,
              cellValue
            );
          },
        },
        {
          field: '',
          title: '关联合同',
          minWidth: 120,
        },
        {
          field: 'updateTime',
          title: '更新时间',
          minWidth: 180,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        operatorCode: '',
        operatorName: '',
        ourOperatorId: '',
        ourOperatorType: '',
        enableFlag: '',
        updateTime: [],
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'operatorCode',
            title: '渠道ID',
            element: 'el-input',
            placeholder: '请输入',
          },
          {
            field: 'operatorName',
            title: '渠道名称',
            element: 'el-input',
            placeholder: '请输入',
          },
          {
            field: 'ourOperatorId',
            title: '平台ID',
            element: 'el-input',
            placeholder: '请输入',
          },
          {
            field: 'ourOperatorType',
            title: '平台运营商类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_hlht_our_operator_type,
            },
          },
          {
            field: 'updateTime',
            title: '更新时间',
            element: 'el-date-picker',
            props: {
                type: 'daterange',
                options: [],
                valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'enableFlag',
            title: '启用状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_hlht_status,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  created() {
    this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;

      const {updateTime} = this.params;
      let updateTimeLeft = ''
      let updateTimeRight = ''
      if(updateTime && updateTime.length > 0){
        updateTimeLeft = updateTime[0]
        updateTimeRight = updateTime[1]
      }

      const [err, res] = await getInterconnectionList({
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        ...this.params,
        updateTimeLeft,
        updateTimeRight,
      });
      this.loading = false;
      if (err) return;
      this.tableData = res?.data || [];
      this.tablePage.total = res?.total || 0;
    },

    handleAdd() {
      this.$router.push({
        path: '/v2g-charging/baseInfo/interconnection/create',
      });
    },

    handleDetail(row) {
      // 详情
      this.$router.push({
        path: '/v2g-charging/baseInfo/interconnection/detail',
        query: {
          operatorId: row.operatorId,
          type: 'detail',
        },
      });
    },

    handleEdit(row) {
      // 编辑
      this.$router.push({
        path: '/v2g-charging/baseInfo/interconnection/edit',
        query: {
          operatorId: row.operatorId,
          type: 'edit',
        },
      });
    },

    async handleUseOrNot(row) {
      if (row.enableFlag === '0') {
        // 当前状态为未启用，调用启用接口
        const [err, res] = await enableInterconnection(row.operatorId);
        if (err) return;
        console.log('res', res);
        this.$message.success('启用成功');
        this.loadData();
      } else {
        // 当前状态为已启用，调用禁用接口
        const [err, res] = await disableInterconnection(row.operatorId);
        if (err) return;
        console.log('res', res);
        this.$message.success('禁用成功');
        this.loadData();
      }
    },

    handleDataPermission(row) {
      this.$router.push({
        path: '/v2g-charging/baseInfo/interconnection/permission',
        query: {
          operatorCode: row.operatorCode,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  // background: #fff;
  // padding: 20px;
  // border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }

  .menu-box {
    .el-button {
      padding: 0 5px;
    }
  }
}
</style>
