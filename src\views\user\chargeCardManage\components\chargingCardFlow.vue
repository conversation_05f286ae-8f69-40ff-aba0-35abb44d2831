<template>
  <el-dialog title="充电卡流水" :visible.sync="dialogVisible" width="1598px">
    <div class="table-wrap">
      <BuseCrud
        style="margin-bottom: 16px; padding: 0 24px"
        ref="periodTableInfo"
        :filterOptions="manageFilterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :modalConfig="{ addBtn: false, menu: false }"
        :pagerProps="pagerProps"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div>
            <div class="card-head">
              <div class="card-head-text">充电卡流水列表</div>

              <div class="top-button-wrap">
                <el-button
                  class="set-btn"
                  type="primary"
                  @click="onClickExport()"
                >
                  <svg-icon iconClass="a-export-black"></svg-icon>
                  导出
                </el-button>
              </div>
            </div>
          </div>
        </template>
      </BuseCrud>
    </div>
  </el-dialog>
</template>
<script>
import { cardWater } from '@/api/user/chargeCardManage';
export default {
  props: {
    manageTableData: {
      type: Array,
      default: () => [],
    },
    id: {
      type: String,
      default: '',
    },
  },
  components: {},
  dicts: [
    'ls_charging_transaction_status', // 充电卡-交易状态
    'ls_charging_charge_transaction_type', // 充电卡-交易类型
  ],
  data() {
    return {
      checkedOrderList: [],
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      dialogVisible: false,
      settleStatusList: [
        { label: '未清分', value: '0' },
        { label: '已清分', value: '1' },
      ],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'transactionOrderNo',
          title: '业务订单号',
          minWidth: 190,
        },
        {
          field: 'transactionSerialNo',
          title: '交易流水号',
          minWidth: 190,
        },
        {
          field: 'transactionTime',
          title: '交易时间',
          minWidth: 120,
        },
        {
          field: 'transactionTypeName',
          title: '交易类型',
          minWidth: 100,
        },
        {
          field: 'transactionAmount',
          title: '交易金额',
          minWidth: 150,
        },
        {
          field: 'accountBalance',
          title: '账户余额',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'transactionStatusName',
          title: '交易状态',
          minWidth: 120,
          fixed: 'right',
        },
      ],

      tableData: [],
      params: {
        transactionSerialNo: '',
        transactionOrderNo: '',
        operator: '',
        transactionType: '',
        transactionStatus: '',
        transactionTime: [],
      },
      cardId: '',
    };
  },
  watch: {
    dialogVisible(value) {
      if (value) {
        this.loadData();
      }
    },
  },
  computed: {
    manageFilterOptions() {
      return {
        config: [
          {
            field: 'transactionSerialNo',
            title: '交易流水编号：',
            element: 'el-input',
          },
          {
            field: 'transactionOrderNo',
            title: '业务订单号：',
            element: 'el-input',
          },
          {
            field: 'operator',
            title: '操作员：',
            element: 'el-input',
          },
          {
            field: 'transactionType',
            title: '交易类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_charge_transaction_type,
            },
          },
          {
            field: 'transactionTime',
            title: '交易时间：',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              options: [],
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
            },
          },
          {
            field: 'transactionStatus',
            title: '交易状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_transaction_status,
            },
          },
        ],
        params: this.params,
      };
    },
  },
  mounted() {},
  methods: {
    onClickExport() {
      const params = {
        ...this.manageFilterOptions.params,
        cardId: this.cardId || '',
      };

      if (params.transactionTime && params.transactionTime.length > 0) {
        params.transactionTimeStart = params.transactionTime[0];
        params.transactionTimeEnd = params.transactionTime[1];
      }

      this.download(
        '/vehicle-charging-admin/card/cardWater/export',
        {
          ...params,
        },
        `充电卡流水列表.xlsx`
      );
    },
    handleCancel() {
      this.dialogVisible = false;
    },

    // 获取管理列表数据
    async loadData() {
      const params = {
        ...this.manageFilterOptions.params,
      };

      if (params.transactionTime && params.transactionTime.length > 0) {
        params.transactionTimeStart = params.transactionTime[0];
        params.transactionTimeEnd = params.transactionTime[1];
      }

      const [err, res] = await cardWater({
        ...params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        cardId: this.cardId || '',
      });

      if (err) return;
      const { data, total } = res;

      this.tableData = data;
      this.tablePage.total = total;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-tabs {
  margin-top: 16px;
  background-color: #f5f6f9;
  .el-tabs__header {
    padding-left: 0;
    display: flex;
    justify-content: center;
    text-align: center;
    margin-bottom: 1px;
    .el-tabs__item {
      padding: 0;
      width: 164px;
      font-size: 18px;
      font-weight: 400;
      background-color: #fff;
    }
    .el-tabs__item.is-active {
      background-color: #1677fe;
      color: #fff;
    }
    .el-tabs__nav-scroll {
      border-radius: 25px;
      border: solid 1px #dfe1e5;
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__nav-wrap::after {
      width: 0;
    }
  }
}

.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }
  margin: 16px;

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
    .set-btn {
      background-color: #ffffff;
      color: #292b33;
      border-color: #dfe1e5;
    }
  }
}

.button-border {
  border: 0.01rem solid #217aff;
  color: #217aff;
  background-color: #fff;
}
.bottom-wrap {
  // position: fixed;
  // bottom: 0;
  // left: 0;
  // width: 100%;
  // height: 86px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-color: #ffffff;
  padding-right: 32px;
  box-sizing: border-box;
}
::v-deep .bd3001-content {
  padding: 0 !important;
}
</style>
