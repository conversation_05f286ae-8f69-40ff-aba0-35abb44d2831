import Vue from 'vue';
// import { setToken } from '@/utils/auth'

// setToken('1234')

import VXETable from 'vxe-table';
import 'vxe-table/lib/style.css';
// VXETable.setConfig({
//   table: {
//     stripe: true,
//     border: true,
//     // round: true
//   },
//   grid: {
//     stripe: true,
//     border: true,
//   }
// })
Vue.use(VXETable);

import BuseCrud from 'buse-components-element';
Vue.use(BuseCrud, {
  BuseCrud: {
    tableProps: {
      stripe: true,
      border: 'none',
      round: true,
    },
    pagerProps: {
      layouts: [
        'Total',
        'PrevPage',
        'JumpNumber',
        'PageCount',
        'NextPage',
        'Sizes',
      ],
    },
  },
  AutoFilters: {
    showCount: 5,
  },
});
