<template>
    <div class="container container-float ">
        <div class="table-wrap">
            <BuseCrud
                ref="crud"
                :loading="loading"
                :filterOptions="filterOptions"
                :tablePage="tablePage"
                :tableColumn="tableColumn"
                :tableData="tableData"
                :pagerProps="pagerProps"
                :modalConfig="modalConfig"
                 class="buse-wrap-station"
                @loadData="loadData"
            >
            <template slot="defaultHeader">
                <div>
                    <div class="card-head">
                        <div class="card-head-text">计费模型管理</div>

                        <div class="top-button-wrap">
                            
                            <el-button
                                type="primary"
                                @click="() => handleAdd()"
                            >
                                新增计费模型
                            </el-button>

                        </div>
                    </div>
                    

                    
                </div>
                
            </template>

                <template slot="operate" slot-scope="{ row }">
                    <!-- <div class="menu-box">
                        <el-button
                            class="add-btn"
                            @click="handleAudit(row, 'detail')"
                        >
                            详情
                        </el-button>

                        <el-button
                            v-if="row.showEditBtn"
                            type="primary"
                            plain
                            @click="handleEdit(row)"
                        >
                            修改
                        </el-button>

                        <el-button
                            type="primary"
                            plain
                            @click="handleAudit(row, 'index')"
                        >
                            审核
                        </el-button>

                        <el-button
                            v-if="row.showDelBtn"
                            type="primary"
                            plain
                            @click="handleDelete(row)"
                        >
                            删除
                        </el-button>



                    
                    </div> -->

                    <el-dropdown trigger="click">
                      <el-button
                        class="button-border"
                        type="primary"
                        plain
                      >
                        操作
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item >
                          <div    @click="handleAudit(row, 'detail')">
                            详情
                          </div>
                        </el-dropdown-item>

                        <el-dropdown-item >
                          <div v-if="row.showEditBtn"    @click="handleEdit(row)">
                            修改
                          </div>
                        </el-dropdown-item>


                        <el-dropdown-item v-show="row.operationStatus === '05' ">
                          <div v-if="row.showDelBtn"   @click="handleDelete(row)">
                            删除
                          </div>
                        </el-dropdown-item>

                        

                      </el-dropdown-menu>
                    </el-dropdown>
                
                </template>

            </BuseCrud>
        </div>
    </div>
    
  </template>
  
  <script>

import {
    getAreaList,
  } from "@/api/electricPricePeriod/index";
import {
    getBillModelList,
    deleteBillModel,
} from '@/api/billingModel/index'
import StatusDot from '@/components/Business/StatusDot';
import StatusInfo from '@/components/Business/StatusInfo';

  
    export default {
    components: {
        StatusDot,
        StatusInfo,
    },
    dicts: [
        'ls_charging_billing_charge_mode',
        'ls_charging_billing_charge_type',
        'ls_charging_billing_template_status',
        'ls_charging_audit_type',
        'ls_charging_contracted_unit',
    ],
    data() {
      return {
        loading: false,
        tablePage: { total: 0, currentPage: 1, pageSize: 10 },
        tableColumn:[
            {
                type: 'seq',
                title: '序号',
                width: 60,
                minWidth: 60,
                fixed: 'left',
            },
            {
                field: 'chcNo',
                title: '计费编号',
                minWidth: 130, 
            },
            {
                field: 'chcName',
                title: '计费名称',
                minWidth: 220, 
            },
            {
                field: 'chargeMode',
                title: '计费模式',
                minWidth: 100,
                formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                            this.dict.type.ls_charging_billing_charge_mode,
                            cellValue
                        );
                    },
            },
            // {
            //     field: 'itemNo',
            //     title: '收费类型',
            //     minWidth: 100,
            //     formatter: ({ cellValue }) => {
            //             return this.selectDictLabel(
            //                 this.dict.type.ls_charging_billing_charge_type,
            //                 cellValue
            //             );
            //         },
            // },
            // {
            //     field: 'marketingPriceName',
            //     title: '营销电价名称',
            //     minWidth: 150, 
            // },
            // {
            //     field: 'marketingPriceType',
            //     title: '营销电价类型',
            //     minWidth: 150,
            // },
            // {
            //     field: 'consistentWithMarketingPrice',
            //     title: '是否与营销电价一致',
            //     minWidth: 180,
            // },
            {
                field: 'suitCityName',
                title: '适用地市',
                minWidth: 100,
            },
            {
                field: 'stationCount',
                title: '关联充电站',
                minWidth: 120,
            },
            {
                field: 'pileCount',
                title: '关联充电桩',
                minWidth: 120,
            },
            {
                field: 'createUnit',
                title: '申请单位',
                minWidth: 220,  
                 formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                            this.dict.type.ls_charging_contracted_unit,
                            cellValue
                        );
                    },
            },
            {
                field: 'createUser',
                title: '申请人',
                minWidth: 100,
            },
            {
                field: 'createTime',
                title: '创建时间',
                minWidth: 180,
            },
            {
                field: 'chcStatus',
                title: '计费模型状态',
                minWidth: 120,
                fixed: 'right',
                // formatter: ({ cellValue }) => {
                //         return this.selectDictLabel(
                //             this.dict.type.ls_charging_billing_template_status,
                //             cellValue
                //         );
                //     },
                    slots: {
                    // 自定义render函数
                default: ({ row }) => {
                        return (
                            <StatusInfo
                                value={row.chcStatus}
                                dictValue={this.dict.type.ls_charging_billing_template_status}
                                colors={['', 'success', ]}
                            ></StatusInfo>
                            );
                        },
                    },
            },
            {
                field: 'approvalStatusc',
                title: '审核状态',
                minWidth: 110,
                fixed: 'right',
                slots: {
                    // 自定义render函数
                    default: ({ row }) => {
                        return (
                            row.approvalStatus?
                        <StatusDot
                            value={row.approvalStatus}
                            dictValue={this.dict.type.ls_charging_audit_type}
                            colors={['warning', 'success', 'danger']}
                        ></StatusDot>
                        :
                        <div style="display: flex; align-items: center; ">
                                <span class="status-dot"></span>
                                待提交
                           </div>
                        );
                    },
                },
            },
            {
                title: '操作',
                slots: { default: 'operate' },
                width: 80,
                align: 'center',
                fixed: 'right',
            },
        ],
        tableData: [],
        pagerProps: {
            layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
        },
        params: {
            chcNo: '',
            chcName: '',
            chargeMode: '',
            // itemNo: '',
            chcStatus: '',
            approvalStatus: '',
            marketingPriceType: '',
            marketingPriceName: '',
            suitCityCode:  '',
        },

        areaList: [],

      };
    },

    computed: {
        filterOptions() {
            return {
                config: [
                    {
                        field: 'chcNo',
                        title: '计费编号',
                        element: 'el-input',
                        props: { placeholder: '请输入' }  // 图片中对应输入框提示文字
                    },
                    {
                        field: 'chcName',
                        title: '计费名称',
                        element: 'el-input',
                        props: { placeholder: '请输入' }
                    },
                    {
                        field: 'chargeMode',
                        title: '计费模式',
                        element: 'el-select',
                        props: {
                            placeholder: '请选择',
                            options:  this.dict.type.ls_charging_billing_charge_mode, 
                        }
                    },
                    // {
                    //     field: 'itemNo',
                    //     title: '收费类型',
                    //     element: 'el-select',
                    //     props: { 
                    //         placeholder: '请选择',
                    //         options: this.dict.type.ls_charging_billing_charge_type, 
                    //     }
                    // },
                    {
                        field: 'chcStatus',
                        title: '计费模型状态',
                        element: 'el-select',
                        props: { 
                        placeholder: '请选择',
                        options: this.dict.type.ls_charging_billing_template_status,
                        }
                    },
                    {
                        field: 'approvalStatus',
                        title: '审核状态',
                        element: 'el-select',
                        props: { 
                            placeholder: '请选择',
                            options: this.dict.type.ls_charging_audit_type, 
                        }
                    },
                    // {
                    //     field: 'marketingPriceType',
                    //     title: '营销电价类型',  // 注意标题与字段名对应
                    //     element: 'el-select',
                    //     props: { 
                    //     placeholder: '请选择',
                    //     options: []
                    //     }
                    // },
                    // {
                    //     field: 'marketingPriceName',
                    //     title: '营销电价名称',
                    //     element: 'el-input',
                    //     props: { placeholder: '请输入' }
                    // },
                    {
                        field: 'suitCityCode',
                        title: '适用地市',
                        element: 'el-select',
                        props: { 
                            placeholder: '请选择',
                            options: this.areaList,
                        }
                    }
                ],
                params: this.params,
            };
        },

        modalConfig() {
            return {
                addBtn: false,
                viewBtn: false,
                menu: false,
                editBtn: false,
                delBtn: false,
            }
        },
    },
    mounted() {
        this.getAreaList();
        this.loadData();
    },
    methods: {
        // 获取适用地市
        async getAreaList() {
            const [err, res] = await getAreaList({
                areaLevel: '03',
                huNanOnly: true
            })
            if (err) return
            const {
                data
            } = res
            const list = []
            data.forEach(item => {
                list.push({
                    label: item.areaName,
                    value: item.areaCode,
                })
            })
            this.areaList = list
            
        },
        async loadData() {
                const  {
                    chcNo,
                    chcName,
                    chargeMode,
                    // itemNo,
                    chcStatus,
                    approvalStatus,
                    suitCityCode,
                } = this.params;

                const params = {
                    chcNo,
                    chcName,
                    chargeMode,
                    // itemNo,
                    chcStatus,
                    approvalStatus,
                    suitCityCode,
                    pageNum: this.tablePage.currentPage,
                    pageSize: this.tablePage.pageSize,
                }


                this.loading = true;

                const [err, res] = await getBillModelList(params)

                this.loading = false;
                
                if (err) return 
                const { data, total } = res;
                this.tableData = data;
                this.tablePage.total = total;
        },

         // 新增计费模型
         handleAdd() {
            this.$router.push({
                path: '/v2g-charging/operatorManage/chargingManage/billingModel/create',
            })
        },

        // 修改计费模型
        handleEdit(row) {
            const {
                chcNo
            } = row;
            this.$router.push({
                path: '/v2g-charging/operatorManage/chargingManage/billingModel/create',
                query: {
                    chcNo,
                }
            })
        },

        // 删除计费模型
        async handleDelete(row) {
            const {
                chcNo,
                chcName
            } = row;
            this.$confirm(`确认删除计费模型-${chcName}吗`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(async() => {
                    const [err,res] = await deleteBillModel(
                        {
                            chcNo 
                        }
                    )

                    if(err) {
                        return this.$message.error(err.message || '删除计费模型失败');
                    }

                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                    this.loadData();

                })
                .catch(() => {});
        },
        // 审核
        handleAudit(row,type) {
            const { chcNo } = row;
            this.$router.push({
                path: '/v2g-charging/operatorManage/chargingManage/billingModel/audit',
                query: {
                    chcNo,
                    type,
                },
            })
        },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }

  .table-wrap {
    ::v-deep .bd3001-table-select-box {
        display: none;
    }
    ::v-deep .bd3001-header  {
        display: block;
    }
    ::v-deep .bd3001-button {
        display: block !important;
    }
    
    .card-head {
        // position: relative; 
        height: 56px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        margin-top: -20px;
        .card-head-text {
        flex:1;
        width: 520px;
            height: 26px;
            background-image: url('~@/assets/images/bg-title.png');
            background-size: 520px 26px;
            background-repeat: no-repeat;
            font-size: 20px;
            font-style: normal;
            font-weight: 500;
            line-height: 16px;
            padding-left: 36px;
            color: #21252e;
        &::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: -3px; /* 调整这个值来改变边框的宽度 */
            width: 0;
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
            border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
        }
        }   

    }

    .card-head-after {
        width: 100%;
        height: 1px;
        background-color: #DCDEE2;
        margin-bottom: 16px;
    }
    .info-wrap {
        margin-top: 16px;
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .info-item {
            background-color: #FAFBFC;
            flex: 1 1 0;
            // min-width: 180px;
            
            border-radius: 5px;
            padding: 8px 24px;
            box-sizing: border-box;
            // margin-right: 16px;
            display: flex;
            .info-icon {
                width: 42px;
                height: 42px;
            }
            .info-right-wrap {
                flex:1;
                margin-left: 8px;
                .info-title {
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 14px;
                    margin-bottom: 8px;
                }
                .info-number {
                    font-size: 20px;
                    font-weight: 500;
                    .info-unit {
                        font-size: 14px;
                        font-weight: 400;
                    }
                }
            }
        }
        .info-item:last-child {
            margin-right: 0;
        }
    }

    .top-button-wrap {
        display:flex;
        margin: 16px 0;
    }
}
.status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 8px;
    background-color: #ff8d24;
  }


  .button-border {
    border: 0.01rem solid #217AFF;
    color: #217AFF;
    background-color: #fff;
}
  </style>
  