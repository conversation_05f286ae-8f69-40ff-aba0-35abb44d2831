/**
 * @file 早期预警管理API
 */

import request from '@/utils/request';

const baseUrl = '/vehicle-charging-admin';

/**
 * 获取价格阈值分页列表
 * @param {Object} params - 请求参数
 * @param {number} [params.pageNum=1] - 当前页码，默认为1
 * @param {number} [params.pageSize=10] - 每页显示条数，默认为10
 * @param {string} [params.stationId] - 站点ID
 * @param {string} [params.stationNo] - 站点编号
 * @param {string} [params.stationName] - 站点名称
 * @param {string} [params.city] - 城市
 * @param {string} [params.assetProperty] - 资产属性
 * @param {string} [params.stationType] - 站点类型
 * @returns {Promise} 返回包含价格阈值分页列表的Promise
 */
export function getPriceThresholdPage(params) {
  return request({
    url: baseUrl + '/warning/billing/priceThreshold/page',
    method: 'post',
    data: params,
  });
}

/**
 * 创建价格阈值
 * @param {Object} data - 请求数据
 * @param {Array<string>} [data.ids] - 主键id集合（批量更新时使用）
 * @param {Array<string>} [data.stationIds] - 关联场站ID集合（新增时使用）
 * @param {number} data.serviceTopThreshold - 尖 - 服务费阈值
 * @param {number} data.servicePeakThreshold - 峰 - 服务费阈值
 * @param {number} data.serviceFlatThreshold - 平 - 服务费阈值
 * @param {number} data.serviceValleyThreshold - 谷 - 服务费阈值
 * @returns {Promise} 返回创建结果的Promise
 */
export function createPriceThreshold(data) {
  return request({
    url: baseUrl + '/warning/billing/priceThreshold/create',
    method: 'post',
    data: data,
  });
}

/**
 * 批量修改价格阈值
 * @param {Object} data - 请求数据
 * @param {Array<string>} [data.ids] - 主键id集合（批量更新时使用）
 * @param {Array<string>} [data.stationIds] - 关联场站ID集合（新增时使用）
 * @param {number} data.serviceTopThreshold - 尖 - 服务费阈值
 * @param {number} data.servicePeakThreshold - 峰 - 服务费阈值
 * @param {number} data.serviceFlatThreshold - 平 - 服务费阈值
 * @param {number} data.serviceValleyThreshold - 谷 - 服务费阈值
 * @returns {Promise} 返回批量修改结果的Promise
 */
export function batchUpdatePriceThreshold(data) {
  return request({
    url: baseUrl + '/warning/billing/priceThreshold/batchUpdate',
    method: 'post',
    data: data,
  });
}

/**
 * 配置详情
 * @param {Object} params - 请求参数 (GET 请求无请求体，此处为示例)
 * @returns {Promise<Object>} 返回配置详情数据，包含规则类型、启用状态、通知渠道等信息
 */
export function getBillingConfigDetail() {
  return request({
    url: baseUrl + '/warning/billing/detail',
    method: 'get',
  });
}

/**
 * 获取预警记录分页列表
 * @param {Object} params - 请求参数
 * @param {number} [params.pageNum=1] - 当前页码，默认为1
 * @param {number} [params.pageSize=10] - 每页显示条数，默认为10
 * @param {string} [params.notifyRecordBillingId] - 计费预警流水编号
 * @param {string} [params.pileNo] - 桩编号
 * @param {string} [params.pileName] - 桩名称
 * @param {string} [params.stationId] - 站点id
 * @param {string} [params.chcNo] - 充电计费编号
 * @param {string} [params.issueUser] - 下发人
 * @param {string} [params.warningTimeLeft] - 预警时间左区间
 * @param {string} [params.warningTimeRight] - 预警时间右区间
 * @returns {Promise} 返回包含预警记录分页列表的Promise
 */
export function getWarningRecordPage(params) {
  return request({
    url: baseUrl + '/warning/billing/record/page',
    method: 'post',
    data: params,
  });
}

/**
 * 导出预警记录
 * @param {Object} params - 请求参数
 * @param {number} [params.pageNum=1] - 当前页码，默认为1
 * @param {number} [params.pageSize=10] - 每页显示条数，默认为10
 * @param {string} [params.notifyRecordBillingId] - 计费预警流水编号
 * @param {string} [params.pileNo] - 桩编号
 * @param {string} [params.pileName] - 桩名称
 * @param {string} [params.stationId] - 站点id
 * @param {string} [params.chcNo] - 充电计费编号
 * @param {string} [params.issueUser] - 下发人
 * @param {string} [params.warningTimeLeft] - 预警时间左区间
 * @param {string} [params.warningTimeRight] - 预警时间右区间
 * @returns {Promise} 返回导出结果的Promise
 */
export function exportWarningRecord(params) {
  return request({
    url: baseUrl + '/warning/billing/record/export',
    method: 'post',
    data: params,
  });
}

// 模版列表
/**
 * 获取通知模板列表（根据渠道）
 *
 * @param {Object} params - 请求参数
 * @param {string} params.notifyChannel - 通知渠道类型（web、app、小程序、短信）
 * @returns {Promise<{code: string, msg: string, data: Array<{templateId: string, name: string}>, total: number}>} 返回模板列表数据
 */
export function templateList(params) {
  return request({
    url: baseUrl + '/warning/message/template/list',
    method: 'post',
    data: params,
  });
}

// 规则信息配置 （新增/修改）
/**
 * 新增或修改计费预警规则信息
 *
 * @param {Array<RuleConfig>} data - 规则配置数组，包含以下字段：
 * @param {string} data.id - 主键ID（编辑时传入）
 * @param {string} data.ruleType - 规则类型（如："01" 表示超充等）
 * @param {number} data.enabled - 是否启用（0=停用；1=启用）
 * @param {Object} [data.paramMap] - 规则参数（仅规则类型为 "01" 时传入，例如 { value: 30, unit: "分钟" }）
 * @param {Array<NotifyChannel>} data.notifyChannels - 通知渠道配置列表
 * @param {string} notifyChannel.notifyChannelType - 通知渠道类型（如："web", "app" 等）
 * @param {string} notifyChannel.notifyTemplateId - 推送模板ID
 *
 * @returns {Promise<boolean>} 操作是否成功
 */
export function billingConfig(params) {
  return request({
    url: baseUrl + '/warning/billing/config',
    method: 'post',
    data: params,
  });
}

// 区域查询
/**
 * 区域查询（支持多条件筛选）
 *
 * @param {Object} params - 请求参数
 * @param {string} [params.areaCode] - 区域代码
 * @param {string} [params.areaLevel] - 区域等级（01-国家；02-省/直辖市；03-市；04-镇/乡/区/县）
 * @param {string} [params.upAreaCode] - 上级区域代码
 * @param {string} [params.areaStatus] - 状态（01=未启动；02=启动）
 * @param {boolean} [params.huNanOnly=false] - 是否仅查询湖南省区域
 * @returns {Promise<{code: string, msg: string, data: Array<Area>, total: number}>} 返回区域数据列表
 */
export function areaList(params) {
  return request({
    url: baseUrl + '/area/list',
    method: 'post',
    data: params,
  });
}

// 计费预警记录 - 分页列表
/**
 * 获取计费预警记录分页列表
 *
 * @param {Object} params - 请求参数
 * @param {number} [params.pageNum=1] - 当前页码，默认为1
 * @param {number} [params.pageSize=10] - 每页显示条数，默认为10
 * @param {string} [params.notifyRecordBillingId] - 计费预警流水编号
 * @param {string} [params.pileNo] - 桩编号
 * @param {string} [params.pileName] - 桩名称
 * @param {string} [params.stationId] - 站点id
 * @param {string} [params.chcNo] - 充电计费编号
 * @param {string} [params.issueUser] - 下发人
 * @param {string} [params.warningTimeLeft] - 预警时间左区间（格式：yyyy-MM-dd HH:mm:ss）
 * @param {string} [params.warningTimeRight] - 预警时间右区间（格式：yyyy-MM-dd HH:mm:ss）
 * @returns {Promise<{code: string, msg: string, data: Array<BillingWarningRecord>, total: number}>} 返回包含数据和总条数的对象
 */
/**
 * @typedef {Object} BillingWarningRecord
 * @property {string} notifyRecordBillingId - 计费预警流水编号
 * @property {string} warningBizType - 预警业务类型
 * @property {string} warningConfigId - 预警规则配置id
 * @property {string} warningConfigName - 预警规则配置名称
 * @property {string} pileId - 充电桩id
 * @property {string} pileNo - 桩编号
 * @property {string} pileName - 桩名称
 * @property {string} stationId - 站点id
 * @property {string} stationName - 站点名称
 * @property {string} chcNo - 充电计费编号
 * @property {string} issueUser - 下发人
 * @property {string} issueTime - 下发时间
 * @property {string} warningTime - 预警时间
 */
export function billingPage(params) {
  return request({
    url: baseUrl + '/warning/record/billing/page',
    method: 'post',
    data: params,
  });
}

// 计费预警记录详情
/**
 * 获取预警记录详情
 *
 * @param {Object} params - 请求参数
 * @param {string} params.warningBizType - 预警业务类型
 * @param {string} params.warningNotifyRecordId - 预警业务通知流水编号
 * @returns {Promise<{code: string, msg: string, data: Array<WarningDetail>, total: number}>} 返回详情数据
 */
/**
 * @typedef {Object} WarningDetail
 * @property {string} notifyRecordId - 预警记录编号
 * @property {string} warningBizType - 预警业务类型
 * @property {string} warningNotifyRecordId - 预警业务通知流水编号
 * @property {string} notifyChannel - 通知渠道类型
 * @property {string} notifyTemplateId - 推送模板ID
 * @property {string} notifyUserId - 预警通知人的ID
 * @property {string} notifyUserName - 预警通知人名称
 * @property {string} notifyContent - 预警通知内容
 * @property {string} notifyResult - 通知结果：0=未成功，1=成功
 * @property {string} notifyTime - 通知时间
 */
export function recordDetail(params) {
  return request({
    url: baseUrl + '/warning/record/detail',
    method: 'post',
    data: params,
  });
}

// 订单预警记录 - 导出
/**
 * 导出订单预警记录
 *
 * @param {Object} params - 查询参数，同分页查询
 * @see orderPage
 * @returns {Promise<Blob>} 返回文件 Blob 数据
 */
export function orderExport(params) {
  return request({
    url: baseUrl + '/warning/record/order/export',
    method: 'post',
    data: params,
  });
}
