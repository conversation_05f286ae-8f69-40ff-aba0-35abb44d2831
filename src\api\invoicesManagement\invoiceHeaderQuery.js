import request from '@/utils/request';

const baseUrl = '/vehicle-grid-system';

// 分页查询买方信息(发票抬头)
export function getInvBuyerConfigByPage(data) {
  return request({
    url: baseUrl + '/invBuyerConfig/getInvBuyerConfigByPage',
    method: 'post',
    data: data,
  });
}

// 新增买方信息(发票抬头)
export function addBuyerConfig(data) {
  return request({
    url: baseUrl + '/invBuyerConfig/addBuyerConfig',
    method: 'post',
    data: data,
  });
}

// 修改买方信息(发票抬头)
export function updateBuyerConfig(data) {
  return request({
    url: baseUrl + '/invBuyerConfig/updateBuyerConfig',
    method: 'post',
    data: data,
  });
}

// 删除买方信息(发票抬头)
export function deleteBuyerConfig(data) {
  return request({
    url: baseUrl + '/invBuyerConfig/deleteBuyerConfig',
    method: 'post',
    data: data,
  });
}
