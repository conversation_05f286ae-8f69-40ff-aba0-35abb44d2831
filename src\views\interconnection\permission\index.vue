<template>
  <div class="container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :pagerProps="pagerProps"
      :modalConfig="modalConfig"
      :tableOn="{
        'checkbox-change': handleCheckboxChange,
        'checkbox-all': handleCheckboxChange,
      }"
      @loadData="getPermissionList"
      @handleReset="handleReset"
    >
      <template slot="region">
        <el-cascader
          v-model="params.region"
          :options="regionTree"
          :props="{
            checkStrictly: true,
            value: 'areaCode',
            label: 'areaName',
            children: 'children',
          }"
          clearable
          placeholder="请选择省/市/区"
          :style="{ width: '100%' }"
        />
      </template>
      <template slot="defaultHeader">
        <div class="card-head">
          <div class="card-head-text">数据权限</div>
          <div class="button-group">
            <el-button type="danger" plain @click="handleBatchRemove">
              批量移除
            </el-button>
            <el-button
              type="primary"
              icon="el-icon-plus"
              @click="handleSelectStation"
            >
              选择充电站
            </el-button>
          </div>
        </div>
        <div class="card-head-after"></div>
      </template>
      <template slot="operate" slot-scope="{ row }">
        <div class="menu-box">
          <!-- <el-button type="text" @click="handleDetail(row)">详情</el-button> -->
          <el-button type="text" @click="handleRemove(row)">移除</el-button>
        </div>
      </template>
    </BuseCrud>
    <SelectStationModal
      ref="selectStationDialog"
      :operatorCode="operatorCode"
      @getList="getPermissionList"
    />
  </div>
</template>

<script>
import SelectStationModal from './components/selectStationModal.vue';
import {
  getPermissionList,
  removePermission,
  batchRemovePermission,
  getAreaList,
} from '@/api/interconnection/index';

export default {
  name: 'InterconnectionDataPermission',
  dicts: ['ls_charging_operation_status', 'ls_station_type'],
  components: {
    SelectStationModal,
  },
  data() {
    return {
      loading: false,
      tableColumn: [
        { type: 'checkbox', width: 30 },
        { type: 'seq', title: '序号', width: 60 },
        { field: 'stationNo', title: '站点编号', minWidth: 120 },
        { field: 'stationName', title: '站点名称', minWidth: 120 },
        {
          field: 'stationType',
          title: '站点类型',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_station_type,
              cellValue
            );
          },
        },
        { field: 'stationAddress', title: '站点地址', minWidth: 200 },
        { field: 'assetName', title: '资产单位', minWidth: 120 },
        { field: 'operName', title: '运营单位', minWidth: 120 },
        { field: 'maintainName', title: '运维单位', minWidth: 120 },
        { field: 'createTime', title: '添加时间', minWidth: 160 },
        {
          field: 'operationStatus',
          title: '运营状态',
          minWidth: 100,
          fixed: 'right',
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_operation_status,
              cellValue
            );
          },
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 200,
          align: 'center',
          fixed: 'right',
        },
      ],
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      pagerProps: {
        pageSizes: [10, 20, 50, 100],
        layout: 'total, sizes, prev, pager, next, jumper',
      },
      params: {
        region: [],
        stationName: '',
        stationNo: '',
        stationType: '',
        operationStatus: '',
      },
      selectedRows: [],
      operatorCode: '',
      selectedIds: [],
      regionTree: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            title: '省市区',
            field: 'region',
            element: 'slot',
            slotName: 'region',
          },
          {
            title: '站点名称',
            field: 'stationName',
            element: 'el-input',
            placeholder: '请输入',
          },
          {
            title: '站点编号',
            field: 'stationNo',
            element: 'el-input',
            placeholder: '请输入',
          },
          {
            title: '站点类型',
            field: 'stationType',
            element: 'el-select',
            options: [],
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_station_type,
            },
          },
          {
            title: '运营状态',
            field: 'operationStatus',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_operation_status,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  created() {
    const { operatorCode = '' } = this.$route.query || {};
    this.operatorCode = operatorCode;
    this.getPermissionList();
    this.loadAreaList();
  },
  methods: {
    async getPermissionList() {
      this.loading = true;
      const [province, city, county] = this.params.region || [];
      const [err, res] = await getPermissionList({
        operatorCode: this.operatorCode,
        ...this.params,
        province,
        city,
        county,
      });
      this.loading = false;
      if (err) return;
      this.tableData = res?.data || [];
    },
    // 获取区域列表
    async loadAreaList() {
      const [err, res] = await getAreaList({});
      if (err) return;
      if (res?.data?.length && Array.isArray(res.data)) {
        const areaList = res.data;
        // 区县级
        const districtList = areaList
          .filter((item) => item.areaLevel === '04')
          .map((x) => ({
            areaCode: x.areaCode,
            areaName: x.areaName,
            parentCode: x.upAreaCode,
          }));

        // 市级
        const cityList = areaList
          .filter((item) => item.areaLevel === '03')
          .map((x) => ({
            areaCode: x.areaCode,
            areaName: x.areaName,
            parentCode: x.upAreaCode,
            children: districtList.filter((y) => y.parentCode === x.areaCode),
          }));

        // 省级
        this.regionTree = areaList
          .filter((item) => item.areaLevel === '02')
          .map((x) => ({
            areaCode: x.areaCode,
            areaName: x.areaName,
            parentCode: x.upAreaCode,
            children: cityList.filter((y) => y.parentCode === x.areaCode),
          }));
      }
    },
    handleCheckboxChange({ records }) {
      this.selectedIds = records.map((item) => item.id);
    },
    async handleBatchRemove() {
      if (this.selectedIds.length === 0) {
        this.$message.warning('请选择要移除的数据');
        return;
      }
      this.loading = true;
      const [err, res] = await batchRemovePermission({
        ids: this.selectedIds,
      });
      this.loading = false;
      if (err) return;
      this.$message.success('批量移除成功');
      this.getPermissionList();
    },
    // handleDetail(row) {},
    async handleRemove(row) {
      this.loading = true;
      const [err, res] = await removePermission({
        id: row.id,
      });
      this.loading = false;
      if (err) return;
      this.$message.success('移除成功');
      this.getPermissionList();
    },
    handleSelectStation() {
      this.$refs.selectStationDialog.show();
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 16px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }
}

.card-head {
  height: 56px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .card-head-text {
    flex: 1;
    width: 520px;
    height: 26px;
    background-image: url('~@/assets/images/bg-title.png');
    background-size: 520px 26px;
    background-repeat: no-repeat;
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
    padding-left: 36px;
    color: #21252e;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      left: -3px; /* 调整这个值来改变边框的宽度 */
      width: 0;
      border-top: 3px solid transparent;
      border-bottom: 3px solid transparent;
      border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
    }
  }
}

.card-head-after {
  width: 100%;
  height: 1px;
  background-color: #dcdee2;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 16px;
}
</style>
