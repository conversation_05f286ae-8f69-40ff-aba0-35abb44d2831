<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <div class="table-wrap">
      <div class="card-head">
        <div class="card-head-text">清分汇总报表历史记录</div>
      </div>
      <div class="info-wrap">
        <el-row :gutter="20" style="width: 100%; margin: 0 0 10px 10px">
          <el-col :span="8">
            <span class="label">充电站编号：</span>
            <span class="value">{{ 1 }}</span>
          </el-col>
          <el-col :span="8">
            <span class="label">站点类型：</span>
            <span class="value">{{ 2 }}</span>
          </el-col>
          <el-col :span="8">
            <span class="label">运营模式：</span>
            <span class="value">{{ 3 }}</span>
          </el-col>
        </el-row>
      </div>
      <BuseCrud
        ref="crud"
        :loading="loading"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        @loadData="loadData"
      ></BuseCrud>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      stationInfo: {
        stationCode: 'STN123456',
        stationType: '快充站',
        operationMode: '自营',
      },
      // 表格列定义
      tableColumn: [
        { type: 'seq', title: '序号', minWidth: 60 },
        { field: 'clearingCycle', title: '清分周期', minWidth: 150 },
        { field: 'totalEnergy', title: '充电总电量（KWH）', minWidth: 180 },
        {
          field: 'clearingElectricityFee',
          title: '清分充电电费（元）',
          minWidth: 170,
        },
        {
          field: 'clearingServiceFee',
          title: '清分充电服务费（元）',
          minWidth: 180,
        },
        {
          field: 'clearingTotalAmount',
          title: '清分充电总金额（元）',
          minWidth: 180,
        },
        { field: 'generateTime', title: '生成时间', minWidth: 160 },
        { field: 'operator', title: '操作人', minWidth: 120 },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
    };
  },
  computed: {
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  methods: {
    loadData(page = this.tablePage) {
      this.loading = true;
      console.log('加载数据', page);
      setTimeout(() => {
        this.tableData = [
          {
            clearingCycle: '2024-09-01 至 2024-09-30',
            totalEnergy: '12000 KWH',
            clearingElectricityFee: '12000.00',
            clearingServiceFee: '1200.00',
            clearingTotalAmount: '13200.00',
            generateTime: '2024-10-01 10:00:00',
            operator: '张三',
          },
        ];
        this.tablePage.total = 1;
        this.loading = false;
      }, 500);
    },
    handleView(row) {
      console.log('查看', row);
    },
    handleExport(row) {
      console.log('导出', row);
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  background-color: #ffffff;
  border-radius: 5px;
  margin: 16px;

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px;
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1);
      }
    }
  }

  .info-wrap {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    padding: 16px;

    .info-item {
      flex: 1 1 0;
      min-width: 180px;
      background-color: #fafbfc;
      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      margin-right: 16px;
      display: flex;
      align-items: center;

      .info-icon {
        width: 42px;
        height: 42px;
      }

      .info-right-wrap {
        flex: 1;
        margin-left: 24px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }

    .info-item:last-child {
      margin-right: 0;
    }
  }
}

::v-deep .bd3001-auto-filters-container {
  margin-bottom: 0px !important;
  box-shadow: none !important;
}

::v-deep .bd3001-content {
  padding-top: 0px !important;
}
</style>
