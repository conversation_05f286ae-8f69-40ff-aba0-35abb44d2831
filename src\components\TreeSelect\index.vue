<template>
  <div class="tree-select">
    <el-select
      ref="select"
      v-model="selectedValue"
      filterable
      :filter-method="filterMethod"
      placeholder="请选择"
      @blur="onBlur"
      @clear="onClear"
      clearable
      style="width: 100%"
    >
      <el-option value="" style="height: auto; padding: 0">
        <el-tree
          ref="tree"
          :data="options"
          :props="defaultProps"
          :node-key="defaultProps.id"
          default-expand-all
          :highlight-current="true"
          :expand-on-click-node="false"
          :filter-node-method="filterNodeMethod"
          @node-click="handleNodeClick"
        />
      </el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'TreeSelect',
  props: {
    options: {
      type: Array,
      default: () => [],
    },
    value: {
      type: [String, Number],
      default: '',
    },
    defaultProps: {
      type: Object,
      default: () => {
        return {
          children: 'list',
          label: 'areaName',
          id: 'areaCode',
        };
      },
    },
  },
  data() {
    return {
      selectedValue: '',
    };
  },
  computed: {},
  watch: {
    value(newValue) {
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(newValue || null);
        this.selectedValue = newValue
          ? this.$refs.tree.getNode(newValue).data[this.defaultProps.label]
          : '';
      });
    },
    selectedValue(newValue) {
      setTimeout(() => {
        // 清空筛选条件
        this.$refs.tree.filter('');
      }, 100);
    },
  },
  methods: {
    onBlur() {
      setTimeout(() => {
        // 清空筛选条件
        this.$refs.tree.filter('');
      }, 100);
    },
    onClear() {
      this.selectedValue = '';
      this.$refs.tree.setCurrentKey(null);
      this.$emit('clear');
    },
    handleNodeClick(data, node) {
      this.$refs.select.blur(); // 关闭下拉菜单
      this.selectedValue = data[this.defaultProps.label];
      this.$emit('input', data[this.defaultProps.id]);
      this.$emit('select', data);
    },
    filterMethod(query) {
      // 调用树形控件的过滤
      this.$refs.tree.filter(query);
      // 忽略选择器本身的过滤
      return true;
    },
    // 定义过滤方法
    filterNodeMethod(value, data) {
      if (!value) return true;
      return data.areaName.includes(value);
    },
  },
};
</script>

<style scoped>
.tree-select .el-select-dropdown__item {
  height: auto;
  padding: 0;
}

.el-tree {
  margin-top: -10px;
  padding: 10px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
