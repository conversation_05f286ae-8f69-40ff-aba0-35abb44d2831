<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        class="buse-wrap-station"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">发票抬头查询</div>
            <div class="top-button-wrap">
              <el-button
                type="primary"
                icon="el-icon-download"
                @click="handleOutput"
                v-hasPermi="['invoicesManagement:invoiceHeade:output']"
              >
                导出
              </el-button>
            </div>
          </div>
          <div class="card-head-after"></div>
        </template>
      </BuseCrud>
    </div>
  </div>
</template>

<script>
import { getInvBuyerConfigByPage } from '@/api/invoicesManagement/invoiceHeaderQuery';

export default {
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          minWidth: 60,
        },
        {
          field: 'invoiceTitle',
          title: '抬头类型',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              [
                { label: '企业', value: 1 },
                { label: '个人', value: 2 },
              ],
              cellValue
            );
          },
        },
        {
          field: 'buyerName',
          title: '发票抬头',
          minWidth: 120,
        },
        {
          field: 'buyerTaxpayerNo',
          title: '税号',
          minWidth: 120,
        },
        {
          field: 'buyerAddress',
          title: '注册地址',
          minWidth: 120,
        },
        {
          field: 'buyerPhone',
          title: '注册电话',
          minWidth: 120,
        },
        {
          field: 'buyerBank',
          title: '开户银行',
          minWidth: 120,
        },
        {
          field: 'buyerAcctNo',
          title: '银行账号',
          minWidth: 120,
        },
        {
          field: 'isDefault',
          title: '是否为默认抬头',
          minWidth: 150,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              [
                { label: '是', value: 1 },
                { label: '否', value: 0 },
              ],
              cellValue
            );
          },
        },
        {
          field: 'mobile',
          title: '关联用户',
          minWidth: 120,
        },
        {
          field: 'createTime',
          title: '创建时间',
          minWidth: 120,
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        invoiceTitle: '',
        buyerName: '',
        buyerAcctNo: '',
        buyerTaxpayerNo: '',
        searchCreateTime: [],
        mobile: '',
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'invoiceTitle',
            title: '抬头类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [
                { label: '企业', value: 1 },
                { label: '个人', value: 2 },
              ],
            },
          },
          {
            field: 'buyerName',
            title: '发票抬头',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'buyerAcctNo',
            title: '银行账号',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'buyerTaxpayerNo',
            title: '税号',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'searchCreateTime',
            title: '创建时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
              valueFormat: 'yyyy-MM-dd',
              options: [],
            },
          },
          {
            field: 'mobile',
            title: '创建人手机号',
            element: 'el-input',
            props: {
              placeholder: '请输入',
              maxlength: 11,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        createBeginTime: '',
        createEndTime: '',
      };
      if (this.params.searchCreateTime.length > 0) {
        params.createBeginTime = this.params.searchCreateTime[0];
        params.createEndTime = this.params.searchCreateTime[1];
      }
      this.loading = true;
      const [err, res] = await getInvBuyerConfigByPage(params);
      this.loading = false;
      if (err) {
        return;
      }
      const { data, total } = res;
      this.tableData = data;
      this.tablePage.total = total;
    },
    handleOutput() {
      this.download(
        '/vehicle-chargin-admin/inv/exportInvBuyerConfig',
        {},
        `发票抬头信息.xlsx`
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  // background: #fff;
  // padding: 20px;
  // border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
}
</style>
