<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        class="buse-wrap-station"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">企业车辆管理</div>
            <div class="top-button-wrap">
              <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
                新增车辆
              </el-button>
              <el-button
                type="primary"
                icon="el-icon-upload2"
                @click="handleInput"
              >
                批量导入
              </el-button>
            </div>
          </div>
          <div class="card-head-after"></div>
        </template>
        <template slot="status" slot-scope="{ row }">
          <el-switch
            v-model="row.status"
            active-value="ENABLE"
            inactive-value="DISABLE"
            @change="(newValue) => handleVehicleStatusChange(row, newValue)"
          />
        </template>
        <template slot="plugChargeStatus" slot-scope="{ row }">
          <el-switch
            v-model="row.plugChargeStatus"
            active-value="1"
            inactive-value="0"
            disabled
            @change="
              (newValue) => handlePlugAndChargeStatusChange(row, newValue)
            "
          />
        </template>
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button
              class="button-border"
              type="primary"
              plain
              @click="handleDetail(row)"
            >
              详情
            </el-button>

            <el-button
              class="button-border"
              type="primary"
              plain
              @click="handleedit(row)"
              v-if="row.status === 'DISABLE'"
            >
              编辑
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="760px"
      height="398px"
      append-to-body
      @close="handleCancel"
    >
      <div>模板下载：</div>
      <div class="box link-box">
        <el-link
          type="primary"
          :underline="false"
          style="font-size: 16px; vertical-align: baseline"
          @click="downloadTemplate"
        >
          导入模板.xlsx
        </el-link>
      </div>
      <div>上传文件：</div>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :auto-upload="false"
        :data="upload.data"
        :on-exceed="handleExceed"
        :http-request="customUpload"
      >
        <div class="box el-upload__text">
          <img
            class="upload-icon"
            src="@/assets/icons/responseAndRegulation/upload.png"
          />
          选择要导入上传的文件
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth';
import {
  getVehicleList,
  batchUpdateVehicleStatus,
  importVehicle,
} from '@/api/user/employeeVehicles';

export default {
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          minWidth: 60, // 最小宽度
        },
        {
          field: 'licenseNo',
          title: '车牌号',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'vin',
          title: '车架号',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'ownership',
          title: '车辆所属类型',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              [
                {
                  label: '企业',
                  value: 'ENP',
                },
                {
                  label: '企业员工',
                  value: 'ENP_E',
                },
              ],
              cellValue
            );
          },
        },
        {
          field: 'enterpriseName',
          title: '企业名称',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'ownerName',
          title: '车主名称',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'mobile',
          title: '车主手机号',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'licenseColor',
          title: '车牌类型',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(this.licenseColorOptions, cellValue);
          },
        },
        {
          field: 'make',
          title: '车辆品牌',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'model',
          title: '车辆型号',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'remark',
          title: '备注',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'status',
          title: '车辆状态',
          minWidth: 120, // 最小宽度
          slots: {
            default: 'status',
          },
        },
        {
          field: 'plugChargeStatus',
          title: '即插即充状态',
          minWidth: 120, // 最小宽度
          slots: {
            default: 'plugChargeStatus',
          },
        },
        {
          field: 'chargeLimit',
          title: '充电限额(元)',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'createUser',
          title: '创建人',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'createTime',
          title: '创建时间',
          minWidth: 120, // 最小宽度
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 250,
          align: 'center',
          fixed: 'right',
        },
      ],
      tableData: [],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        licenceNo: '',
        vin: '',
        ownership: '',
        enterpriseName: '',
        ownerName: '',
        mobile: '',
        status: '',
        plugChargeStatus: '',
      },
      // 导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API +
          '/vehicle-charging-admin/vehicle/import',
        data: {
          requireId: '',
        },
      },
      licenseColorOptions: [
        {
          label: '白色',
          value: '0',
        },
        {
          label: '黄色',
          value: '1',
        },
        {
          label: '蓝色',
          value: '2',
        },
        {
          label: '黑色',
          value: '3',
        },
        {
          label: '黄绿',
          value: '4',
        },
        {
          label: '绿色',
          value: '5',
        },
        {
          label: '其他',
          value: '99',
        },
      ],
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'licenceNo',
            title: '车牌号',
            element: 'el-input',
          },
          {
            field: 'vin',
            title: '车架号',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'ownership',
            title: '车辆归属',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [
                {
                  label: '企业',
                  value: 'ENP',
                },
                {
                  label: '企业员工',
                  value: 'ENP_E',
                },
              ],
            },
          },
          {
            field: 'enterpriseName',
            title: '企业名称',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'ownerName',
            title: '车主名称',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'mobile',
            title: '车主手机号',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'status',
            title: '车辆状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [
                {
                  label: '启用',
                  value: 'ENABLE',
                },
                {
                  label: '禁用',
                  value: 'DISABLE',
                },
              ],
            },
          },
          {
            field: 'plugChargeStatus',
            title: '即插即充状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [
                {
                  label: '否',
                  value: '0',
                },
                {
                  label: '是',
                  value: '1',
                },
              ],
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      let params = {
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        ...this.params,
      };
      console.log('params', params);
      try {
        const [err, res] = await getVehicleList(params);
        this.loading = false;
        if (err) return;

        this.tableData = res?.data || [];
        this.tablePage.total = res?.total || 0;
      } catch (error) {
        this.loading = false;
        console.error('获取车辆列表失败', error);
      }
    },
    // 禁用/启用
    async handleVehicleStatusChange(row, newValue) {
      let oldStatus = 'DISABLE';
      if (row.status == 'DISABLE') {
        oldStatus = 'ENABLE';
      } else if (row.status == 'ENABLE') {
        oldStatus = 'DISABLE';
      }
      this.$confirm('确定变更当前车辆状态吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          // console.log(row);
          // row.status = newValue;
          let params = {
            ids: [row.id],
            type: newValue,
          };
          const [err, res] = await batchUpdateVehicleStatus(params);
          if (err) return;
          this.$message({
            type: 'success',
            message: '变更成功',
          });
        })
        .catch(() => {
          row.status = oldStatus;
          this.$message({
            type: 'info',
            message: '已取消变更',
          });
        });
    },
    handlePlugAndChargeStatusChange(row, newValue) {
      let oldStatus = '0';
      if (row.plugChargeStatus == '0') {
        oldStatus = '1';
      } else if (row.plugChargeStatus == '1') {
        oldStatus = '0';
      }
      this.$confirm('确定变更即充状态吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          row.plugChargeStatus = newValue;
          this.$message({
            type: 'success',
            message: '变更成功',
          });
        })
        .catch(() => {
          row.plugChargeStatus = oldStatus;
          this.$message({
            type: 'info',
            message: '已取消变更',
          });
        });
    },
    // 详情
    handleDetail(row) {
      this.$router.push({
        path: '/v2g-charging/baseInfo/userManage/employeeVehicles/addOrEdit',
        query: { id: row.id, isDetail: true },
      });
    },
    // 编辑
    handleedit(row) {
      this.$router.push({
        path: '/v2g-charging/baseInfo/userManage/employeeVehicles/addOrEdit',
        query: { id: row.id },
      });
    },
    // 添加
    handleAdd() {
      this.$router.push({
        path: '/v2g-charging/baseInfo/userManage/employeeVehicles/addOrEdit',
      });
    },
    // 导入
    handleInput() {
      this.upload.title = '导入';
      this.upload.open = true;
    },
    // 导入关闭
    handleCancel() {
      this.upload.open = false;
      this.$refs.upload.clearFiles();
    },
    // 下载模板
    downloadTemplate() {
      this.download(
        '/vehicle-charging-admin/vehicle/template',
        {},
        `企业车辆管理导入模板.xlsx`
      );
    },
    // 用户选择的文件数量超过 limit 限制
    handleExceed(files, fileList) {
      console.log(files, fileList);
      // 清空已选文件列表
      this.$refs.upload.clearFiles();
      // 手动添加新选择的文件（首个文件）
      this.$refs.upload.handleStart(files[0]);
    },
    async customUpload({ action, file, data }) {
      this.upload.isUploading = true;
      this.upload.open = false;
      const formData = new FormData();
      formData.append('file', file);
      try {
        const [err, res] = await importVehicle(formData);
        if (err) return;
        if (res.code == '10000') {
          // console.log('导入res', res);
          this.$message.success(
            `导入成功${res.data.succeedNum}条，导入失败${res.data.failNum}条`
          );
          if (res.data.failNum > 0) {
            this.download(
              `/vehicle-charging-admin/vehicle/export/${res.data.failInfoId}`,
              {},
              `企业车辆管理导入失败数据.xlsx`,
              'getXlsx'
            );
            this.$message.success(`导入失败文件下载成功`);
          }
          this.handleCancel();
          this.loadData();
        } else {
          this.$message.error(res.subMsg || '导入失败');
        }
      } finally {
        this.upload.isUploading = false;
      }
    },
    // 提交上传文件
    submitFileForm() {
      console.log('upload data', this.upload.data);
      this.$refs.upload.submit();
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  // background: #fff;
  // padding: 20px;
  // border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
}

::v-deep .el-upload {
  text-align: left;
  font-size: 16px;
}
.box {
  margin-top: 4px;
  width: 688px;
  height: 36px;
  line-height: 36px;
  border-radius: 2px;
  text-align: center;
}
.link-box {
  margin-bottom: 38px;
  border: 1px solid #dfe1e5;
}
::v-deep .el-upload__text {
  border: 1px dashed #dfe1e5;
}
.upload-icon {
  width: 14px;
  height: 14px;
  margin-right: 6px;
}
</style>
