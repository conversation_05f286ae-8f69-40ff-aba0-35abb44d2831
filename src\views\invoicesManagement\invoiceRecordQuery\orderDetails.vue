<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <div class="table-wrap">
      <div class="card-head">
        <div class="card-head-text">企业员工账户</div>
      </div>
      <div class="info-wrap">
        <div class="info-item" v-for="item in infoList" :key="item.name">
          <img :src="item.icon" class="info-icon" />
          <div class="info-right-wrap">
            <div class="info-title">{{ item.name }}</div>
            <div class="info-number">
              {{ item.value }}
              <span class="info-unit">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="table-wrap">
      <BuseCrud
        :tableColumn="tableColumn"
        :tableData="tableData"
        :loading="loading"
        :pagerProps="{
          layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
        }"
        :tablePage="tablePage"
        :modalConfig="modalConfig"
      ></BuseCrud>
    </div>
  </div>
</template>

<script>
import icon1 from '@/assets/invoiceRecordQuery/icon-1.png';
import icon2 from '@/assets/invoiceRecordQuery/icon-2.png';
import icon3 from '@/assets/invoiceRecordQuery/icon-3.png';
import icon4 from '@/assets/invoiceRecordQuery/icon-4.png';
import icon5 from '@/assets/invoiceRecordQuery/icon-5.png';
import icon6 from '@/assets/invoiceRecordQuery/icon-6.png';
import icon7 from '@/assets/invoiceRecordQuery/icon-7.png';
import {
  queryInvOrderPage,
  queryInvOrderStatistics,
} from '@/api/invoicesManagement/invoiceRecordQuery';

export default {
  dicts: [
    'ls_pay_mode', // 支付方式
  ],
  data() {
    return {
      infoList: [
        {
          name: '订单数',
          value: '',
          unit: '个',
          icon: icon1,
        },
        {
          name: '充电总金额',
          value: '',
          unit: '元',
          icon: icon2,
        },
        {
          name: '充电电费',
          value: '',
          unit: '元',
          icon: icon3,
        },
        {
          name: '充电服务费',
          value: '',
          unit: '元',
          icon: icon4,
        },
        {
          name: '实扣总金额',
          value: '',
          unit: '元',
          icon: icon5,
        },
        {
          name: '实扣电费',
          value: '',
          unit: '元',
          icon: icon6,
        },
        {
          name: '实扣服务费',
          value: '',
          unit: '元',
          icon: icon7,
        },
      ],
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableColumn: [
        { type: 'seq', title: '序号', minWidth: 60 },
        { field: 'billId', title: '订单编号', minWidth: 150 },
        { field: 'orderTime', title: '下单时间', minWidth: 160 },
        { field: 'payTime', title: '支付时间', minWidth: 160 },
        {
          field: 'paymentMode',
          title: '支付方式',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(this.dict.type.ls_pay_mode, cellValue);
          },
        },
        { field: 'stationName', title: '充电站', minWidth: 180 },
        { field: 'pileName', title: '充电桩', minWidth: 120 },
        { field: 'chargeCapacity', title: '充电量(kWh)', minWidth: 120 },
        { field: 'chargeHour', title: '充电时长(h)', minWidth: 120 },
        {
          field: 'originElecMoney',
          title: '充电电费(元)',
          minWidth: 140,
        },
        { field: 'originServiceMoney', title: '充电服务费(元)', minWidth: 140 },
        { field: 'originMoney', title: '充电总金额(元)', minWidth: 140 },
        {
          field: 'electricityPrice',
          title: '实扣电费(元)',
          minWidth: 140,
        },
        { field: 'servicePrice', title: '实扣服务费(元)', minWidth: 140 },
        { field: 'totalPrice', title: '实扣总金额(元)', minWidth: 140 },
      ],
      invoiceId: '',
    };
  },
  computed: {
    modalConfig() {
      return {
        addBtn: false, // 隐藏新增按钮
        editBtn: false,
        delBtn: false,
        viewBtn: false,
        menu: false,
      };
    },
  },
  mounted() {
    console.log('route', this.$route.query);
    if (this.$route.query.invoiceId) {
      this.invoiceId = this.$route.query.invoiceId;
    }
  },
  methods: {
    async getOrderDetil() {
      let params = { invoiceId: this.invoiceId };
      const [err, res] = await queryInvOrderPage(params);
      if (err) {
        return;
      }
      const { data, total } = res;
      this.tableData = data;
      this.tablePage.total = total;
    },
    async getOrderStatistics() {
      let params = { invoiceId: this.invoiceId };
      const [err, res] = await queryInvOrderStatistics(params);
      if (err) {
        return;
      }
      // 订单数
      this.infoList[0].value = res.data.orderCount;
      // 充电总金额
      this.infoList[1].value = res.data.sumOriginMoney;
      // 充电电费
      this.infoList[2].value = res.data.sumOriginElecMoney;
      // 充电服务费
      this.infoList[3].value = res.data.sumOriginServiceMoney;
      // 实扣总金额
      this.infoList[4].value = res.data.sumTotalPrice;
      // 实扣电费
      this.infoList[5].value = res.data.sumElectricityPrice;
      // 实扣服务费
      this.infoList[6].value = res.data.sumServicePrice;
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  background-color: #ffffff;
  border-radius: 5px;
  margin: 16px;
  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    // margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .info-wrap {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      // background-color: #FAFBFC;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 24px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }
}

::v-deep .bd3001-auto-filters-container {
  margin-bottom: 0px !important;
}

::v-deep .bd3001-auto-filters-container {
  box-shadow: none !important;
}

::v-deep .bd3001-content {
  padding-top: 0px !important;
}
</style>
