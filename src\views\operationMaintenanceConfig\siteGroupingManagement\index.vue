<template>
  <div class="container container-region">
    <LeftTree @nodeClick="nodeClick" ref="LeftTreeRef"></LeftTree>
    <div class="container-info">
      <div class="table-wrap">
        <BuseCrud
          ref="crud"
          :loading="loading"
          :filterOptions="filterOptions"
          :tablePage="tablePage"
          :tableColumn="tableColumn"
          :tableData="tableData"
          :pagerProps="pagerProps"
          :modalConfig="modalConfig"
          class="buse-wrap-station"
          @loadData="loadData"
        >
          <template slot="area">
            <el-cascader
              v-model="params.area"
              :options="industryRegionOptions"
              style="width: 100%"
              clearable
              :props="{
                value: 'areaCode',
                label: 'areaName',
                children: 'list',
              }"
            ></el-cascader>
          </template>
          <template slot="defaultHeader">
            <div>
              <div class="card-head">
                <div class="card-head-text">站点分组管理</div>
                <div class="top-button-wrap">
                  <el-button type="primary" @click="handleAdd">新增</el-button>
                </div>
              </div>
              <div class="card-head-after"></div>
            </div>
          </template>
          <template slot="operate" slot-scope="{ row }">
            <div class="menu-box">
              <el-button type="danger" plain @click="handleDel(row)">
                移除
              </el-button>
            </div>
          </template>
        </BuseCrud>
      </div>
    </div>
  </div>
</template>

<script>
import LeftTree from './components/leftTree';
import { areaTreeStation } from '@/api/vehicleGrid/common';
import {
  getStationGroupList,
  removeStationGroup,
} from '@/api/operationMaintenanceConfig/siteGroupingManagement.js';

export default {
  dicts: [
    'ls_charging_operation_mode', // 运营模式
    'ls_charging_asset_property', // 资产属性
  ],
  components: {
    LeftTree,
  },
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: [
        { field: 'groupName', title: '分组名称', minWidth: 150 }, // 👈 新增字段
        { field: 'stationNo', title: '充电站编号', minWidth: 150 },
        { field: 'stationName', title: '充电站名称', minWidth: 180 },
        {
          field: 'assetProperty',
          title: '资产属性',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_asset_property,
              cellValue
            );
          },
        },
        {
          field: 'operationMode',
          title: '运营模式',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_operation_mode,
              cellValue
            );
          },
        },
        { field: 'area', title: '行政区域', minWidth: 150 }, // 👈 新增字段
        { field: 'stationAddress', title: '场站位置', minWidth: 200 },
        {
          title: '操作',
          width: 150,
          align: 'center',
          slots: { default: 'operate' },
        },
      ],
      tableData: [{}],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        stationNo: '', // 充电站编号
        stationName: '', // 充电站名称
        assetProperty: '', // 资产属性
        operationMode: '', // 运营模式
        area: '', // 行业区域
      },
      industryRegionOptions: [], // 行业区域数据
      selectGroup: '', // 选中的分组
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'stationNo',
            title: '充电站编号',
            element: 'el-input',
          },
          {
            field: 'stationName',
            title: '充电站名称',
            element: 'el-input',
          },
          {
            field: 'assetProperty',
            title: '资产属性',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_asset_property,
            },
          },
          {
            field: 'operationMode',
            title: '运营模式',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_operation_mode,
            },
          },
          {
            field: 'area',
            title: '行政区域',
            // element: 'el-select',
            // props: {
            //   options: this.industryRegionOptions,
            // },
            element: 'slot',
            slotName: 'area',
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  async mounted() {
    await this.getAreaOPtions();
    await this.loadData();
  },
  methods: {
    async getAreaOPtions() {
      const [err, res] = await areaTreeStation({});
      if (err) return;
      this.industryRegionOptions = [res.data];
      console.log('getAreaOPtions', this.industryRegionOptions);
      this.industryRegionOptions.forEach((item) => {
        if (item.list && item.list.length > 0) {
          item.list.forEach((it) => {
            if (it.list && it.list.length > 0) {
              it.list.forEach((i) => {
                delete i.list;
              });
            }
          });
        }
      });
    },
    async loadData() {
      console.log(this.params);
      let params = {
        ...this.params,
        province: '',
        city: '',
        county: '',
        groupName: this.selectGroup,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      if (this.params.area && this.params.area.length > 0) {
        params.province = this.params.area[0];
      }
      if (this.params.area && this.params.area.length > 1) {
        params.city = this.params.area[1];
      }
      if (this.params.area && this.params.area.length > 2) {
        params.county = this.params.area[2];
      }
      this.loading = true;
      const [err, res] = await getStationGroupList(params);
      this.loading = false;
      if (err) return;
      const { data, total } = res;
      this.tableData = data;
      this.tablePage.total = total;
    },
    nodeClick(node) {
      if (node) {
        console.log('node', node);
        this.selectGroup = node.groupName;
        this.loadData();
      }
    },
    // 新增
    handleAdd() {
      this.$router.push({
        path: '/v2g-charging/operationMaintenanceConfig/siteGroupingManagement/apply',
        // query: {
        // },
      });
    },
    // 删除
    handleDel(row) {
      this.$confirm(`确定删除这条数据吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const [err, res] = await removeStationGroup({
            detailId: row.detailId,
          });
          if (err) return;
          this.$message.success('删除成功');
          // await this.$refs.LeftTreeRef.loadData();
          await this.loadData();
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
    justify-content: flex-end;
  }
}

.ellipsis-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.button-border {
  border: 0.01rem solid #217aff;
  color: #217aff;
  background-color: #fff;
}
.delete-btn {
  border: 0.01rem solid #fc1e31;
  color: #fc1e31;
  background-color: #fff;
}

::v-deep .vxe-table--render-default .vxe-body--column.col--center {
  text-align: left !important;
}
</style>
