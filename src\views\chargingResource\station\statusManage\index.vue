<template>
  <div class="container container-region">
    <RegionSelect @nodeClick="nodeClick"></RegionSelect>
    <div class="container-info">
      <div class="top-info-wrap">
        <div class="top-info-icon"></div>
        <div class="top-info-first" v-if="topInfoFirst">{{ topInfoFirst }}</div>
        <div class="top-info-bold">{{ topInfoBold }}</div>
      </div>
      <div class="table-wrap">
        <BuseCrud
          ref="crud"
          :loading="loading"
          :filterOptions="filterOptions"
          :tablePage="tablePage"
          :tableColumn="tableColumn"
          :tableData="tableData"
          :pagerProps="pagerProps"
          :modalConfig="modalConfig"
          class="buse-wrap-station"
          @loadData="loadData"
        >
          <template slot="defaultHeader">
            <div>
              <div class="card-head">
                <div class="card-head-text">充电站列表</div>

                <div class="top-button-wrap">
                  <el-button plain @click="() => handleStatus('04')">
                    充电站永久退运
                  </el-button>

                  <el-button plain @click="() => handleStatus('03')">
                    充电站临时退运
                  </el-button>

                  <el-button
                    type="primary"
                    plain
                    @click="() => handleStatus('02')"
                  >
                    充电站停运
                  </el-button>

                  <el-button type="primary" @click="() => handleStatus('01')">
                    充电站投运
                  </el-button>
                </div>
              </div>
              <div class="card-head-after"></div>
            </div>
          </template>

          <template slot="operate" slot-scope="{ row }">
            <!-- <div class="menu-box">
                        <el-button
                            type="primary"
                            plain
                            @click="handleAudit(row, 'index')"
                            v-if="row.auditType == '00'"
                        >
                            审核
                        </el-button>

                        <el-button
                            type="primary"
                            plain
                            @click="handleAudit(row, 'detail')"
                        >
                            详情
                        </el-button>



                    
                    </div> -->

            <el-dropdown trigger="click">
              <el-button class="button-border" type="primary" plain>
                操作
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-if="row.auditType == '00'">
                  <div @click="handleAudit(row, 'index')">审核</div>
                </el-dropdown-item>

                <el-dropdown-item>
                  <div @click="handleAudit(row, 'detail')">详情</div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </BuseCrud>
      </div>
    </div>
  </div>
</template>

<script>
import RegionSelect from '@/components/Business/RegionSelect';

import {
  getStationStatusList,
  getAssetUnit,
  getOperationUnit,
  getMaintenanceUnit,
} from '@/api/station/index';

import StatusDot from '@/components/Business/StatusDot';

export default {
  components: {
    RegionSelect,
    StatusDot,
  },
  dicts: [
    'ls_charging_station_type', // 站点类型
    'ls_charging_construction', // 建设场所
    'ls_charging_operation_mode', // 运营模式
    'ls_charging_asset_property', // 资产属性
    'ls_charging_station_access_type', // 充电站接入类型
    'ls_charging_operation_status', // 运营状态
    'ls_charging_station_source', // 数据来源
    'ls_charging_status', // 是否状态
    'ls_charging_adjustable_type', // 可控类型
    'ls_charging_parking_charge_type', // 停车场收费类型
    'ls_charging_area_type', // 所属区域
    'ls_charging_contracted_unit', // 签约单位 投资主体 供电机构
    'ls_charging_audit_type', // 审核状态
    'ls_charging_apply_status', // 申请类型
  ],
  data() {
    return {
      loading: false,
      stationList: [],
      topInfo: {},
      topInfoFirst: '',
      topInfoBold: '',

      assetUnitList: [], // 资产单位列表
      operationUnitList: [], // 运营单位列表
      maintenanceUnitList: [], // 运维单位列表

      tablePage: { total: 0, currentPage: 1, pageSize: 10 },

      tableColumn: [
        // {
        //     type: 'checkbox',
        //     width: 50,
        //     fixed: 'left',
        // },
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60, // 最小宽度
        },
        {
          field: 'applyNo',
          title: '申请编号',
          minWidth: 220, // 最小宽度
        },
        {
          field: 'stationNo',
          title: '充电站编号',
          minWidth: 220, // 最小宽度
        },
        {
          field: 'stationName',
          title: '充电站名称',
          minWidth: 200, // 最小宽度
        },
        {
          field: 'stationType',
          title: '站点类型',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_station_type,
              cellValue
            );
          },
        },
        {
          field: 'operationMode',
          title: '运营模式',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_operation_mode,
              cellValue
            );
          },
        },
        {
          field: 'assetProperty',
          title: '资产属性',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_asset_property,
              cellValue
            );
          },
        },
        {
          field: 'stationAccessType',
          title: '接入方式',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_station_access_type,
              cellValue
            );
          },
        },
        // {
        //     field: 'pileNum',
        //     title: '充电桩数量',
        //     minWidth: 120, // 最小宽度
        // },
        {
          field: 'isAdjustable',
          title: '是否参与调控',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_status,
              cellValue
            );
          },
        },
        {
          field: 'adjustableType',
          title: '可控类型',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_adjustable_type,
              cellValue
            );
          },
        },
        {
          field: 'unifiedConstruction',
          title: '是否统建统服',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_status,
              cellValue
            );
          },
        },
        {
          field: 'construction',
          title: '建设场所',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_construction,
              cellValue
            );
          },
        },
        {
          field: 'areaType',
          title: '所属区域',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_area_type,
              cellValue
            );
          },
        },
        {
          field: 'stationAddress',
          title: '充电站地址',
          minWidth: 200, // 最小宽度
          slots: {
            default: ({ row }) => [
              <el-tooltip
                content={row.stationAddress}
                placement="top"
                disabled={!row.stationAddress || row.stationAddress.length < 10}
              >
                <span class="ellipsis-text">{row.stationAddress}</span>
              </el-tooltip>,
            ],
          },
        },
        {
          title: '资产单位',
          field: 'assetUnitName',
          minWidth: 200, // 最小宽度
          slots: {
            default: ({ row }) => [
              <el-tooltip
                content={row.assetUnitName}
                placement="top"
                disabled={!row.assetUnitName || row.assetUnitName.length < 10}
              >
                <span class="ellipsis-text">{row.assetUnitName}</span>
              </el-tooltip>,
            ],
          },
        },
        {
          title: '运营单位',
          field: 'operatingUnitName',
          minWidth: 200, // 最小宽度
          slots: {
            default: ({ row }) => [
              <el-tooltip
                content={row.operatingUnitName}
                placement="top"
                disabled={
                  !row.operatingUnitName || row.operatingUnitName.length < 10
                }
              >
                <span class="ellipsis-text">{row.operatingUnitName}</span>
              </el-tooltip>,
            ],
          },
        },
        {
          title: '运维单位',
          field: 'maintenanceUnitName',
          minWidth: 200, // 最小宽度
          slots: {
            default: ({ row }) => [
              <el-tooltip
                content={row.maintenanceUnitName}
                placement="top"
                disabled={
                  !row.maintenanceUnitName ||
                  row.maintenanceUnitName.length < 10
                }
              >
                <span class="ellipsis-text">{row.maintenanceUnitName}</span>
              </el-tooltip>,
            ],
          },
        },
        {
          title: '站点来源',
          field: 'stationSource',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_station_source,
              cellValue
            );
          },
        },
        {
          title: '投运日期',
          field: 'openDate',
          minWidth: 120, // 最小宽度
        },
        {
          title: '申请时间',
          field: 'applyTime',
          minWidth: 200, // 最小宽度
        },
        {
          title: '申请人',
          field: 'applicant',
          minWidth: 120, // 最小宽度
        },

        {
          title: '申请类型',
          field: 'applyType',
          minWidth: 120, // 最小宽度
          align: 'center',

          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_apply_status,
              cellValue
            );
          },
        },
        {
          title: '审核状态',
          field: 'auditType',
          minWidth: 100, // 最小宽度
          align: 'center',
          fixed: 'right',
          // formatter: ({ cellValue }) => {
          //     return this.selectDictLabel(
          //         this.dict.type.ls_charging_audit_type,
          //         cellValue
          //     );
          // }

          slots: {
            // 自定义render函数
            default: ({ row }) => {
              return (
                <StatusDot
                  value={row.auditType}
                  dictValue={this.dict.type.ls_charging_audit_type}
                  colors={['warning', 'success', 'danger']}
                ></StatusDot>
              );
            },
          },
        },

        {
          title: '操作',
          slots: { default: 'operate' },
          width: 100,
          align: 'center',
          fixed: 'right',
        },
      ],
      tableData: [],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        stationId: '',
        stationName: '',
        stationType: '',
        operationMode: '',
        assetAttribute: '',
        assetType: '',
        assetUnit: '',
        operationUnit: '',
        operationMaintainUnit: '',
        dataSources: '02',
        operationDate: [],
        isRegulation: '',
        controlType: '',
        isTjtf: '',
        constructionSite: '',
        auditStatus: '',
        applyType: '',
        applyTime: [],
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'stationId',
            title: '充电站编号',
            element: 'el-input',
          },
          {
            field: 'stationName',
            title: '充电站名称',
            element: 'el-input',
          },
          {
            field: 'stationType',
            title: '站点类型',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_station_type,
            },
          },
          {
            field: 'operationMode',
            title: '运营模式',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_operation_mode,
            },
          },
          {
            field: 'assetAttribute',
            title: '资产属性',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_asset_property,
            },
          },
          {
            field: 'assetType',
            title: '接入方式',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_station_access_type,
            },
          },
          {
            field: 'assetUnit',
            title: '资产单位',
            element: 'el-select',
            props: {
              options: this.assetUnitList,
            },
          },
          {
            field: 'operationUnit',
            title: '运营单位',
            element: 'el-select',
            props: {
              options: this.operationUnitList,
            },
          },
          {
            field: 'operationMaintainUnit',
            title: '运维单位',
            element: 'el-select',
            props: {
              options: this.maintenanceUnitList,
            },
          },
          // {
          //   field: 'operationStatus',
          //   title: '运营状态',
          //   element: 'el-select',
          //   props: {
          //     options: this.dict.type.ls_charging_operation_status,
          //   },
          // },
          {
            field: 'dataSources',
            title: '数据来源',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_station_source,
            },
          },
          {
            field: 'operationDate',
            title: '投运日期',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              options: [],
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'isRegulation',
            title: '是否参与调控',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_status,
            },
          },
          {
            field: 'controlType',
            title: '可控类型',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_adjustable_type,
            },
          },
          {
            field: 'isTjtf',
            title: '是否统建统服',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_status,
            },
          },
          {
            field: 'constructionSite',
            title: '建设场所',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_construction,
            },
          },
          {
            field: 'applyType',
            title: '申请类型',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_apply_status,
            },
          },
          {
            field: 'auditStatus',
            title: '审核状态',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_audit_type,
            },
          },
          {
            field: 'applyTime',
            title: '申请时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              options: [],
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
              defaultTime: ['00:00:00', '23:59:59'],
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.getAssetUnit();
    this.getOperationUnit();
    this.getMaintenanceUnit();
  },
  methods: {
    // 获取资产单位
    async getAssetUnit() {
      const [err, res] = await getAssetUnit({});
      if (err) return;
      this.assetUnitList = res.data;
    },

    // 获取运营单位
    async getOperationUnit() {
      const [err, res] = await getOperationUnit({});
      if (err) return;
      this.operationUnitList = res.data;
    },

    // 获取运维单位
    async getMaintenanceUnit() {
      const [err, res] = await getMaintenanceUnit({});
      if (err) return;
      this.maintenanceUnitList = res.data;
    },

    nodeClick(node) {
      if (node) {
        const { list, areaCode, areaName, ...reset } = node;
        this.params.operatorCode = '';
        this.params.stationId = '';
        this.params.pileId = '';
        this.regionInfo = reset;
        console.log('node', node);
        this.loadData();
        this.topInfo = {
          ...reset,
          areaName,
        };

        this.handleTopInfo();
      }
    },
    handleTopInfo() {
      const { provinceCodeName, cityCodeName, districtCodeName, areaName } =
        this.topInfo;
      if (provinceCodeName === areaName) {
        // 选中了省
        this.topInfoFirst = '';
        this.topInfoBold = areaName;
      } else if (cityCodeName === areaName) {
        // 选中了市
        this.topInfoFirst = `${provinceCodeName} / `;
        this.topInfoBold = areaName;
      } else if (districtCodeName === areaName) {
        // 选中了区
        this.topInfoFirst = `${provinceCodeName} / ${cityCodeName} / `;
        this.topInfoBold = areaName;
      }
    },
    async loadData() {
      const {
        stationId,
        stationName,
        stationType,
        operationMode,
        assetAttribute,
        assetType,
        assetUnit,
        operationUnit,
        operationMaintainUnit,
        dataSources,
        operationDate,
        isRegulation,
        controlType,
        isTjtf,
        constructionSite,
        auditStatus,
        applyType,
        applyTime,
      } = this.filterOptions.params;

      const { provinceCode, cityCode, districtCode } = this.regionInfo;

      let openDateStart = '';
      let openDateEnd = '';
      if (operationDate && operationDate.length > 0) {
        openDateStart = operationDate[0];
        openDateEnd = operationDate[1];
      }

      let applyStartTime = '';
      let applyEndTime = '';
      if (applyTime && applyTime.length > 0) {
        applyStartTime = applyTime[0];
        applyEndTime = applyTime[1];
      }

      this.loading = true;
      const [err, res] = await getStationStatusList({
        stationNo: stationId,
        stationName,
        stationType,
        operationMode,
        assetProperty: assetAttribute,
        stationAccessType: assetType,
        assetUnit,
        operatingUnit: operationUnit,
        maintenanceUnit: operationMaintainUnit,
        stationSource: dataSources,
        openDateStart,
        openDateEnd,
        isAdjustable: isRegulation,
        adjustableType: controlType,
        unifiedConstruction: isTjtf,
        construction: constructionSite,
        province: provinceCode,
        city: cityCode,
        county: districtCode,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        auditType: auditStatus,
        applyType,
        applyStartTime,
        applyEndTime,
      });
      this.loading = false;
      if (err) return;

      const { data, total } = res;

      this.tableData = data;
      this.tablePage.total = total;
    },

    handleCheckboxChange({ records }) {
      console.log('records', records);
      this.stationList = records.map((item) => item.stationId);
    },

    handleStatus(status) {
      this.$router.push({
        path: '/v2g-charging/baseInfo/equipmentAndAssets/station/apply',
        query: {
          status,
        },
      });
    },

    // 审核
    handleAudit(row, type) {
      this.$router.push({
        path: '/v2g-charging/baseInfo/equipmentAndAssets/station/audit',
        query: {
          type,
          applyNo: row.applyNo,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
  }
}

.top-info-wrap {
  display: flex;
  height: 20px;
  align-items: center;
  margin-bottom: 16px;

  .top-info-icon {
    margin-left: 10px;
    margin-right: 4px;

    width: 20px;
    height: 20px;
    background-image: url('~@/assets/station/location-top.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .top-info-first {
    font-weight: 400;
    font-size: 16px;
    color: #505363;
  }
  .top-info-bold {
    margin-left: 4px;
    font-weight: 400;
    font-size: 16px;
    color: #12151a;
  }
}

.ellipsis-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
</style>
