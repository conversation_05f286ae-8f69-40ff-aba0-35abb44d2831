<template>
  <div class="container">
    <BuseCrud
      ref="crud"
      :loading="table.loading"
      :tablePage="table.page"
      :tableColumn="tableColumn"
      :tableData="table.data"
      :pagerProps="pagerProps"
      :modalConfig="modalConfig"
      @loadData="getTablePage"
    >
      <template slot="defaultHeader">
        <div>
          <div class="card-head">
            <div class="card-head-text">充电站列表</div>
          </div>
          <div class="card-head-after"></div>

          <div class="top-button-wrap">
            <el-button type="primary" @click="() => handleChooseStation()">
              充电站选择
            </el-button>

            <div class="choose-info-wrap">
              已选择
              <div class="choose-number">{{ stationNumber }}</div>
              个充电站，
              <div class="choose-number">{{ pileNumber }}</div>

              个充电桩
            </div>
          </div>
        </div>
      </template>

      <template slot="operate" slot-scope="{ row }">
        <div class="menu-box">
          <el-button type="primary" plain @click="hanleDetail(row)">
            详情
          </el-button>

          <el-button type="danger" plain @click="() => handleDelete(row)">
            删除
          </el-button>
        </div>
      </template>
    </BuseCrud>

    <div class="info-card">
      <div class="card-head" style="margin-top: 0">
        <div class="before-icon"></div>
        <div class="card-head-text">场站SOC配置</div>
      </div>
      <div class="card-head-after"></div>
      <div class="form-wrap">
        <el-form
          :model="form"
          :rules="rules"
          ref="applyForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="场站SOC"
                prop="applyType"
                :label-width="formLabelWidth"
              >
                <!-- <el-select
                  v-model="form.applyType"
                  placeholder="请选择申请类型"
                  style="width: 100%"
                  :disabled="true"
                >
                  <el-option
                    v-for="item in applyTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select> -->
                <el-input placeholder="请输入内容" v-model="input2">
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item
                label="临时退运时长"
                prop="time"
                :label-width="formLabelWidth"
                v-if="form.applyType == '03'"
              >
                <div style="width: 100%; display: flex">
                  <el-input-number
                    :min="1"
                    :precision="0"
                    :step="1"
                    controls-position="right"
                    v-model="form.time"
                    placeholder="请输入"
                  ></el-input-number>
                  <el-select
                    v-model="unit"
                    placeholder="请选择"
                    style="width: 20%; margin-left: 8px"
                  >
                    <el-option
                      v-for="item in this.dict.type
                        .ls_charging_return_time_unit"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="申请说明"
                prop="remark"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="form.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入申请说明"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="bottom-wrap">
      <el-button @click="() => handleCancel()">取消</el-button>
      <el-button type="primary" @click="() => handleConfirm()">提交</el-button>
    </div>

    <ChooseStationModal
      ref="chooseStationModal"
      @confirm="handleChooseStationConfirm"
    />
  </div>
</template>

<script>
import BgTitle from '@/components/Business/BgTitle';
import ChooseStationModal from './components/chooseStationModal';

import {
  stationOperation,
  stationStop,
  stationPermanentReturns,
  stationTemporaryStop,
} from '@/api/station/index';
export default {
  name: 'ResponseRequirements',
  components: {
    BgTitle,
    ChooseStationModal,
  },
  dicts: [
    'ls_charging_station_type', // 站点类型
    'ls_charging_construction', // 建设场所
    'ls_charging_operation_mode', // 运营模式
    'ls_charging_asset_property', // 资产属性
    'ls_charging_station_access_type', // 充电站接入类型
    'ls_charging_operation_status', // 运营状态
    'ls_charging_station_source', // 数据来源
    'ls_charging_status', // 是否状态
    'ls_charging_adjustable_type', // 可控类型
    'ls_charging_parking_charge_type', // 停车场收费类型
    'ls_charging_area_type', // 所属区域
    'ls_charging_contracted_unit', // 签约单位 投资主体 供电机构
    'ls_charging_return_time_unit', // 临时退运时长-单位
  ],
  data() {
    return {
      menuShow: true,

      table: {
        loading: false,
        page: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        },
        dataTotal: [],
        data: [],
      },
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60, // 最小宽度
        },
        {
          field: 'stationNo',
          title: '充电站编号',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'stationName',
          title: '充电站名称',
          minWidth: 150, // 最小宽度
        },
        {
          field: 'stationType',
          title: '站点类型',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_station_type,
              cellValue
            );
          },
        },
        {
          field: 'operationMode',
          title: '运营模式',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_operation_mode,
              cellValue
            );
          },
        },
        {
          field: 'assetProperty',
          title: '资产属性',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_asset_property,
              cellValue
            );
          },
        },
        {
          field: 'stationAccessType',
          title: '接入方式',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_station_access_type,
              cellValue
            );
          },
        },
        {
          field: 'pileNum',
          title: '充电桩数量',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'isAdjustable',
          title: '是否参与调控',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_status,
              cellValue
            );
          },
        },
        {
          field: 'adjustableType',
          title: '可控类型',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_adjustable_type,
              cellValue
            );
          },
        },
        {
          field: 'unifiedConstruction',
          title: '是否统建统服',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_status,
              cellValue
            );
          },
        },
        {
          field: 'construction',
          title: '建设场所',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_construction,
              cellValue
            );
          },
        },
        {
          field: 'areaType',
          title: '所属区域',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_area_type,
              cellValue
            );
          },
        },
        {
          title: '资产单位',
          field: 'assetUnitName',
          minWidth: 120, // 最小宽度
        },
        {
          title: '运营单位',
          field: 'operatingUnitName',
          minWidth: 120, // 最小宽度
        },
        {
          title: '运维单位',
          field: 'maintenanceUnitName',
          minWidth: 120, // 最小宽度
        },
        {
          title: '签约单位',
          field: 'contractedUnit',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_contracted_unit,
              cellValue
            );
          },
        },
        {
          title: '数据来源',
          field: 'stationSource',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_station_source,
              cellValue
            );
          },
        },

        {
          title: '操作',
          slots: { default: 'operate' },
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],

      stationNumber: '0',
      pileNumber: '0',

      form: {
        applyType: '',
        time: '',
        remark: '',
      },
      rules: {
        time: [{ required: true, message: '请输入时常', trigger: 'blur' }],
        remark: [
          { required: true, message: '请输入申请说明', trigger: 'blur' },
        ],
      },
      formLabelWidth: '120px',
      applyTypeList: [
        { label: '投运', value: '01' },
        { label: '停运', value: '02' },
        { label: '临时退运', value: '03' },
        { label: '退运', value: '04' },
      ],

      unit: '00',
    };
  },
  computed: {
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    const status = this.$route.query.status;
    // if (status === '04') {
    //   // 退运
    //   this.form.applyType = '04';
    // } else if (status === '03') {
    //   // 临时退运
    //   this.form.applyType = '03';
    // } else if (status === '02') {
    //   // 停运
    //   this.form.applyType = '02';
    // } else if (status === '01') {
    //   // 投运
    //   this.form.applyType = '01';
    // }

    this.table.dataTotal = [];

    this.table.page.currentPage = 1;
    this.table.page.total = this.table.dataTotal.length;
    this.getTablePage();
  },
  methods: {
    getTablePage() {
      this.table.data = this.table.dataTotal.slice(
        (this.table.page.currentPage - 1) * this.table.page.pageSize,
        this.table.page.currentPage * this.table.page.pageSize
      );
    },

    // 选择充电站
    handleChooseStation() {
      if (this.form.applyType === '01') {
        // 投运
        this.$refs.chooseStationModal.operationStatusList = ['01', '03', '10'];
        // } else if (this.form.applyType === '02') {
        //   // 停运
        //   this.$refs.chooseStationModal.operationStatusList = ['02'];
        // } else if (this.form.applyType === '03') {
        //   // 临时退运
        //   this.$refs.chooseStationModal.operationStatusList = ['02', '03'];
        // } else if (this.form.applyType === '04') {
        //   // 退运
        //   this.$refs.chooseStationModal.operationStatusList = ['02', '03', '10'];
      }

      this.$refs.chooseStationModal.loadData();

      this.$refs.chooseStationModal.dialogVisible = true;
    },

    // 选择充电站确认
    handleChooseStationConfirm(stationList) {
      const list = this.table.dataTotal.concat(stationList);

      const uniqueArray = Array.from(
        new Map(list.map((item) => [item.stationId, item])).values()
      );

      this.table.dataTotal = uniqueArray;

      this.stationNumber = this.table.dataTotal.length;
      let number = 0;
      this.table.dataTotal.forEach((item) => {
        number += Number(item.pileNum);
      });

      this.pileNumber = 0;
      this.pileNumber = number;

      this.table.page.currentPage = 1;
      this.table.page.total = this.table.dataTotal.length;
      this.getTablePage();
    },

    // 充电站详情
    hanleDetail(row) {
      const { stationId } = row;
      this.$router.push({
        path: '/v2g-charging/baseInfo/equipmentAndAssets/station/detail',
        query: {
          stationId,
        },
      });
    },

    // 删除充电站
    handleDelete(row) {
      this.$confirm('确定删除该充电站吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          const index = this.table.dataTotal.findIndex(
            (item) => item.stationId === row.stationId
          );
          this.table.dataTotal.splice(index, 1);

          this.table.page.currentPage = 1;
          this.table.page.total = this.table.dataTotal.length;
          this.stationNumber = this.table.dataTotal.length;
          let number = 0;
          this.table.dataTotal.forEach((item) => {
            number += Number(item.pileNum);
          });

          this.pileNumber = 0;
          this.pileNumber = number;

          this.getTablePage();
        })
        .catch(() => {});
    },

    // 确认
    async handleConfirm() {
      this.$refs.applyForm.validate(async (valid) => {
        if (valid) {
          const { dataTotal } = this.table;

          const { applyType, time, remark } = this.form;

          const list = [];
          dataTotal.forEach((item) => {
            list.push(item.stationId);
          });

          if (applyType === '01') {
            // 投运
            const [err, res] = await stationOperation({
              stationIdList: list,
              applyRemark: remark,
            });
            if (err) return;

            this.$message.success('申请成功');

            setTimeout(() => {
              this.$router.back();
            }, 2000);
          } else if (applyType === '02') {
            // 停运
            const [err, res] = await stationStop({
              stationIdList: list,
              applyRemark: remark,
            });
            if (err) return;

            this.$message.success('申请成功');

            setTimeout(() => {
              this.$router.back();
            }, 2000);
          } else if (applyType === '04') {
            // 退运
            const [err, res] = await stationPermanentReturns({
              stationIdList: list,
              applyRemark: remark,
            });
            if (err) return;

            this.$message.success('申请成功');

            setTimeout(() => {
              this.$router.back();
            }, 2000);
          } else if (applyType === '03') {
            // 临时退运
            const [err, res] = await stationTemporaryStop({
              stationIdList: list,
              applyRemark: remark,
              returnTimeNum: time,
              unit: this.unit,
            });
            if (err) return;

            this.$message.success('申请成功');

            setTimeout(() => {
              this.$router.back();
            }, 2000);
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.title-box {
  padding: 10px 0 20px;
  display: flex;
  // justify-content: space-between;
  .add-btn {
    margin-left: 20px;
  }
}
.menu-box {
  display: flex;
}
::v-deep .bd3001-table-select-box {
  display: none;
}
::v-deep .bd3001-header {
  display: block !important;
}
::v-deep .bd3001-button {
  display: block !important;
}

.card-head {
  // position: relative;
  height: 56px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  margin-top: -20px;
  .card-head-text {
    flex: 1;
    width: 520px;
    height: 26px;
    background-image: url('~@/assets/images/bg-title.png');
    background-size: 520px 26px;
    background-repeat: no-repeat;
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
    padding-left: 36px;
    color: #21252e;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      left: -3px; /* 调整这个值来改变边框的宽度 */
      width: 0;
      border-top: 3px solid transparent;
      border-bottom: 3px solid transparent;
      border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
    }
  }
}

.card-head-after {
  width: 100%;
  height: 1px;
  background-color: #dcdee2;
  margin-bottom: 16px;
}

.top-button-wrap {
  display: flex;
  align-items: center;
  height: 34px;
  align-items: center;
  margin-bottom: 14px;
  .choose-info-wrap {
    border-radius: 2px;
    height: 34px;
    padding: 0 10px;
    display: flex;
    align-items: center;
    background-color: #ebf3ff;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    margin-left: 16px;
    color: #217aff;
    .choose-number {
      font-size: 16px;
      font-weight: 500;
      margin: 0 4px;
    }
  }
}

.info-card {
  margin: 16px 0 16px 0;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  .form-wrap {
    padding: 0 16px 16px 16px;
    .custom-header {
      background: -webkit-gradient(
          linear,
          left top,
          left bottom,
          from(rgba(0, 149, 255, 0.5)),
          to(rgba(87, 152, 255, 0))
        ),
        #f5faff;
      background: linear-gradient(
          180deg,
          rgba(0, 149, 255, 0.5) 0%,
          rgba(87, 152, 255, 0) 100%
        ),
        #f5faff;
      background-repeat: no-repeat;
    }
  }
}

.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .bottom-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
  }
}
</style>
