import request from '@/utils/request'

const baseUrl = '/vehicle-charging-admin'

// 用户列表
export function getStationList(data) {
  return request({
    url: baseUrl + '/station/page',
    method: 'post',
    data: data
  })
}

// 充电站数据统计
export function getStationStatistic(data) {
  return request({
    url: baseUrl + '/station/stationCount',
    method: 'post',
    data: data
  })
}   


// 批量删除
export function batchDeleteStation(data) {
  return request({
    url: baseUrl + '/station/batchRemove',
    method: 'post',
    data: data
  })
}

// 删除充电站
export function deleteStation(data) {
    return request({
      url: baseUrl + '/station/remove',
      method: 'post',
      data: data
    })
}

// 获取资产单位
export function getAssetUnit(data) {
    return request({
        url: baseUrl + '/common/getAssetUnit',
        method: 'post',
        data: data
    })
}

// 获取运营单位
export function getOperationUnit(data) {
    return request({
        url: baseUrl + '/common/getOperationUnit',
        method: 'post',
        data: data
    })
}

// 获取运维单位
export function getMaintenanceUnit(data) {
    return request({
        url: baseUrl + '/common/getMaintenanceUnit',
        method: 'post',
        data: data
    })
}

// 获取服务标签
export function getServiceTag(data) {
    return request({
        url: baseUrl + '/Tag/list',
        method: 'post',
        data: data
    })
}

// 新增充电站
export function createStation(data) {
  return request({
    url: baseUrl + '/station/create',
    method: 'post',
    data: data
  })
}

// 获取充电站详情
export function getStationDetail(data) {
  return request({
    url: baseUrl + '/station/detail',
    method: 'post',
    data: data
  })
}

// 编辑充电站

export function updateStation(data) {
  return request({
    url: baseUrl + '/station/update',
    method: 'post',
    data: data
  })
}

// 批量取消删除
export function batchCancelDelete(data) {
  return request({
    url: baseUrl + '/station/batchCancelRemove',
    method: 'post',
    data: data
  })
}

// 调控配置查看
export function getControlDetail(data) {
  return request({
    url: baseUrl + '/station/stationControlDetail',
    method: 'post',
    data: data
  })
}

// 调控配置修改
export function updateControl(data) {
  return request({
    url: baseUrl + '/station/stationControl',
    method: 'post',
    data: data
  })
}

// 充电桩状态变更管理列表
export function getStationStatusList(data) {
    return request({
        url: baseUrl + '/station/stationStatusPage',
        method: 'post',
        data: data
    })
}

// 充电站投运
export function stationOperation(data) {
  return request({
    url: baseUrl + '/station/stationOperation',
    method: 'post',
    data: data
  })
}

// 充电站停运
export function stationStop(data) {
   return request({
    url: baseUrl + '/station/stationStop',
    method: 'post',
    data: data
   })
}

// 充电站退运
export function stationPermanentReturns(data) {
  return request({
    url: baseUrl + '/station/stationPermanentReturns',
    method: 'post',
    data: data
  })
}

// 充电站临时退运
export function stationTemporaryStop(data) {
  return request({
    url: baseUrl + '/station/stationTemporaryStop',
    method: 'post',
    data: data
  })
}

// 审核详情
export function getAuditDetail(data) {
  return request({
    url: baseUrl + '/station/stationApprovalDetail',
    method: 'post',
    data: data
  })
}

// 审批
export function stationAudit(data) {
  return request({
    url: baseUrl + '/approval/station/audit',
    method: 'post',
    data: data
  })  
}