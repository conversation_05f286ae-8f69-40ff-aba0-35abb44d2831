import request from '@/utils/request'

const baseUrl = '/vehicle-grid-admin'

// 调控需求列表
export function getRequirementList(data) {
  return request({
    url: baseUrl + '/adjustment/plan/requirementList',
    method: 'post',
    data: data
  })
}

// 运营商申报计划详情
export function getPlanDetails(data) {
  return request({
    url: baseUrl + '/adjustment/plan/operatePlanDetail',
    method: 'post',
    data: data
  })
}

// 点击编辑计划后查询出的站点户号申报计划
export function getConsPlanList(data) {
  return request({
    url: baseUrl + '/adjustment/plan/consPlanList',
    method: 'post',
    data: data
  })
}

// 点击站点户号申报计划详情查询数据
export function getConsPlanDetail(data) {
  return request({
    url: baseUrl + '/adjustment/plan/consPlanDetail',
    method: 'post',
    data: data
  })
}

// 点击V2G后查询出的车主申报计划
export function getDrivePlanList(data) {
  return request({
    url: baseUrl + '/adjustment/plan/drivePlanList',
    method: 'post',
    data: data
  })
}

// 点击V2G申报计划详情
export function getDrivePlanDetail(data) {
  return request({
    url: baseUrl + '/adjustment/plan/drivePlanDetail',
    method: 'post',
    data: data
  })
}

// 邀约 最外层邀约 以服务商为单位
export function handleInvite(data) {
  return request({
    url: baseUrl + '/adjustment/plan/invite',
    method: 'post',
    data: data
  })
}

// 邀约 最外层邀约 批量邀约
export function handleBatchInvite(data) {
  return request({
    url: baseUrl + '/adjustment/plan/batchInvite',
    method: 'post',
    data: data
  })
}

// 制定计划
export function formulatePlan(data) {
  return request({
    url: baseUrl + '/adjustment/plan/formulate',
    method: 'post',
    data: data
  })
}

// 制定计划-有序充电 调控专员修改计划明细数据
export function formulateOrderCharging(data) {
  return request({
    url: baseUrl + '/adjustment/plan/formulateOrderCharging',
    method: 'post',
    data: data
  })
}

// 制定计划-V2G 调控专员修改计划明细数据
export function formulateV2G(data) {
  return request({
    url: baseUrl + '/adjustment/plan/formulateV2G',
    method: 'post',
    data: data
  })
}

// 提交计划审核
export function requestReview(data) {
  return request({
    url: baseUrl + '/adjustment/plan/requestReview',
    method: 'post',
    data: data
  })
}

// 审批计划
export function reviewResult(data) {
  return request({
    url: baseUrl + '/adjustment/plan/reviewResult',
    method: 'post',
    data: data
  })
}

// 计划审核记录--生效记录
export function getOperationRecordList(data) {
  return request({
    url: baseUrl + '/adjustment/plan/getDrOperationRecordList',
    method: 'post',
    data: data
  })
}

// 需求申报记录
export function getPlanRecord(data) {
  return request({
    url: baseUrl + '/adjustment/plan/planRecordPage',
    method: 'post',
    data: data
  })
}

// 计划申报提交-暂未对接负荷
export function declarePlan(data) {
  return request({
    url: baseUrl + '/adjustment/plan/declarePlan',
    method: 'post',
    data: data
  })
}

// 判断去申报按钮到底干什么事情
export function getOperation(data) {
  return request({
    url: baseUrl + '/adjustment/plan/getOperation',
    method: 'post',
    data: data
  })
}

// 申报记录详情顶部数据
export function getRecordDetail(data) {
  return request({
    url: baseUrl + '/adjustment/plan/applyPlanTopDetail',
    method: 'post',
    data: data
  })
}

// 审批人列表
export function getApproverList(data) {
  return request({
    url: '/vehicle-grid-system/system/user/search',
    method: 'post',
    data: data
  })
}

// 申报记录 运营商申报计划详情
export function getPlanDetailsDeclareInfo(data) {
  return request({
    url: baseUrl + '/adjustment/plan/operatePlanDetailDeclareInfo',
    method: 'post',
    data: data
  })
}

// 申报记录 点击编辑计划后查询出的站点户号申报计划
export function getConsPlanListDeclareInfo(data) {
  return request({
    url: baseUrl + '/adjustment/plan/consPlanListDeclareInfo',
    method: 'post',
    data: data
  })
}

// 申报记录 点击站点户号申报计划详情查询数据
export function getConsPlanDetailDeclareInfo(data) {
  return request({
    url: baseUrl + '/adjustment/plan/consPlanDetailDeclareInfo',
    method: 'post',
    data: data
  })
}

// 申报记录 点击V2G后查询出的车主申报计划
export function getDrivePlanListDeclareInfo(data) {
  return request({
    url: baseUrl + '/adjustment/plan/drivePlanListDeclareInfo',
    method: 'post',
    data: data
  })
}

// 申报记录 点击V2G申报计划详情
export function getDrivePlanDetailDeclareInfo(data) {
  return request({
    url: baseUrl + '/adjustment/plan/drivePlanDetailDeclareInfo',
    method: 'post',
    data: data
  })
}