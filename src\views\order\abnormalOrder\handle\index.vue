<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
        <div class="device-head">
            <img src="@/assets/order/order-top-icon.png" class="device-head-icon">
            
            <div class="device-info-wrap">
                <div class="device-title-wrap">
                    <div class="device-title">订单编号：{{ abnormalOrderId }}</div>
                </div>
                <div class="device-info-wrap">
                    <el-row>
                        <el-col :span="6">
                            <span class="label">第三方订单编号：</span>
                            <span class="value">{{thirdPartyOrderNumber}}</span>
                        </el-col>
                        <el-col :span="6">
                            <span class="label">用户类型：</span>
                            <span class="value">{{userType}}</span>
                        </el-col>
                        <el-col :span="6">
                            <span class="label">用户手机号：</span>
                            <span class="value">{{userPhone}}</span>
                        </el-col>
                        <el-col :span="6">
                            <span class="label">下单渠道：</span>
                            <span class="value">{{orderChannel}}</span>
                        </el-col>
                    </el-row>
                </div>
            </div>

            <div class="device-status-wrap">
                    <div class="device-status-item-wrap">
                        <div class="device-status-item-title">订单状态</div>
                        <div class="device-status-success">{{  orderStatus}}</div>
                    </div>

            </div>

        </div>

        <div class="info-card" >
            <div class="card-head" >
                <div class="before-icon"></div>
                <div class="card-head-text">异常信息</div>
            </div>
            <div class="card-head-after" style="margin-bottom: 16px;"></div>

            <div class="form-wrap">
                <el-row :gutter="20">
                    <el-col :span="8" v-for="(item, key) in baseInfoOperation" :key="key" style="margin-bottom: 24px;">
                    <div style="display: flex;">
                        <div class="info-title">{{labels.baseInfo[key]}}：</div>
                        <div class="info-detail">{{ item }}</div>
                    </div>
                    </el-col>
                </el-row>
            </div>
        </div>

        <div class="info-card" >
            <div class="card-head" >
                <div class="before-icon"></div>
                <div class="card-head-text">订单信息</div>
            </div>
            <div class="card-head-after" style="margin-bottom: 16px;"></div>


            <div class="form-wrap">
                <el-row :gutter="20">
                    <el-col :span="8" v-for="(item, key) in orderInfoOperation" :key="key" style="margin-bottom: 24px;">
                    <div style="display: flex;">
                        <div class="info-title">{{labels.orderInfo[key]}}：</div>
                        <div class="info-detail">{{ item }}</div>
                    </div>
                    </el-col>
                </el-row>

                <el-radio-group v-model="orderTable" style="margin-bottom: 16px;">
                    <el-radio-button label="charge" >充电进度数据</el-radio-button>
                    <el-radio-button label="settlement" >结算信息报文</el-radio-button>
                </el-radio-group>

                <BuseCrud
                    style="margin-bottom: 16px;"
                    v-if="orderTable === 'charge'"
                    ref="chargeInfo"
                    :tableColumn="chargeInfoTableColumn"
                    :tableData="chargeInfoData"
                    :modalConfig="{ addBtn: false, menu: false }"
                >
                </BuseCrud>

                <BuseCrud
                    style="margin-bottom: 16px;"
                    v-if="orderTable === 'settlement'"
                    ref="settlementInfo"
                    :tableColumn="settlementInfoTableColumn"
                    :tableData="settlementInfoData"
                    :modalConfig="{ addBtn: false, menu: false }"
                >
                </BuseCrud>
            </div>
        </div>

        <div class="info-card" >
            <div class="card-head" >
                <div class="before-icon"></div>
                <div class="card-head-text">修订信息</div>
            </div>

            <div class="card-head-after" style="margin-bottom: 16px;"></div>


            <div class="form-wrap">
                <el-form :model="form" :rules="rules" ref="form"  label-position="top">
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item
                                label="充电开始时间"
                                prop="startTime"
                                :label-width="formLabelWidth"
                            >
                                <el-date-picker
                                    v-model="form.startTime"
                                    type="datetime"
                                    placeholder="请选择时间"
                                    style="width: 100%"
                                >
                                </el-date-picker>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item
                                label="充电结束时间"
                                prop="endTime"
                                :label-width="formLabelWidth"
                            >
                                <el-date-picker
                                    v-model="form.endTime"
                                    type="datetime"
                                    placeholder="请选择时间"
                                    style="width: 100%"
                                >
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <!-- <BuseCrud
                        ref="settlement"
                        style="margin-bottom: 16px;"
                        :tableColumn="changeTableColumn"
                        :tableData="changeData"
                        :modalConfig="{ addBtn: false, menu: false }"
                    >
                    </BuseCrud> -->
                    <vxe-table
                            round
                            show-footer
                            :data="changeData"
                            :edit-config="{ trigger: 'click', mode: 'cell', showIcon: false }"
                        >
                            <!-- 固定列 -->
                            <vxe-column field="period" title="时段标识" width="120" ></vxe-column>
                            <vxe-column field="timeRange" title="时段" width="150" ></vxe-column>

                            <!-- 充电量相关列 -->
                            <vxe-column field="periodPq" title="原充电量(kWh)" width="150"></vxe-column>
                            <vxe-column 
                            field="periodPqAfterC" 
                            title="新充电量(kWh)" 
                            width="150"
                            :edit-render="{
                                autofocus: '.vxe-input--inner',
                            }"
                            >
    
                            <template #edit="{ row }">
                                <vxe-input 
                                style="color: #217AFF !important;"
                                v-if="row.period === '尖' || row.period === '峰' || row.period === '平' || row.period === '谷'"
                                v-model="row.periodPqAfterC" 
                                type="number" 
                                :min="0" 
                                :step="0.01"
                                @change="updateRow(row)"
                                ></vxe-input>

                                <div style="color: #217AFF !important;" v-else>{{ row.periodPqAfterC}}</div>
                            </template>
                            </vxe-column>

                            <!-- 电价 -->
                            <vxe-column field="periodElecPrice" title="电价(元)" width="100">
                            </vxe-column>

                            <!-- 电费列 -->
                            <vxe-column field="periodElecFee" title="原电费(元)" width="100"></vxe-column>
                            <vxe-column field="periodElecFeeAfterC" title="新电费(元)" width="100">
                            <!-- <template #default="{ row }">
                                {{ formatNumber(row.periodElecFeeAfterC) }}
                            </template> -->
                            </vxe-column>

                            <!-- 服务费 -->
                            <vxe-column field="periodServicePrice" title="服务费单价" width="120">
                            <!-- <template #default="{ row }">
                                {{ formatNumber(row.periodServicePrice) }}
                            </template> -->
                            </vxe-column>
                            <vxe-column field="periodServiceFee" title="原服务费(元)" width="120"></vxe-column>
                            <vxe-column field="periodServiceFeeAfterC" title="新服务费(元)" width="120">
                            <!-- <template #default="{ row }">
                                {{ formatNumber(row.periodServiceFeeAfterC) }}
                            </template> -->
                            </vxe-column>

                            <!-- 总金额 -->
                            <vxe-column field="periodTotalFee" title="原总金额(元)" width="120"></vxe-column>
                            <vxe-column field="periodTotalFeeAfterC" title="新总金额(元)" width="120">
                            <!-- <template #default="{ row }">
                                {{ formatNumber(row.periodTotalFeeAfterC) }}
                            </template> -->
                            </vxe-column>


                    </vxe-table>

                    <div class="explain">
                        注：退款金额（元）=原金额（元）-新金额（元），但不超过原实扣金额
                    </div>

                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item
                                label="问题责任方"
                                prop="responsibleParty"
                                :label-width="formLabelWidth"
                            >
                                <el-select
                                    v-model="form.responsibleParty"
                                    placeholder="请选择问题责任方"
                                    style="width: 100%"
                                    >
                                    <el-option
                                        v-for="item in  dict.type.ls_responsible_party "
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item
                                label="处理说明"
                                prop="remark"
                                :label-width="formLabelWidth"
                            >
                                <el-input
                                    v-model="form.remark"
                                    type="textarea"
                                    :rows="3"
                                    placeholder="请输入"
                                ></el-input>
                            </el-form-item>
                        </el-col>  
                    </el-row>
                </el-form>
            </div>
        </div>

        <div class="bottom-wrap">
            <el-button
                @click="() => handleCancel()"
            >
                取消
            </el-button>
            <el-button
                type="primary"
                @click="() => handleConfirm()"
            >
                提交
            </el-button>
        </div>

    </div>
    
  </template>
  
  <script>

import {
    getAbnormalOrderDetail,
    abnormalOrderHandle
  } from '@/api/order/index';
import { flatten } from 'mathjs';
import { v } from 'vxe-table';
  
    export default {
    components: {
        
    },
    dicts: [
        'ls_responsible_party',
    ],
    data() {
        return {
            orderNo: '',
            abnormalOrderId: '',
            thirdPartyOrderNumber: '',
            userType: '',
            userPhone:'',
            orderChannel: '',
            orderStatus: '',
            baseInfoOperation: {
                abnormalLevel: '',
                abnormalType: '',
                abnormalName: '',
                abnormalReason: '',
                abnormalTime: '',
            },
            orderInfoOperation: {
                stationName: '',
                pileName: '',
                enterpriseNumber: '',
                paymentMethod: '',
                chargeStatus: '',
                pileAccessMethod: '',
                serviceFeeMode: '',
                chargeFeeMode: '',
                orderSource: '',
                pileSource: ''
            },
            labels: {
                baseInfo: {
                    abnormalLevel: '异常等级',          
                    abnormalType: '异常类型',          
                    abnormalName: '异常名称',      
                    abnormalReason: '异常原因',              
                    abnormalTime: '异常上报时间',       
                },
                orderInfo: {
                    stationName: '充电站',
                    pileName: '充电桩',
                    enterpriseNumber :'企业编号',
                    paymentMethod: '支付方式',
                    chargeStatus: '充电状态',
                    pileAccessMethod:'桩接入方式',
                    serviceFeeMode: '服务费计费模型',
                    chargeFeeMode: '电费计费模型',
                    orderSource: '订单来源',
                    pileSource: '桩来源',
                }
            },

            form: {
                startTime:  '',
                endTime:  '',
                responsibleParty: '',
                remark: '',
            },
            rules: {
                startTime: [
                    { required: true, message: '请选择开始时间', trigger: 'blur'}
                ],
                endTime: [
                    { required: true, message: '请选择结束时间', trigger: 'blur'}
                ],
                responsibleParty: [
                   { required: true, message: '请选择问题责任方', trigger: 'blur'}
                ],
                remark: [
                    { required: true, message: '请输入处理说明', trigger: 'blur'}
                ]
            },
            formLabelWidth: '120px',

            orderTable: 'charge',

            chargeInfoTableColumn: [
                {
                    field: 'orderTime',
                    title: '下单时间',
                    minWidth: 180,
                },
                {
                    field: 'chargeStartTime',
                    title: '充电开始时间',
                    minWidth: 180,
                },
                {
                    field: 'chargeEndTime',
                    title: '充电结束时间',
                    minWidth: 180,
                },
                {
                    field: 'chargeAmount',
                    title: '充电电量(kWh)',
                    minWidth: 180,
                },
                {
                    field: 'meterReadAmount',
                    title: '抄表电量(kWh)',
                    minWidth: 180,
                },
            ],

            chargeInfoData:[
            ],

            settlementInfoTableColumn: [
                {
                    title: '充电电量(kWh)',
                    field: 'chargeAmount',
                    minWidth: 180,
                },
                {
                    title: '尖电量(kWh)',
                    field: 'cuspAmount',
                    minWidth: 180,
                },
                {
                    title: '峰电量(kWh)',
                    field: 'peakAmount',
                    minWidth: 180,
                },
                {
                    title: '平电量(kWh)',
                    field: 'flatAmount',
                    minWidth: 180,
                },
                {
                    title: '谷电量(kWh)',
                    field: 'valleyAmount',
                    minWidth: 180,
                },
                {
                    title: '充电电费(元)',
                    field: 'electricityFee',
                    minWidth: 180,
                },
                {
                    title: '充电服务费(元)',
                    field: 'serviceFee',
                    minWidth: 180,
                }
            ],
            settlementInfoData: [
            ],
            changeData: [],

        };
    },

    computed: {


    },
    mounted() {
        const orderNo = this.$route.query.orderNo;
        this.orderNo = orderNo;
        this.getAbnormalOrderDetail();

        // mock

        // const periodFeeBOList = [
        //     {
        //         timeFlag: '1',
        //         beginTime: '00:00',
        //         endTime: '01:00',
        //         periodPq: '100',
        //         periodElecPrice: '2',
        //         periodElecFee: '200',
        //         periodServicePrice: '1',
        //         periodServiceFee: '100',
        //         periodTotalFee: '300',
        //         periodPqAfterC: '',
        //         periodElecFeeAfterC: '',
        //         periodServiceFeeAfterC: '',
        //         periodTotalFeeAfterC:'',
        //     },
        //     {
        //         timeFlag: '2',
        //         beginTime: '00:00',
        //         endTime: '01:00',
        //         periodPq: '100',
        //         periodElecPrice: '2',
        //         periodElecFee: '200',
        //         periodServicePrice: '1',
        //         periodServiceFee: '100',
        //         periodTotalFee: '300',
        //         periodPqAfterC: '',
        //         periodElecFeeAfterC: '',
        //         periodServiceFeeAfterC: '',
        //         periodTotalFeeAfterC:'',
        //     },
        //     {
        //         timeFlag: '3',
        //         beginTime: '00:00',
        //         endTime: '01:00',
        //         periodPq: '100',
        //         periodElecPrice: '2',
        //         periodElecFee: '200',
        //         periodServicePrice: '1',
        //         periodServiceFee: '100',
        //         periodTotalFee: '300',
        //         periodPqAfterC: '',
        //         periodElecFeeAfterC: '',
        //         periodServiceFeeAfterC: '',
        //         periodTotalFeeAfterC:'',
        //     },
        //     {
        //         timeFlag: '4',
        //         beginTime: '00:00',
        //         endTime: '01:00',
        //         periodPq: '100',
        //         periodElecPrice: '2',
        //         periodElecFee: '200',
        //         periodServicePrice: '1',
        //         periodServiceFee: '100',
        //         periodTotalFee: '300',
        //         periodPqAfterC: '',
        //         periodElecFeeAfterC: '',
        //         periodServiceFeeAfterC: '',
        //         periodTotalFeeAfterC:'',
        //     }
        // ]

        //     const list = []

        //     const map = {
        //         '1': '尖',
        //         '2': '峰',
        //         '3': '平',
        //         '4': '谷',
        //     }

        //     periodFeeBOList.forEach(item => {
        //         list.push({
        //             ...item,
        //             period: map[item.timeFlag],
        //             timeRange: item.beginTime + '-' + item.endTime,
        //         })
        //     })

        //     const total = this.handleTotal(list)
        //     list.push(total)

        //     const discount = this.handleDiscount(list)
        //     list.push(discount)

        //     const receive = this.handleReceive(list)
        //     list.push(receive)

        //     const real  = this.handleReal(list)
        //     list.push(real)

        //     this.changeData = list


            
        
    },
    methods: {
        // 处理合计
        handleTotal(list) {
            let periodPqTotal = 0 // 原充电量
            let periodPqAfterCTotal = 0 // 新充电量
            let periodElecFeeToTal = 0 // 原电费
            let periodElecFeeAfterCTotal = 0 // 新电费
            let periodServiceFeeTotal = 0 // 原服务费
            let periodServiceFeeAfterCTotal = 0 // 新服务费
            let periodTotalFeeTotal = 0 // 原合计
            let periodTotalFeeAfterCTotal = 0 // 新合计
            
            list.forEach((item, index) => {
                if(index<4) {
                    periodPqTotal += Number(item.periodPq)
                    periodPqAfterCTotal += Number(item.periodPqAfterC)
                    periodElecFeeToTal += Number(item.periodElecFee)
                    periodElecFeeAfterCTotal += Number(item.periodElecFeeAfterC)
                    periodServiceFeeTotal += Number(item.periodServiceFee)
                    periodServiceFeeAfterCTotal += Number(item.periodServiceFeeAfterC)
                    periodTotalFeeTotal += Number(item.periodTotalFee)                  
                    periodTotalFeeAfterCTotal += Number(item.periodTotalFeeAfterC)
                   
                }
            })

            return {
                period: '小计',
                timeRange: '-',
                periodPq: periodPqTotal,
                periodPqAfterC: periodPqAfterCTotal,
                periodElecPrice:'-',

                periodElecFee: periodElecFeeToTal,
                periodElecFeeAfterC: periodElecFeeAfterCTotal,
                periodServicePrice: '-',
                periodServiceFee: periodServiceFeeTotal,
                periodServiceFeeAfterC: periodServiceFeeAfterCTotal,
                periodTotalFee: periodTotalFeeTotal,
                periodTotalFeeAfterC: periodTotalFeeAfterCTotal
            }
        },
        // 处理优惠金额
        handleDiscount() {
            return {
                period: '优惠',
                timeRange: '-',
                periodPq: '-',
                periodPqAfterC: '-',
                periodElecPrice:'-',

                periodElecFee: '-',
                periodElecFeeAfterC: '-',
                periodServicePrice: '-',
                periodServiceFee: '-10',
                periodServiceFeeAfterC: '-10',
                periodTotalFee: '-10',
                periodTotalFeeAfterC: '-10'
            } 
        },

        // 处理应收金额
        handleReceive(list) {
            const item = list[4]
            return {
                period: '应收',
                timeRange: '-',
                periodPq: '-',
                periodPqAfterC: '-',
                periodElecPrice:'-',

                periodElecFee: item.periodElecFee,
                periodElecFeeAfterC: item.periodElecFeeAfterC,
                periodServicePrice: '-',
                periodServiceFee: item.periodServiceFee,
                periodServiceFeeAfterC: item.periodServiceFeeAfterC,
                periodTotalFee: item.periodTotalFee,
                periodTotalFeeAfterC: item.periodTotalFeeAfterC
            }
        },
        handleReal(list) {
            const item1 = list[4]
            const item2 = list[5]
            return {
                period: '应收',
                timeRange: '-',
                periodPq: '-',
                periodPqAfterC: '-',
                periodElecPrice:'-',

                periodElecFee: item2.periodElecFee,
                periodElecFeeAfterC: item2.periodElecFeeAfterC,
                periodServicePrice: '-',
                periodServiceFee: this.sumValues(item2.periodServiceFee,item1.periodServiceFee),
                periodServiceFeeAfterC: this.sumValues(item2.periodServiceFeeAfterC,item1.periodServiceFeeAfterC) ,
                periodTotalFee: this.sumValues(item2.periodTotalFee,item1.periodTotalFee) ,
                periodTotalFeeAfterC: this.sumValues(item2.periodTotalFeeAfterC,item1.periodTotalFeeAfterC) 
            }
        },
        async getAbnormalOrderDetail() {
            const [err,res] = await getAbnormalOrderDetail({
                orderNo: this.orderNo
            })

            if (err) return

             // 顶部信息
             const {
                orderNo,
                partOrderNo,
                userType,
                mobile,
                applyModeName,
                orderStatus,
            } = res.data

            this.abnormalOrderId = orderNo;
            this.thirdPartyOrderNumber = partOrderNo;
            this.userType = userType;
            this.userPhone = mobile;
            this.orderChannel = applyModeName;
            this.orderStatus = orderStatus;

   
            // 异常信息
            const {
                exceptionLog
            } = res.data

            if(exceptionLog.length) {
                const  {
                    level,
                    type,
                    name,
                    description,
                    reportTime,
                } = exceptionLog[0]
                this.baseInfoOperation = {
                    abnormalLevel: level,
                    abnormalType: type,
                    abnormalName: name,
                    abnormalReason: description,
                    abnormalTime: reportTime,
                }
            }

            // 订单信息
            const {
                stationName,
                pileName,
                enterpriseNo,
                payMode,
                chargeStatus,
                pileInType,
                serviceChcNo,
                elecChcNo,
                orderSource,
                pileSource,
            } = res.data

            this.orderInfoOperation = {
                stationName,
                pileName,
                enterpriseNumber: enterpriseNo,
                paymentMethod: payMode,
                chargeStatus,
                pileAccessMethod: pileInType,
                serviceFeeMode: serviceChcNo,
                chargeFeeMode: elecChcNo,
                orderSource,
                pileSource
            }

            // 充电进度数据
            const {
                applyTime,
                bgnTime,
                endTime,
                chargePq,
                tmr,
            } = res.data

            this.chargeInfoData = [
                {
                    orderTime: applyTime,
                    chargeStartTime: bgnTime,
                    chargeEndTime: endTime,
                    chargeAmount: chargePq,
                    meterReadAmount: tmr,
                }
            ]

            // 结算信息报文
            const {
                // chargePq,
                cuspElec,
                peakElec,
                flatElec,
                valleyElec,
                elecAmt,
                serviceAmt,
            } = res.data

            this.settlementInfoData =[
                {
                    chargeAmount: chargePq,
                    cuspAmount: cuspElec,
                    peakAmount: peakElec,
                    flatAmount: flatElec,
                    valleyAmount:  valleyElec,
                    electricityFee: elecAmt,
                    serviceFee: serviceAmt
                }
            ]

            // 修订信息
            const {
                periodFeeBOList
            }  = res.data

            const list = []

            const map = {
                '1': '尖',
                '2': '峰',
                '3': '平',
                '4': '谷',
            }

            if(periodFeeBOList && periodFeeBOList.length > 0) {
                periodFeeBOList.forEach(item => {
                    list.push({
                        ...item,
                        period: map[item.timeFlag],
                        timeRange: item.beginTime + '-' + item.endTime,
                    })
                })

                const total = this.handleTotal(list)
                list.push(total)

                const discount = this.handleDiscount(list)
                list.push(discount)

                const receive = this.handleReceive(list)
                list.push(receive)

                const real  = this.handleReal(list)
                list.push(real)

                this.changeData = list
            }

            

           

            

       

        },

        // 数值格式化
    formatNumber(value) {
      return Number(value).toLocaleString(undefined, { 
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },

        // 单行更新
        updateRow(row) {
        row.periodElecFeeAfterC = this.calculateFee(row.periodPqAfterC, row.periodElecPrice)
        row.periodServiceFeeAfterC = this.calculateFee(row.periodPqAfterC, row.periodServicePrice)
        row.periodTotalFeeAfterC = this.sumValues(row.periodElecFeeAfterC, row.periodServiceFeeAfterC)
        //   this.updateSummary()

        // 创建新数组副本 (触发响应式)
        const newData = [...this.changeData]

        // 更新汇总行
        newData[4] = this.handleTotal(newData)
        newData[6] = this.handleReceive(newData)
        newData[7] = this.handleReal(newData)

        // 替换整个数组 (强制触发视图更新)
        this.changeData = newData
                
        },

        // 计算费用
        calculateFee(quantity, periodElecPrice) {
        return Math.round(quantity * periodElecPrice * 100) / 100
        },



        // 精确加法
        sumValues(a, b) {
        return Math.round((Number(a) + Number(b)) * 100) / 100
        },

        // 取消
         handleCancel() {
            this.$router.back()
        },
        // 处理
        handleConfirm: _.debounce(async function(){
            this.$refs.form.validate(async (valid) => {
                if (valid) {
                    const {
                        startTime,
                        endTime,
                        responsibleParty,
                        remark,
                    } = this.form

                    const params = {
                        orderNo: this.orderNo,
                        problemParty: responsibleParty,
                        handleMsg: remark,
                        bgnTimeAfterC: startTime,
                        endTimeAfterC: endTime,
                        chargePqAfterC: this.chargeInfoData[4]?.periodPqAfterC || '',
                        elecAmtAfterC: this.chargeInfoData[4]?.periodElecFeeAfterC || '',
                        serviceAmtAfterC: this.chargeInfoData[4]?.periodServiceFeeAfterC || '',
                        chargeAmtAfterC: this.chargeInfoData[4]?.periodTotalFeeAfterC || '',
                        cuspElecAfterC: this.chargeInfoData[0]?.periodPqAfterC || '',
                        cuspElecAmtAfterC: this.chargeInfoData[0]?.periodElecFeeAfterC || '',
                        cuspServiceAmtAfterC: this.chargeInfoData[0]?.periodServiceFeeAfterC|| '',
                        cuspChargeAmtAfterC: this.chargeInfoData[0]?.periodTotalFeeAfterC || '',
                        peakElecAfterC: this.chargeInfoData[1]?.periodPqAfterC || '',
                        peakElecAmtAfterC: this.chargeInfoData[1]?.periodElecFeeAfterC || '',
                        peakServiceAmtAfterC: this.chargeInfoData[1]?.periodServiceFeeAfterC|| '',
                        peakChargeAmtAfterC: this.chargeInfoData[1]?.periodTotalFeeAfterC || '',
                        flatElecAfterC: this.chargeInfoData[2]?.periodPqAfterC|| '',
                        flatElecAmtAfterC: this.chargeInfoData[2]?.periodElecFeeAfterC || '',
                        flatServiceAmtAfterC: this.chargeInfoData[2]?.periodServiceFeeAfterC|| '',
                        flatChargeAmtAfterC: this.chargeInfoData[2]?.periodTotalFeeAfterC|| '',
                        valleyElecAfterC: this.chargeInfoData[3]?.periodPqAfterC|| '',
                        valleyElecAmtAfterC: this.chargeInfoData[3]?.periodElecFeeAfterC|| '',
                        valleyServiceAmtAfterC: this.chargeInfoData[3]?.periodServiceFeeAfterC|| '',
                        valleyChargeAmtAfterC: this.chargeInfoData[3]?.periodTotalFeeAfterC|| '',
                    }

                    const [err,res] = await abnormalOrderHandle(
                           { ...params}
                        )
                        if(err) return
                        
                        this.$message.success('处理成功')

                        setTimeout(() => {
                            this.$router.back();
                        }, 2000);

                }
            });
        },300)

    }
  };
  </script>
  
  <style lang="scss" scoped>
  ::v-deep .bd3001-content {
    padding: 0 !important;
    .body--wrapper  {
        min-height: 40px !important;
    }
  }
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }

  .device-head {
    background-color: #fff;
    
    display: flex;
    height: 112px;
    display: flex;
    align-items: center;
    padding: 0 24px;
    box-sizing: border-box;
    .device-head-icon {
        width: 48px;
        height: 48px;
        margin-right: 24px;
    }
    .device-info-wrap {
        flex: 1;
        .device-title-wrap {
            height: 32px;
            display: flex;
            align-items: center;
            .device-title {
                font-weight: 500;
                font-size: 24px;
                color: #12151A;
            }
            .device-status {
                // width: 50px;
                padding: 0 10px;
                height: 24px;
                border-radius: 10px 0 10px 0;
                font-size: 16px;
                font-weight: 400;
                line-height: 24px;
                text-align: center;
                color: #fff;
                background: linear-gradient(321.01deg, #FFB624 8.79%, #FF8D24 100.27%);
                margin-left: 12px;
            }
        }
        .device-info-wrap {
            height: 16px;
            margin-top: 16px;
            font-size: 16px;
            font-weight: 400;
            color: #292B33;
        }
    }
    .device-status-wrap {
        display: flex;
        align-items: center;
        .device-status-item-wrap {
            width: 150px;
            .device-status-item-title {
                font-weight: 400;
                font-size: 14px;
                line-height: 14px;
                color: #505363;
                margin: 0 auto 12px auto;
                text-align: center;
            }
            .device-status {
                width: 86px;
                height: 34px;
                border-radius: 4px;
                display: flex;
                margin: 0 auto;
                align-items: center;
                justify-content: center;
                background-color: #EBF3FF;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 18px;
                color: #217AFF;
            }
            .device-status-success {
                width: 86px;
                height: 34px;
                border-radius: 4px;
                display: flex;
                margin: 0 auto;
                align-items: center;
                justify-content: center;
                background-color: #EBFFF1;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 18px;
                color: #00C864;

            }
        }
        .device-status-split{
            width: 1px;
            height: 36px;
            background-color: #E9EBF0;
        }
    }
    
  
  }

  .info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    // background: linear-gradient(180deg, #E9F2FF 0%, #FFFFFF 100%);
    .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
    }
    .card-head-text {
        flex:1;
        font-weight: 500;
        font-size: 16px;
        color: #12151A;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
          background-color: #1ab2ff;
          border-color: #1ab2ff;
        }
        ::v-deep .el-button--small {
          font-size: 14px;
        }
      .distribution {
          margin-left: 24px;
          margin-right: 24px;
          display: flex;
          align-items: center;
        }
    }
  }
  .card-head-after {
      height: 1px;
      width: 100%;
      background-color: #E9EBEF;
  }
  
  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
      padding: 0 16px 0 16px;
      .info-title {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #505363;
      }
      .info-detail {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #292B33;
      }
      .info-amount {
        height: 28px;
        background-color: #FFF7E6;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 8px;
        font-weight: 400;
        font-size: 20px;
        color: #FE8921;
        margin-top: -6px;
        margin-right: 4px;

      }
      .info-img {
        width: 140px;
        height: 140px;
      }

      .explain {
            font-weight: 400;
            font-size: 16px;
            line-height: 16px;
            margin: 0 0 24px 0;
            color: #505363;

      }
    }
  }


  .container {
      position: relative;
      padding-bottom: 100px;
      box-sizing: border-box;
      .bottom-wrap {
          position: fixed;
          z-index: 100;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 86px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          background-color: #FFFFFF;
          padding-right: 32px;
          box-sizing: border-box;
      }
  }



 
  </style>
  