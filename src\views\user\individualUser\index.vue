<template>
  <div class="container container-float" style="padding: 0">
    <el-tabs v-model="activeName" @tab-click="handleClickTab">
      <!-- 个人用户管理标签栏内容 -->
      <el-tab-pane label="个人用户管理" name="manage">
        <div class="table-wrap">
          <BuseCrud
            ref="crud"
            :loading="manageLoading"
            :filterOptions="manageFilterOptions"
            :tablePage="manageTablePage"
            :tableColumn="manageTableColumn"
            :tableData="manageTableData"
            :pagerProps="pagerProps"
            :modalConfig="modalConfig"
            class="buse-wrap-user"
            @loadData="loadManageData"
          >
            <template slot="defaultHeader">
              <div>
                <div class="card-head">
                  <div class="card-head-text">个人用户列表</div>
                </div>
              </div>
            </template>

            <template slot="operate" slot-scope="{ row }">
              <div class="menu-box">
                <el-button type="primary" plain @click="handleDetail(row)">
                  详情
                </el-button>
              </div>
            </template>
          </BuseCrud>
        </div>
      </el-tab-pane>
      <!-- 个人用户注销标签栏内容 -->
      <el-tab-pane label="个人用户注销" name="logout">
        <div class="table-wrap">
          <BuseCrud
            ref="crud"
            :loading="logoutLoading"
            :filterOptions="logoutFilterOptions"
            :tablePage="logoutTablePage"
            :tableColumn="logoutTableColumn"
            :tableData="logoutTableData"
            :pagerProps="pagerProps"
            :modalConfig="modalConfig"
            class="buse-wrap-user"
            @loadData="loadLogoutData"
          >
            <template slot="defaultHeader">
              <div>
                <div class="card-head">
                  <div class="card-head-text">个人用户注销列表</div>
                </div>
              </div>
            </template>

            <template slot="operate" slot-scope="{ row }">
              <div class="menu-box">
                <el-button type="primary" plain @click="handleDetail(row)">
                  详情
                </el-button>
              </div>
            </template>
          </BuseCrud>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { getUserList, getUserCancelList } from '@/api/user/index';

export default {
  components: {},
  dicts: [
    'ls_charging_user_status', // 个人用户状态（即账号状态）
    'ls_charging_register_channel', // 注册渠道
    'ls_charging_cancel_status', // 个人用户注销状态
  ],
  data() {
    return {
      activeName: 'manage',
      manageLoading: false,
      manageParams: {
        uid: '',
        mobile: '',
        registerChannel: '',
        status: '',
        registerTime: [],
        loginTime: [],
      },
      manageTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      manageTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'identityCard',
          title: 'UID',
          minWidth: 180,
        },
        {
          field: 'nickName',
          title: '用户昵称',
          minWidth: 150,
        },
        {
          field: 'mobile',
          title: '手机号码',
          minWidth: 150,
        },
        {
          field: 'registerChannel',
          title: '注册渠道',
          minWidth: 100,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_register_channel,
              cellValue
            );
          },
        },
        {
          field: 'walletBalance',
          title: '电子钱包余额',
          minWidth: 120,
        },
        {
          field: 'chargeBalance',
          title: '充电包余额',
          minWidth: 100,
        },
        {
          field: 'chargePackageCount',
          title: '充电包数量',
          minWidth: 100,
          align: 'center',
        },
        {
          field: 'totalChargeCapacity',
          title: '累计充电电量(kWh)',
          minWidth: 180,
        },
        {
          field: 'totalElectricityPrice',
          title: '累计实收电费(元)',
          minWidth: 160,
        },
        {
          field: 'totalServicePrice',
          title: '累计实收服务费(元)',
          minWidth: 180,
        },
        {
          field: 'totalRealPrice',
          title: '累计实收金额(元)',
          minWidth: 160,
        },
        {
          field: 'status',
          title: '账号状态',
          minWidth: 110,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_user_status,
              cellValue
            );
          },
        },
        {
          field: 'registerTime',
          title: '注册时间',
          minWidth: 180,
        },
        {
          field: 'latestLoginTime',
          title: '最后登录时间',
          minWidth: 180,
        },
        {
          field: 'action',
          title: '操作',
          slots: { default: 'operate' },
          width: 250,
          align: 'center',
          fixed: 'right',
        },
      ],
      manageTableData: [],

      logoutLoading: false,
      logoutParams: {
        uid: '',
        mobile: '',
        status: '',
        applyTime: [],
        logoutTime: [],
      },
      logoutTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      logoutTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'applyNo',
          title: '申请编号',
          minWidth: 160,
        },
        {
          field: 'identityCard',
          title: 'UID',
          minWidth: 160,
        },
        {
          field: 'nickName',
          title: '用户昵称',
          minWidth: 100,
        },
        {
          field: 'mobile',
          title: '手机号码',
          minWidth: 150,
        },
        {
          field: 'state',
          title: '注销状态',
          minWidth: 110,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_cancel_status,
              cellValue
            );
          },
        },
        {
          field: 'gmtCreate',
          title: '申请时间',
          minWidth: 180,
        },
        {
          field: 'gmtCancel',
          title: '注销时间',
          minWidth: 180,
        },
        {
          field: 'operator',
          title: '操作人',
          minWidth: 180,
        },
        {
          field: 'action',
          title: '操作',
          slots: { default: 'operate' },
          width: 250,
          align: 'center',
          fixed: 'right',
        },
      ],
      logoutTableData: [],
    };
  },

  computed: {
    manageFilterOptions() {
      return {
        config: [
          {
            field: 'uid',
            title: 'UID',
            element: 'el-input',
          },
          {
            field: 'mobile',
            title: '手机号',
            element: 'el-input',
          },
          {
            field: 'registerChannel',
            title: '注册渠道',
            element: 'el-select',
            props: {
              placeholder: '请选择注册渠道',
              options: this.dict.type.ls_charging_register_channel,
            },
          },
          {
            field: 'status',
            title: '账号状态',
            element: 'el-select',
            props: {
              placeholder: '请选择账号状态',
              options: this.dict.type.ls_charging_user_status,
            },
          },
          {
            field: 'registerTime',
            title: '注册时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
              options: [],
              defaultTime: ['00:00:00', '23:59:59'],
            },
          },
          {
            field: 'loginTime',
            title: '最后登陆时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
              options: [],
              defaultTime: ['00:00:00', '23:59:59'],
            },
          },
        ],
        params: this.manageParams,
      };
    },
    logoutFilterOptions() {
      return {
        config: [
          {
            field: 'uid',
            title: 'UID',
            element: 'el-input',
          },
          {
            field: 'mobile',
            title: '手机号',
            element: 'el-input',
          },
          {
            field: 'status',
            title: '注销状态',
            element: 'el-select',
            props: {
              placeholder: '请选择账号状态',
              options: this.dict.type.ls_charging_cancel_status,
            },
          },
          {
            field: 'applyTime',
            title: '申请日期',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
              options: [],
              defaultTime: ['00:00:00', '23:59:59'],
            },
          },
          {
            field: 'logoutTime',
            title: '注销日期',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
              options: [],
              defaultTime: ['00:00:00', '23:59:59'],
            },
          },
        ],
        params: this.logoutParams,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadManageData();
  },
  methods: {
    async handleClickTab({ index }) {
      if (index === '0') {
        // 获取管理列表数据
        this.manageTablePage = {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        };
        this.manageTableData = [];
        this.loadManageData();
      } else {
        // 获取审核列表数据
        this.logoutTablePage = {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        };
        this.logoutTableData = [];
        this.loadLogoutData();
      }
    },

    // 详情
    handleDetail(row) {
      const { userId } = row;
      this.$router.push({
        path: '/v2g-charging/baseInfo/userManage/individualUser/detail',
        query: {
          userId,
        },
      });
    },

    // 个人用户列表
    async loadManageData() {
      const { uid, mobile, registerChannel, status, registerTime, loginTime } =
        this.manageFilterOptions.params;

      console.log(this.manageFilterOptions.params);

      let registerStartTime = '';
      let registerEndTime = '';
      if (registerTime && registerTime.length > 0) {
        registerStartTime = registerTime[0];
        registerEndTime = registerTime[1];
      }

      let lastLoginStartTime = '';
      let lastLoginEndTime = '';
      if (loginTime && loginTime.length > 0) {
        lastLoginStartTime = loginTime[0];
        lastLoginEndTime = loginTime[1];
      }

      this.manageLoading = true;
      const [err, res] = await getUserList({
        identityCard: uid,
        mobile,
        registerChannel,
        status,
        registerStartTime,
        registerEndTime,
        lastLoginStartTime,
        lastLoginEndTime,
        pageNum: this.manageTablePage.currentPage,
        pageSize: this.manageTablePage.pageSize,
      });
      this.manageLoading = false;

      if (err) {
        return this.$message.error(err.message);
      }
      const { data, total } = res;

      this.manageTableData = data;
      this.manageTablePage.total = total;
    },

    // 个人用户注销列表
    async loadLogoutData() {
      const { uid, mobile, status, applyTime, logoutTime } =
        this.logoutFilterOptions.params;

      let gmtCreateStart = '';
      let gmtCreateEnd = '';
      if (applyTime && applyTime.length > 0) {
        gmtCreateStart = applyTime[0];
        gmtCreateEnd = applyTime[1];
      }

      let gmtCancelStart = '';
      let gmtCancelEnd = '';
      if (logoutTime && logoutTime.length > 0) {
        gmtCancelStart = logoutTime[0];
        gmtCancelEnd = logoutTime[1];
      }

      this.logoutLoading = true;
      const [err, res] = await getUserCancelList({
        identityCard: uid,
        mobile,
        state: status,
        gmtCreateStart,
        gmtCreateEnd,
        gmtCancelStart,
        gmtCancelEnd,
        pageNum: this.logoutTablePage.currentPage,
        pageSize: this.logoutTablePage.pageSize,
      });
      this.logoutLoading = false;

      if (err) {
        return this.$message.error(err.message);
      }
      const { data, total } = res;

      const list = [];
      const statusObj = {
        1: '已注销',
        2: '注册',
      };

      data.forEach((item) => {
        list.push({
          ...item,
          stateText: statusObj[item.state],
        });
      });

      this.logoutTableData = list;
      this.logoutTablePage.total = total;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-tabs {
  margin-top: 16px;
  background-color: #f5f6f9;
  .el-tabs__header {
    padding-left: 0;
    display: flex;
    justify-content: center;
    text-align: center;
    margin-bottom: 1px;
    .el-tabs__item {
      padding: 0;
      width: 164px;
      font-size: 18px;
      font-weight: 400;
      background-color: #fff;
    }
    .el-tabs__item.is-active {
      background-color: #1677fe;
      color: #fff;
    }
    .el-tabs__nav-scroll {
      border-radius: 25px;
      border: solid 1px #dfe1e5;
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__nav-wrap::after {
      width: 0;
    }
  }
}

.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }
  margin: 16px;

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
  }
}
</style>
