<template>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="630px"
      @close="handleCancel"
    >
        <div class="info-wrap">
            <div class="info-title">所属充电站：</div>
            <div class="info-detail">{{ form.stationName }}</div>
        </div>

        <div class="info-wrap">
            <div class="info-title">充电桩名称：</div>
            <div class="info-detail">{{ form.pileName }}</div>
        </div>

        <div class="info-wrap">
            <div class="info-title">展示近 10 条枪状态记录</div>
        </div>

        <BuseCrud
            ref="gunInfo"
            :tableColumn="tableColumn"
            :tableData="form.tableData"
            :modalConfig="{ addBtn: false, menu: false }"
        >
        </BuseCrud>

    
    </el-dialog>
  </template>
  <script>  

import {
    editPileControl
} from '@/api/pile/index';

import StatusBg from './statusBg.vue';

import moment from 'moment';

import _ from 'lodash';

  export default {
    props: {
        dialogTitle:{
            type: String,
            default: '枪状态记录'
        }
    },
    components: {
        StatusBg,
    },
    dicts: [
        'ls_charging_status', // 是否状态
        'ls_charging_adjustable_type', // 可调控类型
        'ls_charging_station_control', // 站点调节方式
    ],
    data() {
        return {
            dialogVisible: false,

            form: {
                stationName: '',
                pileName:  '',

                tableData: [],
            },
           

            tableColumn: [
                {
                    field: 'gunName',
                    title: '枪名称',
                    minWidth: 150,
                },
                {
                    title: '运行状态',
                    field: 'runStatus',
                    width: 220,
                    slots: {
                  // 自定义render函数
                    default: ({ row }) => {
                        return (
                        <StatusBg
                            value={row.runStatus}
                        ></StatusBg>
                        );
                    },
                    },
                },
                {
                    title: '时间',
                    field: 'dateTimeStr',
                    width: 200,
                }
            ]

        };
    },
    computed: {},
    mounted() {},
    methods: {

        resetForm() {
            this.stationName = '',
            this.pileName = '',
            this.tableData = []
        },

        handleCancel() {
            this.resetForm();
            console.log(this.form, 'this.form')
            this.dialogVisible = false;
        },

    },
  };
  </script>
  <style lang="scss" scoped>
::v-deep .el-form-item__content{
    display: flex !important;
}

::v-deep .el-input-number {
    width: 90% !important;
}



.info-wrap {
    display: flex;
    height: 20px;
    margin-bottom: 24px;
    align-items: center;
    .info-title {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #505363;
      }
      .info-detail {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #292B33;
      }
}
  </style>
  