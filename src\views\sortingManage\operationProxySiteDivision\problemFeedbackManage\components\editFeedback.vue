<template>
  <el-dialog title="问题描述" :visible.sync="dialogVisible" width="630px">
    <div class="info-card">
      <div class="form-wrap" v-if="dialogVisible">
        <el-form :model="baseInfo.form" ref="baseInfoForm" label-position="top">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="问题描述："
                prop="feedbackContent"
                :label-width="formLabelWidth"
              >
                <el-input
                  type="textarea"
                  :rows="4"
                  v-model="baseInfo.form.feedbackContent"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="bottom-wrap">
      <div>
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleSave" :loading="submitLoading" type="primary">
          提交
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: {
    selectOrderRule: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {},
  dicts: [],
  data() {
    return {
      dialogVisible: false,
      formLabelWidth: '120px',
      baseInfo: {
        form: {
          feedbackContent: '',
        },
      },
      submitLoading: false,
    };
  },
  watch: {
    dialogVisible(value) {
      if (!value) {
        this.baseInfo.form = {
          feedbackContent: '',
        };
      } else {
        if (Object.keys(this.selectOrderRule).length) {
          this.baseInfo.form = {
            feedbackContent: this.selectOrderRule.feedbackContent || '',
          };
        }
      }
    },
  },
  computed: {},
  mounted() {},
  methods: {
    handleCancel() {
      this.dialogVisible = false;
    },
    // 保存
    handleSave() {
      this.$refs.baseInfoForm.validate(async (valid) => {
        if (valid) {
          this.$emit('editFeedbackSave', this.baseInfo.form.feedbackContent);
          this.dialogVisible = false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
        background-color: #1ab2ff;
        border-color: #1ab2ff;
      }
      ::v-deep .el-button--small {
        font-size: 14px;
      }
      .distribution {
        margin-left: 24px;
        margin-right: 24px;
        display: flex;
        align-items: center;
      }
    }
  }

  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
    padding: 0 16px 16px 16px;
    .custom-header {
      background: -webkit-gradient(
          linear,
          left top,
          left bottom,
          from(rgba(0, 149, 255, 0.5)),
          to(rgba(87, 152, 255, 0))
        ),
        #f5faff;
      background: linear-gradient(
          180deg,
          rgba(0, 149, 255, 0.5) 0%,
          rgba(87, 152, 255, 0) 100%
        ),
        #f5faff;
      background-repeat: no-repeat;
    }
  }
}

.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .bottom-wrap {
    // position: fixed;
    // bottom: 0;
    // left: 0;
    // width: 100%;
    // height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
  }
}
::v-deep .hidden-label .el-form-item__label:before {
  visibility: hidden;
}
</style>
