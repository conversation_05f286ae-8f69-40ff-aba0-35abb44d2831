<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
        <div class="info-card" >
              <div class="card-head">
                  <div class="before-icon"></div>
                  <div class="card-head-text">班组信息</div>
              </div>

              <div class="card-head-split"></div>



              <div class="form-wrap">
                <el-form :model="form" :rules="rules" ref="baseInfoForm"  label-position="top">
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item
                                label="班组名称"
                                prop="teamName"
                                :label-width="formLabelWidth"
                            >
                                <el-input v-model="form.teamName"  placeholder="请输入班组名称"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                   
                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item
                                label="班组成员"
                                prop="teamMember"
                                :label-width="formLabelWidth"
                            >
                                    <BuseCrud
                                        ref="crud"
                                        :loading="table.loading"
                                        :tablePage="table.page"
                                        :tableColumn="tableColumn"
                                        :tableData="table.data"
                                        :pagerProps="pagerProps"
                                        :modalConfig="modalConfig"
                                        @loadData="getTablePage"
                                    >
                                    <template slot="defaultHeader">
                                        <div>
                                            <div class="top-button-wrap">
                                                <!-- <el-button 
                                                    @click="() => handleChooseStation()"
                                                >
                                                    删除
                                                </el-button> -->
                                                <el-button
                                                    type="primary"
                                                    @click="() => handleAdd()"
                                                >
                                                    添加人员
                                                </el-button>
                                            
                                            </div>
                                        </div>
                                        
                                    </template>

                                        
                                        <template slot="operate" slot-scope="{ row }">
                                            <div class="menu-box">
                                                <el-button
                                                    type="danger"
                                                    plain
                                                    @click="() => handleDelete(row)"
                                                >
                                                    删除
                                                </el-button>

                                            
                                            </div>

                                        
                                        </template>

                                </BuseCrud>

                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item
                                label="班组负责人"
                                prop="leaderUserId"
                                :label-width="formLabelWidth"
                            >
                                <el-select 
                                    v-model="form.leaderUserId" 
                                    placeholder="请选择" 
                                    clearable
                                    style="width: 100%;"
                                >
                                    <el-option
                                        v-for="item in table.dataTotal"
                                        :key="item.userId"
                                        :label="item.userName"
                                        :value="item.userId"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>

                </el-form>
              </div>
        </div>

        <div class="bottom-wrap">
            <el-button
                @click="() => handleCancel()"
            >
                取消
            </el-button>
            <el-button
                type="primary"
                @click="() => handleConfirm()"
            >
                确定
            </el-button>
        </div>

        <ChooseMemberModal ref="chooseMemberModal" @confirm="handleMemberConfirm" />
    </div>
    
  </template>
  
  <script>
  import ChooseMemberModal from '../components/chooseMemberModal.vue'

  import{
    addOperationMaintenanceTeam,
    getOperationMaintenanceTeamDetail,
    getDeptList,
    updateOperationMaintenanceTeam,
  } from '@/api/operationMaintenanceConfig/operationMaintenanceTeam'
  
    export default {
    components: {
        ChooseMemberModal
    },
    dicts: [],
    data() {
        return {
            teamId: '', 
            formLabelWidth: '120px',

            deptOptions:[],

            form: {
                teamName: '',
                teamMember: [],
                leaderUserId: '',
            },
            rules: {
                teamName: [
                    { required: true, message: '请输入班组名称', trigger: 'blur' },
                ],
                teamMember: [
                    { 
                        required: true, 
                        validator: (rule, value, callback) => {
                            if(!this.form.teamMember.length ) {
                                callback(new Error('请选择班组人员')); 
                            } else {
                                callback();
                            }
                        },
                        trigger: 'change' 
                    },
                ],
                leaderUserId: [
                    { required: true, message: '请选择班组负责人', trigger: 'change' },
                ],
            },
            table: {
                loading: false,
                page: {
                    total: 0,
                    currentPage: 1,
                    pageSize: 10,
                },
                dataTotal: [],
                data: [],
            },

            pagerProps: {
                layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
            },

            tableColumn:[
                // {
                //     type: 'checkbox',
                //     width: 50,
                //     fixed: 'left',
                // },
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60, // 最小宽度
                },
                {
                    field: 'userName',
                    title: '姓名',
                    minWidth: 220, // 最小宽度
                },
                {
                    field: 'phonenumber',
                    title: '联系方式',
                    minWidth: 220, // 最小宽度
                },
                {
                    field: 'deptId',
                    title: '公司',
                    minWidth: 220, // 最小宽度
                    formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                            this.deptOptions,
                            cellValue
                        );
                    },
                    
                },
                {
                    title: '操作',
                    slots: { default: 'operate' },
                    width: 80,
                    align: 'center',
                    fixed: 'right',
                }
            ]
           
                
        }
    },

    computed: {
        modalConfig() {
            return {
                addBtn: false,
                viewBtn: false,
                menu: false,
                editBtn: false,
                delBtn: false,
            }
        },
    },
    mounted() {
        this.getDeptList();
        const teamId = this.$route.query.teamId;
        this.teamId = teamId;
        if (teamId) {
            this.getDetail();
        }
    },
    methods: {
        // 获取部门
        async getDeptList() {
            const [err, res] = await getDeptList({})
            if (err) return
            const {
                data
            } = res

            const list = []
            data.forEach(item => {
                list.push({
                    label: item.deptName,
                    value: item.deptId
                })
            })
            this.deptOptions = list
        },
        // 获取详情
        async getDetail() {
            
            const [err,res] = await getOperationMaintenanceTeamDetail({
                teamId: this.teamId
            })
            if(err) return

            const {
                teamName,
                leaderUserId,
                userList,
            } = res.data;

            // todo 列表

           

            this.table.dataTotal = userList

            this.table.page.currentPage = 1;
            this.table.page.total = this.table.dataTotal.length;
            this.getTablePage();

           
            this.form = {
                teamName,
                teamMember: userList,
                leaderUserId: Number(leaderUserId),
            }
            

            
        },
       // 添加人员
        handleAdd() {
            this.$refs.chooseMemberModal.deptOptions = this.deptOptions;  
            this.$refs.chooseMemberModal.openModal();
            this.$refs.chooseMemberModal.dialogVisible = true;
        },

        // 添加人员确认
        handleMemberConfirm(stationList) {
            const list = this.table.dataTotal.concat(stationList);

            const uniqueArray = Array.from(new Map(list.map(item => [item.userId, item])).values());

            this.table.dataTotal = uniqueArray;

            this.form.teamMember = uniqueArray;

            this.table.page.currentPage = 1;
            this.table.page.total = this.table.dataTotal.length;
            this.getTablePage();
        },

        getTablePage() {
            this.table.data = this.table.dataTotal.slice(
                (this.table.page.currentPage - 1) * this.table.page.pageSize,
                this.table.page.currentPage * this.table.page.pageSize
            );
        },

        // 删除人员
        handleDelete(row) {
            this.$confirm('确定删除该成员吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    const index = this.table.dataTotal.findIndex(item => item.userId === row.userId);
                    this.table.dataTotal.splice(index, 1);

                    this.form.teamMember = this.table.dataTotal;


                    
                   
                    this.table.page.currentPage = 1;
                    this.table.page.total = this.table.dataTotal.length;

                    this.getTablePage();
                })
                .catch(() => {});
        },

         // 确认
         handleConfirm: _.debounce(async function(){
            this.$refs.baseInfoForm.validate(async (valid) => {
                if (valid) {
                    const {
                        dataTotal
                    } = this.table;

                    const {
                        teamName,
                        teamMember,
                        leaderUserId,
                    } = this.form;

                    const list = []
                    teamMember.forEach(item => {
                        list.push(
                            item.userId,
                        )
                    })

                    const memberUserIds = list.join(',')

                    if(this.teamId) {
                        // 编辑
                        const [err,res] = await updateOperationMaintenanceTeam(
                            {
                                teamId: this.teamId,
                                teamName,
                                memberUserIds,
                                leaderUserId,
                            }
                        )
                        if(err) return
                            
                        this.$message.success('编辑成功')
                    
                        setTimeout(() => {
                            this.$router.back();
                        }, 500);

                    } else {
                        // 新增
                        const [err,res] = await addOperationMaintenanceTeam(
                            {
                                teamName,
                                leaderUserId,
                                memberUserIds,
                            }
                        )
                        if(err) return
                            
                        this.$message.success('新增成功')
                    
                        setTimeout(() => {
                            this.$router.back();
                        }, 500);
                    }

                    
                }
            });
        }, 300)
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }
   
  

  .info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 48px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
    }
    .card-head-text {
        flex:1;
        font-weight: 500;
        font-size: 16px;
        color: #12151A;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
          background-color: #1ab2ff;
          border-color: #1ab2ff;
        }
        ::v-deep .el-button--small {
          font-size: 14px;
        }
      .distribution {
          margin-left: 24px;
          margin-right: 24px;
          display: flex;
          align-items: center;
        }
    }
  }
  .card-head-split {
    width: 100%;
    height: 1px;
    background-color: #E9EBF0;
    margin-bottom: 24px;
  }
  
  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
      padding: 0 16px 16px 16px;
      .custom-header {
          background: -webkit-gradient(linear, left top, left bottom, from(rgba(0, 149, 255, 0.5)), to(rgba(87, 152, 255, 0))), #f5faff;
          background: linear-gradient(180deg, rgba(0, 149, 255, 0.5) 0%, rgba(87, 152, 255, 0) 100%), #f5faff;
          background-repeat: no-repeat;
      }
    }
  }


  .container {
      position: relative;
      padding-bottom: 100px;
      box-sizing: border-box;
      .bottom-wrap {
          position: fixed;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 86px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          background-color: #FFFFFF;
          padding-right: 32px;
          box-sizing: border-box;
      }
  }

  .top-button-wrap {
        display:flex;
        width: 100%;
        height: 100%;
        align-items: center;
        justify-content: flex-end;
        margin-bottom: 8px;
    }

    ::v-deep .bd3001-header {
    justify-content: space-between   !important;
}
 
  </style>
  