<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">基础信息</div>
      </div>

      <div class="form-wrap">
        <el-form
          :model="baseInfo.form"
          :rules="baseInfo.rules"
          ref="baseInfoForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="异常编号"
                prop="exceptId"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.exceptId"
                  placeholder="自动生成"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item
                label="异常名称"
                prop="name"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.name"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item
                label="异常类型"
                prop="type"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.type"
                  placeholder="请选择异常类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dict.type.ls_order_except_type"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="异常等级"
                prop="level"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.level"
                  placeholder="请选择异常等级"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dict.type.ls_order_except_level"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="16">
              <el-form-item
                label="异常描述"
                prop="description"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.description"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="16">
              <el-form-item
                label="校验规则"
                prop="validationRule"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.validationRule.checkItem"
                  placeholder="请选择"
                  style="width: 30%; margin-right: 16px"
                >
                  <el-option
                    v-for="item in dict.type.ls_except_rule_item"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>

                <el-select
                  v-model="baseInfo.form.validationRule.checkMethod"
                  placeholder="请选择"
                  style="width: calc(20% - 32px); margin-right: 16px"
                >
                  <el-option
                    v-for="item in sizeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>

                <el-input
                  style="width: 30%; margin-right: 16px"
                  v-model="baseInfo.form.validationRule.threshold"
                  placeholder="请输入阈值"
                ></el-input>

                <el-select
                  v-model="baseInfo.form.validationRule.unit"
                  placeholder="请选择"
                  style="width: calc(20% - 16px)"
                >
                  <el-option
                    v-for="item in unitList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item
                label="处理措施"
                prop="measures"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.measures"
                  placeholder="请选择处理措施"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dict.type.ls_order_except_measures"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="规则状态"
                prop="status	"
                :label-width="formLabelWidth"
              >
                <el-radio-group v-model="baseInfo.form.status">
                  <el-radio
                    v-for="(item, index) in dict.type.ls_order_except_status"
                    :label="item.value"
                  >
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="备注"
                prop="remark"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="bottom-wrap">
      <div>
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          @click="handleSave"
          :loading="submitLoading"
          type="primary"
          v-if="type === 'edit'"
        >
          保存
        </el-button>
        <el-button
          @click="handleSave"
          :loading="submitLoading"
          type="primary"
          v-if="type === 'create'"
        >
          提交
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { addExrule, getExruleDetail, editExrule } from '@/api/order/index';

export default {
  components: {},
  dicts: [
    'ls_order_except_level', // 异常级别
    'ls_order_except_type', // 异常类型
    'ls_order_except_measures', // 处理措施
    'ls_order_except_status', // 异常状态
    'ls_except_rule_item', // 异常规则
  ],
  data() {
    return {
      type: 'create',
      formLabelWidth: '120px',
      baseInfo: {
        form: {
          exceptId: '自动生成',
          name: '',
          type: '',
          level: '',
          description: '',
          validationRule: {
            checkItem: '',
            checkMethod: '',
            threshold: '',
            unit: '',
          },
          measures: '',
          status: '',
          remark: '',
        },
        rules: {
          exceptId: [
            { required: true, message: '请输入异常编号', trigger: 'blur' },
          ],
          name: [
            { required: true, message: '请输入异常名称', trigger: 'blur' },
          ],
          type: [
            { required: true, message: '请选择异常类型', trigger: 'change' },
          ],
          level: [
            { required: true, message: '请选择异常等级', trigger: 'blur' },
          ],
          description: [
            { required: true, message: '请输入异常描述', trigger: 'blur' },
          ],
          validationRule: [
            { required: true, message: '请填写完整', trigger: 'blur' },
            {
              validator: this.validationRuleMap, // 自定义校验方法
              trigger: 'blur',
            },
          ],
          measures: [
            { required: true, message: '请选择处理措施', trigger: 'change' },
          ],
          status: [
            { required: true, message: '请选择状态', trigger: 'change' },
          ],
        },
      },
      sizeList: [
        { label: '大于', value: '>' },
        { label: '小于', value: '<' },
        { label: '等于', value: '=' },
        { label: '大于等于', value: '>=' },
        { label: '小于等于', value: '<=' },
      ],
      unitList: [
        { label: 'kWh', value: 'kWh' },
        { label: '元', value: '元' },
      ],
      submitLoading: false,
    };
  },

  computed: {},
  mounted() {
    const exceptId = this.$route.query.exceptId;
    if (exceptId) {
      this.type = 'edit';
      this.baseInfo.form.exceptId = exceptId;
      this.getRuleDetail(exceptId);
    }
  },
  methods: {
    // 自定义校验规则校验函数
    validationRuleMap(rule, value, callback) {
      console.log(rule, value, '111');
      if (
        !value?.checkItem ||
        !value?.checkMethod ||
        !value?.threshold ||
        !value?.unit
      ) {
        callback(Error('请填写完整'));
      }
      callback();
    },
    handleCancel() {
      this.$router.back();
    },
    // 获取异常规则详情
    async getRuleDetail(exceptId) {
      const [err, res] = await getExruleDetail({
        exceptId,
      });

      if (err) return;

      const {
        name,
        type,
        level,
        description,
        measures,
        status,
        remark,
        checkItem,
        checkMethod,
        threshold,
        unit,
      } = res.data;

      this.baseInfo.form = {
        exceptId,
        name,
        type,
        level,
        description,
        measures,
        status,
        remark,
        validationRule: {
          checkItem,
          checkMethod,
          threshold,
          unit,
        },
      };
    },

    // 保存
    handleSave() {
      this.$refs.baseInfoForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true;
          const {
            exceptId,
            name,
            type,
            level,
            description,
            validationRule,
            measures,
            status,
            remark,
          } = this.baseInfo.form;

          const params = {
            name,
            type,
            level,
            description,
            measures,
            status,
            remark,
            checkItem: validationRule?.checkItem,
            checkMethod: validationRule?.checkMethod,
            threshold: validationRule?.threshold,
            unit: validationRule?.unit,
          };

          if (this.type === 'create') {
            const [err, res] = await addExrule(params);
            if (err) {
              this.submitLoading = false;
              return this.$message.error(err.message || '新增异常规则失败');
            }
            this.$message({
              type: 'success',
              message: '新增成功!',
            });

            setTimeout(() => {
              this.$router.back();
              this.submitLoading = false;
            }, 2000);
          } else if (this.type === 'edit') {
            const [err, res] = await editExrule({
              ...params,
              exceptId,
            });
            if (err) {
              this.submitLoading = false;
              return this.$message.error(err.message || '编辑异常规则失败');
            }
            this.$message({
              type: 'success',
              message: '编辑成功!',
            });

            setTimeout(() => {
              this.$router.back();
              this.submitLoading = false;
            }, 2000);
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
        background-color: #1ab2ff;
        border-color: #1ab2ff;
      }
      ::v-deep .el-button--small {
        font-size: 14px;
      }
      .distribution {
        margin-left: 24px;
        margin-right: 24px;
        display: flex;
        align-items: center;
      }
    }
  }

  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
    padding: 0 16px 16px 16px;
    .custom-header {
      background: -webkit-gradient(
          linear,
          left top,
          left bottom,
          from(rgba(0, 149, 255, 0.5)),
          to(rgba(87, 152, 255, 0))
        ),
        #f5faff;
      background: linear-gradient(
          180deg,
          rgba(0, 149, 255, 0.5) 0%,
          rgba(87, 152, 255, 0) 100%
        ),
        #f5faff;
      background-repeat: no-repeat;
    }
  }
}

.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .bottom-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
  }
}
</style>
