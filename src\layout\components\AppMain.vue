<template>
  <section
    class="app-main"
    :class="{
      'app-main-close': !sidebar.opened,
      'app-main-hide': device === 'mobile',
    }"
  >
    <tags-view v-if="!isInMicroEnv && needTagsView && !isScreen" />
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view
          :class="
            !isInMicroEnv && needTagsView && !isScreen
              ? 'router-container'
              : 'router-container-micro'
          "
          v-if="!$route.meta.link"
          :key="key"
        />
      </keep-alive>
    </transition>
    <iframe-toggle />
  </section>
</template>

<script>
import iframeToggle from './IframeToggle/index';
import TagsView from './TagsView/index';
import { mapState } from 'vuex';

export default {
  name: 'AppMain',
  components: { iframeToggle, TagsView },
  props: {
    isScreen: Boolean,
  },
  computed: {
    ...mapState({
      needTagsView: (state) => state.settings.tagsView,
      sidebar: (state) => state.app.sidebar,
      device: (state) => state.app.device,
    }),
    cachedViews() {
      return this.$store.state.tagsView.cachedViews;
    },
    key() {
      return this.$route.path;
    },
    isInMicroEnv() {
      return window.__MICRO_APP_ENVIRONMENT__;
    },
  },
  watch: {
    $route() {
      this.addIframe();
    },
  },
  mounted() {
    this.addIframe();
  },
  methods: {
    addIframe() {
      const { name } = this.$route;
      if (name && this.$route.meta.link) {
        this.$store.dispatch('tagsView/addIframeView', this.$route);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
  // .router-container {
  //   height: calc(100% - 0.4rem);
  //   overflow: auto;
  // }
  // .router-container-micro {
  //   height: 100% !important;
  // }
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 6px;
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background-color: #c0c0c0;
  border-radius: 3px;
}
</style>
