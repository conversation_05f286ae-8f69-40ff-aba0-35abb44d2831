import request from '@/utils/request';

const baseUrl = '/vehicle-charging-admin';

// 清分汇总分页
export function clearInfoPage(data) {
  return request({
    url: baseUrl + '/clearInfo/page',
    method: 'post',
    data: data,
  });
}

// 清分汇总导出
export function clearInfoExport(data) {
  return request({
    url: baseUrl + '/clearInfo/export',
    method: 'post',
    data: data,
  });
}

// 清分汇总详情
export function clearInfoDetail(data) {
  return request({
    url: baseUrl + '/clearInfo/detail',
    method: 'post',
    data: data,
  });
}

// 清分明细分页
export function clearDetailPage(data) {
  return request({
    url: baseUrl + '/clearDetail/page',
    method: 'post',
    data: data,
  });
}

// 清分明细详情
export function clearDetailInfo(data) {
  return request({
    url: baseUrl + '/clearDetail/detail',
    method: 'post',
    data: data,
  });
}

// 清分明细修改
export function clearDetailUpdate(data) {
  return request({
    url: baseUrl + '/clearDetail/update',
    method: 'post',
    data: data,
  });
}

// 清分明细导出
export function clearDetailExport(data) {
  return request({
    url: baseUrl + '/clearDetail/export',
    method: 'post',
    data: data,
  });
}

// 清分明细历史记录分页
export function clearDetailChangePage(data) {
  return request({
    url: baseUrl + '/clearDetailChange/page',
    method: 'post',
    data: data,
  });
}

// 问题汇总分页
export function clearProblemFeedbackPage(data) {
  return request({
    url: baseUrl + '/clearProblemFeedback/page',
    method: 'post',
    data: data,
  });
}

// 问题反馈详情查询（未分页）
export function clearProblemFeedbackDetailPage(data) {
  return request({
    url: baseUrl + '/clearProblemFeedbackDetail/page',
    method: 'post',
    data: data,
  });
}

// 问题反馈插入列表
export function clearProblemFeedbackDetailInsert(data) {
  return request({
    url: baseUrl + '/clearProblemFeedbackDetail/insert',
    method: 'post',
    data: data,
  });
}

// 问题反馈核查状态修改
export function clearProblemFeedbackCheck(data) {
  return request({
    url: baseUrl + '/clearProblemFeedback/check',
    method: 'post',
    data: data,
  });
}

// 清分汇总修改保存
export function clearInfoUpdate(data) {
  return request({
    url: baseUrl + '/clearInfo/update',
    method: 'post',
    data: data,
  });
}

// 清分汇总结算
export function clearInfoSettle(data) {
  return request({
    url: baseUrl + '/clearInfo/settle',
    method: 'post',
    data: data,
  });
}
