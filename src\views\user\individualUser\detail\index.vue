<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
        <div class="device-head">
            <img v-if="avatar" :src="avatar" class="device-head-icon">
            <img v-else src="@/assets/user/user-detail-icon.png" class="device-head-icon">
            
            <div class="device-info-wrap">
                <div class="device-title-wrap">
                    <div class="device-title">用户昵称：{{ nickName }}</div>
                </div>
                <div class="device-info-wrap">
                    <el-row>
                        <el-col :span="8">
                            <span class="label">UID：</span>
                            <span class="value">{{uid}}</span>
                        </el-col>
                        <!-- <el-col :span="8">
                            <span class="label">性别：</span>
                            <span class="value">{{gender}}</span>
                        </el-col> -->
                        
                    </el-row>
                </div>
            </div>
            <div class="device-status-wrap">
                    <div class="device-status-item-wrap">
                        <div class="device-status-item-title">账号状态</div>
                        <div class="device-status">{{  accountStatus}}</div>
                    </div>


            </div>

            <el-button class="button-class" type="primary" @click="onClickBack">
                返回
            </el-button>

        </div>

        <div class="info-wrap" >
            <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="用户资料" name="userProfile" style="padding-bottom: 24px;">
                    <div class="info-item">
                        <div class="info-item-head">
                            <div class="info-item-before-icon"></div>
                            <div class="info-item-head-text">账号信息</div>
                        </div>

                        <div class="form-wrap">
                            <el-row :gutter="20">
                                <el-col :span="8" v-for="(item, key) in baseInfo" :key="key" style="margin-bottom: 24px;">
                                    <div style="display: flex; align-items: center;" v-if="key === 'mobile'">
                                        <div class="info-title">{{labels[key]}}：</div>
                                        <div class="info-detail">{{ isShowMobile ? realMobile :  item }}</div>
                                        <div class="info-look" @click="onClickMobile">查看{{ labels[key] }}</div>
                                    </div>
                                    <div style="display: flex;align-items: center;" v-else>
                                        <div class="info-title">{{labels[key]}}：</div>
                                        <div class="info-detail">{{ item }}</div>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-item-head">
                            <div class="info-item-before-icon"></div>
                            <div class="info-item-head-text">渠道账号信息</div>
                        </div>

                        <div class="form-wrap">
                            <BuseCrud
                                style="margin-bottom: 24px;"
                                ref="channelInfo"
                                :tableColumn="channelInfoTableColumn"
                                :tableData="channelInfoData"
                                :modalConfig="{ addBtn: false, menu: false }"
                            >
                                
                            </BuseCrud>
                        </div>
                    </div>
                </el-tab-pane>

                <!-- <el-tab-pane label="钱包余额" name="walletBalance">
                    <div class="wallet-wrap">
                        <div class="wallet-title-wrap">
                            <div class="wallet-title-item">
                                <img src="@/assets/user/wallet-balance-icon.png" class="wallet-title-icon">
                                <div>
                                    <div class="wallet-title">账户余额</div>
                                    <div class="wallet-info">
                                        {{ balance }}
                                        <block class="wallet-unit">元</block>
                                    </div>
                                </div>
                            </div>
                            <div class="wallet-title-item">
                                <img src="@/assets/user/wallet-freeze-icon.png" class="wallet-title-icon">
                                <div>
                                    <div class="wallet-title">冻结余额</div>
                                    <div class="wallet-info">
                                        {{ freeze }}
                                        <block class="wallet-unit">元</block>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <BuseCrud
                            ref="balance"
                            :loading="balanceLoading"
                            :filterOptions="balanceFilterOptions"
                            :tablePage="balanceTablePage"
                            :tableColumn="balanceTableColumn"
                            :tableData="balanceTableData"
                            :pagerProps="pagerProps"
                            :modalConfig="modalConfig"
                            @loadData="loadbalanceData"
                        >

                        </BuseCrud>
                    </div>

                    
                </el-tab-pane>

                <el-tab-pane label="充电包" name="chargePackage">
                    <div class="charge-wrap">
                        <div class="charge-title-wrap">
                            <div class="charge-title-item">
                                <img src="@/assets/user/wallet-balance-icon.png" class="wallet-title-icon">
                                <div>
                                    <div class="wallet-title">账户余额</div>
                                    <div class="charge-info">
                                        <div>
                                            {{ chargeBalance }}
                                            <block class="charge-unit">元</block>
                                        </div>
                                       
                                    </div>
                                </div>
                            </div>
                            <div class="charge-title-item">
                                <div class="status-use">使用中</div>
                                <img src="@/assets/user/wallet-freeze-icon.png" class="wallet-title-icon">
                                <div style="flex: 1;">
                                    <div class="wallet-title">卡1：{{ card1 }}</div>
                                    <div class="charge-info">
                                        <div>
                                            {{ card1Balance }}
                                            <block class="charge-unit">元</block>
                                        </div>

                                        <div class="charge-bind-time">
                                            绑定时间：{{ card1BindTime }}
                                        </div>
                                       
                                    </div>
                                </div>
                            </div>

                            <div class="charge-title-item-last">
                                <div class="status-use-stop">暂停使用</div>
                                <img src="@/assets/user/wallet-freeze-icon.png" class="wallet-title-icon">
                                <div style="flex: 1;">
                                    <div class="wallet-title">卡2：{{ card2 }}</div>
                                    <div class="charge-info">
                                        <div>
                                            {{ card2Balance }}
                                            <block class="charge-unit">元</block>
                                        </div>
                                        <div class="charge-bind-time">
                                            绑定时间：{{ card2BindTime }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <BuseCrud
                            ref="charge"
                            :loading="chargeLoading"
                            :filterOptions="chargeFilterOptions"
                            :tablePage="chargeTablePage"
                            :tableColumn="chargeTableColumn"
                            :tableData="chargeTableData"
                            :pagerProps="pagerProps"
                            :modalConfig="modalConfig"
                            @loadData="loadChargeData"
                        >

                        </BuseCrud>

                    </div>
                </el-tab-pane> -->
            </el-tabs>
        </div>
    </div>
    
  </template>
  
  <script>


import {getUserDetail, getUserPhone} from '@/api/user/index';

  
    export default {
    components: {
        
    },
    dicts: [
        'ls_charging_gender',
        'ls_charging_register_channel',
        'ls_charging_user_status',
    ],
    data() {
        return {
            userId: '', // 用户id 查询接口用
            nickName: '',
            uid: '',
            gender: '',
            accountStatus: '',

            activeName: 'userProfile',

            baseInfo: {
                mobile: '',
                // password: '',
                // email: '',
                channel: '',
                registerTime: '',
            },

            realMobile: '', // 真实手机号

            labels: {
                mobile: '手机号',
                // password: '登陆密码',
                // email: '联系邮箱',
                channel: '注册渠道',
                registerTime: '注册时间',
            },

            isShowMobile: false, // 是否显示手机号
            isShowPassword: false, // 是否显示密码


            channelInfoTableColumn: [
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60,
                    fixed: 'left',
                    align: 'center'
                },
                {
                    field: 'channelName',
                    title: '渠道',
                    minWidth: 180,
                },
                {
                    field: 'channelUserId',
                    title: 'Openid',
                    minWidth: 180,
                },
                {
                    field: 'status',
                    title: '状态',
                    minWidth: 180,
                },
                {
                    field: 'createTime',
                    title: '绑定时间',
                    minWidth: 200,
                }
            ],

            channelInfoData: [],


            balance: '91222.00',
            freeze: '10.92',

            balanceLoading: false,
            balanceParams: {
                transactionSerialNo:'',
                orderId: '',
                transactionTime: [],
                transactionType: '',   
            },
            balanceTablePage: { total: 0, currentPage: 1, pageSize: 10 },
            balanceTableColumn: [
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60,
                    fixed: 'left',
                },
                {
                    field: 'transactionSerialNo',
                    title: '交易流水号',
                    minWidth: 160,
                },
                {
                    field: 'orderId',
                    title: '业务订单号',
                    minWidth: 100,
                },
                {
                    field: 'transactionTime',
                    title: '交易时间',
                    minWidth: 180,
                },
                {
                    field: 'transactionType',
                    title: '交易类型',
                    minWidth: 120,
                },
                {
                    field: 'amount',
                    title: '交易金额（元）',
                    minWidth: 150,
                },
                {
                    field: 'balance',
                    title: '交易后余额（元）',
                    minWidth: 170,
                }

            ],
            balanceTableData: [
                {
                    transactionSerialNo: 'TR202411230001',
                    orderId: 'ORD202411230001',
                    transactionTime: '2024/11/23 11:20:02',
                    transactionType: '充值',
                    amount: 100.00,
                    balance: 100.00
                },
                {
                    transactionSerialNo: 'TR202411230002',
                    orderId: 'ORD202411230002',
                    transactionTime: '2024/11/23 11:25:15',
                    transactionType: '消费',
                    amount: -50.00,
                    balance: 50.00
                },
                {
                    transactionSerialNo: 'TR202411230003',
                    orderId: 'ORD202411230003',
                    transactionTime: '2024/11/23 14:30:45',
                    transactionType: '提现',
                    amount: -30.00,
                    balance: 20.00
                },
                {
                    transactionSerialNo: 'TR202411230004',
                    orderId: 'ORD202411230004',
                    transactionTime: '2024/11/23 16:45:30',
                    transactionType: '退款',
                    amount: 20.00,
                    balance: 40.00
                },
                {
                    transactionSerialNo: 'TR202411230005',
                    orderId: 'ORD202411230005',
                    transactionTime: '2024/11/23 18:15:20',
                    transactionType: '消费',
                    amount: -15.00,
                    balance: 25.00
                },
                {
                    transactionSerialNo: 'TR202411230006',
                    orderId: 'ORD202411230006',
                    transactionTime: '2024/11/23 20:30:10',
                    transactionType: '充值',
                    amount: 100.00,
                    balance: 125.00
                },
                {
                    transactionSerialNo: 'TR202411230007',
                    orderId: 'ORD202411230007',
                    transactionTime: '2024/11/23 22:45:00',
                    transactionType: '消费',
                    amount: -25.00,
                    balance: 100.00
                }
            ],


            chargeBalance: '21023.11',
            card1: '1231231',
            card2: '1231232',
            card1Balance: '100.00',
            card2Balance: '100.00',
            card1BindTime: '2024/11/23 18:15:20',
            card2BindTime: '2024/11/24 18:15:20',

            chargeLoading: false,
            chargeParams: {
                transactionSerialNo:'',
                orderId: '',
                transactionTime: [],
                transactionType: '',   
            },
            chargeTablePage: { total: 0, currentPage: 1, pageSize: 10 },
            chargeTableColumn: [
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60,
                    fixed: 'left',
                },
                {
                    field: 'transactionSerialNo',
                    title: '交易流水号',
                    minWidth: 160,
                },
                {
                    field: 'orderId',
                    title: '业务订单号',
                    minWidth: 100,
                },
                {
                    field: 'transactionTime',
                    title: '交易时间',
                    minWidth: 180,
                },
                {
                    field: 'transactionType',
                    title: '交易类型',
                    minWidth: 120,
                },
                {
                    field: 'amount',
                    title: '交易金额（元）',
                    minWidth: 150,
                },
                {
                    field: 'chargeNo',
                    title: '充电包卡号',
                    minWidth: 150,
                },
                {
                    field: 'beforeBalance',
                    title: '交易前余额（元）',
                    minWidth: 170,
                },
                {
                    field: 'balance',
                    title: '交易后余额（元）',
                    minWidth: 170,
                }

            ],
            chargeTableData: [
                {
                    transactionSerialNo: 'TR202411230001',
                    orderId: 'ORD202411230001',
                    transactionTime: '2024/11/23 11:20:02',
                    transactionType: '充值',
                    amount: 100.00,
                    chargeNo: 'CHG202411230001',
                    beforeBalance: 0.00,
                    balance: 100.00
                },
                {
                    transactionSerialNo: 'TR202411230002',
                    orderId: 'ORD202411230002',
                    transactionTime: '2024/11/23 11:25:15',
                    transactionType: '消费',
                    amount: -50.00,
                    chargeNo: 'CHG202411230002',
                    beforeBalance: 1.00,
                    balance: 50.00
                },
                {
                    transactionSerialNo: 'TR202411230003',
                    orderId: 'ORD202411230003',
                    transactionTime: '2024/11/23 14:30:45',
                    transactionType: '提现',
                    amount: -30.00,
                    chargeNo: 'CHG202411230003',
                    beforeBalance: 4.00,
                    balance: 20.00
                },
                {
                    transactionSerialNo: 'TR202411230004',
                    orderId: 'ORD202411230004',
                    transactionTime: '2024/11/23 16:45:30',
                    transactionType: '退款',
                    amount: 20.00,
                    balance: 40.00
                },
                {
                    transactionSerialNo: 'TR202411230005',
                    orderId: 'ORD202411230005',
                    transactionTime: '2024/11/23 18:15:20',
                    transactionType: '消费',
                    amount: -15.00,
                    chargeNo: 'CHG202411230004',
                    beforeBalance: 4.00,
                    balance: 25.00
                },
                {
                    transactionSerialNo: 'TR202411230006',
                    orderId: 'ORD202411230006',
                    transactionTime: '2024/11/23 20:30:10',
                    transactionType: '充值',
                    amount: 100.00,
                    chargeNo: 'CHG202411230005',
                    beforeBalance: 5.00,
                    balance: 125.00
                },
                {
                    transactionSerialNo: 'TR202411230007',
                    orderId: 'ORD202411230007',
                    transactionTime: '2024/11/23 22:45:00',
                    transactionType: '消费',
                    amount: -25.00,
                    chargeNo: 'CHG202411230006',
                    beforeBalance: 12.00,
                    balance: 100.00
                }
            ],


        };
    },

    computed: {
        balanceFilterOptions() {
            return {
                config: [
                    {
                        field: 'transactionSerialNo',
                        title: '交易流水号',
                        element: 'el-input',
                    },
                    {
                        field: 'orderId',
                        title: '业务订单号',
                        element: 'el-input',
                    },
                    {
                        field: 'transactionTime',
                        title: '交易日期',
                        element: 'el-date-picker',
                        props: {
                            type: 'daterange',
                            options: [],
                            valueFormat: 'yyyy-MM-dd',
                        }
                    },
                    {
                        field: 'transactionType',
                        title: '交易类型',
                        element: 'el-select',
                        props: {
                            placeholder: '请选择交易类型',
                            options: [
                                { label: '线上', value: 0 },
                                { label: '线下', value: 1 },
                            ]  
                        }
                    },


                ],
                params: this.balanceParams,
            }
        },
        chargeFilterOptions() {
            return {
                config: [
                    {
                        field: 'transactionSerialNo',
                        title: '交易流水号',
                        element: 'el-input',
                    },
                    {
                        field: 'orderId',
                        title: '业务订单号',
                        element: 'el-input',
                    },
                    {
                        field: 'transactionTime',
                        title: '交易日期',
                        element: 'el-date-picker',
                        props: {
                            type: 'daterange',
                            options: [],
                            valueFormat: 'yyyy-MM-dd',
                        }
                    },
                    {
                        field: 'transactionType',
                        title: '交易类型',
                        element: 'el-select',
                        props: {
                            placeholder: '请选择交易类型',
                            options: [
                                { label: '充值', value: 0 },
                                { label: '消费', value: 1 },
                                { label: '提现', value: 2 },
                                { label: '退款', value: 3 },
                            ]  
                        }
                    },


                ],
                params: this.chargeParams,
            }
        },
        modalConfig() {
            return {
                addBtn: false,
                viewBtn: false,
                menu: false,
                editBtn: false,
                delBtn: false,
            }
        },
    },
    beforeMount() {
        this.userId = this.$route.query.userId;
       
    },
    mounted() {
        this.getUserDetail()
    },
    methods: {
        // 获取用户详情
        async getUserDetail() {
            const [err, res] = await getUserDetail({
                userId: this.userId,
            });

            if (err) {
                return this.$message.error(err.message);
            }
            const { data } = res;

            // const data = { 
            //     identityCard: '******************',
            //     nickName: '小王',
            //     gender: '男',
            //     status: '注册',
            //     mobile: '138****000',
            //     password: '******',
            //     email: '<EMAIL>',
            //     registerChannel: 'APP',
            //     registerTime: '2021-12-12 12:12:12',
            //     channelUserInfoList: [
            //         {
            //             channelName: 'APP',
            //             channelUserId: '123123123',
            //             status: '已绑定',
            //             createTime: '2021-12-12 12:12:12',
            //         },
            //         {
            //             channelName: '微信小程序',
            //             channelUserId: '*********',
            //             status: '已绑定',
            //             createTime: '2024-12-12 12:12:12',
            //         },
            //     ]
            // }


            

            this.avatar = data.avatar;

            this.nickName = data.nickName;
            this.gender = this.selectDictLabel(
                    this.dict.type.ls_charging_gender,
                    data.gender
                  );
            this.uid = data.identityCard;
            this.accountStatus = this.selectDictLabel(
                            this.dict.type.ls_charging_user_status,
                            data.status
                        );

            this.baseInfo = {
                mobile: data.mobile,
                // password: data.password,
                // email: data.email,
                channel:this.selectDictLabel(
                            this.dict.type.ls_charging_register_channel,
                            data.registerChannel
                        ),
                registerTime: data.registerTime,
            }

            this.channelInfoData = data.channelUserInfoList;





        },
        // tab 切换
        handleClick({index}) {
            console.log(Number(index) === 0,'index')
        },

        // desensitizedPhone(phone) {
        //     if (typeof phone === 'string' && phone.length === 11) {
        //         return phone.slice(0, 3) + '****' + phone.slice(-4);
        //     }
        //     return phone;
        // },

        // 查看手机号
        async onClickMobile() {
            const [err, res] = await getUserPhone({
                userId: this.userId,
            });

            if (err) return this.$message.error(err.message || '操作失败');

            this.realMobile = res.data;
            // this.realMobile = '12345678901';

            this.isShowMobile = true;
            setTimeout(() => {
                this.isShowMobile = false;
            }, 3000);
        },

        onClickBack() {
            this.$router.back();
        },

        // desensitizedPassword(password) {
        //     if (typeof password === 'string') {
        //         return '*'.repeat(password.length);
        //     }
        //     return password;
        // },

        // // 查看密码
        // onClickPassword() {
        //     this.isShowPassword = true;
        //     setTimeout(() => {
        //         this.isShowPassword = false;
        //     }, 3000);
        // },
        
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }

  .device-head {
    background-color: #fff;
    
    display: flex;
    height: 148px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    box-sizing: border-box;
    .device-head-icon {
        width: 100px;
        height: 100px;
        margin-right: 20px;
        border-radius: 50%;
    }
    .device-info-wrap {
        flex: 1;
        .device-title-wrap {
            height: 32px;
            display: flex;
            align-items: center;
            .device-title {
                font-weight: 500;
                font-size: 24px;
                color: #12151A;
            }
        }
        .device-info-wrap {
            height: 16px;
            margin-top: 16px;
            font-size: 16px;
            font-weight: 400;
            color: #292B33;
        }
    }
    .device-status-wrap {
        display: flex;
        align-items: center;
        .device-status-item-wrap {
            width: 150px;
            .device-status-item-title {
                font-weight: 400;
                font-size: 14px;
                line-height: 14px;
                color: #505363;
                margin: 0 auto 12px auto;
                text-align: center;
            }
            .device-status {
                width: 86px;
                height: 34px;
                border-radius: 4px;
                display: flex;
                margin: 0 auto;
                align-items: center;
                justify-content: center;
                background-color: #EBF3FF;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 18px;
                color: #217AFF;
            }

        }

    }
    
  
  }

  .info-wrap {
    margin: 16px;
    border-radius: 5px;
    border: 1px solid #fff;
    overflow: hidden;
    background-color: #fff;
    .info-item {
        margin: 16px 16px 0 16px;
        border-radius: 5px;
        border: 1px solid #E9EBF0;
        .info-item-head {
            height: 56px;
            padding: 0 16px;
            display: flex;
            align-items: center;
            .info-item-before-icon {
                width: 3px;
                height: 16px;
                background-image: url('~@/assets/station/consno-before.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                margin-right: 8px;
            }
            .info-item-head-text {
                flex:1;
                font-weight: 500;
                font-size: 16px;
                color: #12151A;

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: -3px; /* 调整这个值来改变边框的宽度 */
                    width: 0;
                    border-top: 3px solid transparent;
                    border-bottom: 3px solid transparent;
                    border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
                }        
            }

        }
        .form-wrap {
            padding: 0 24px 0 24px;
            .form-title-wrap {
                margin: 8px 0 16px 0;
                height: 32px;
                display: flex;
                align-items: center;
                .form-title-blue {
                    line-height: 32px;
                    border-radius: 2px;
                    padding: 0 10px;
                    background-color: #EBF3FF;
                    color:#217AFF;
                    font-size: 16px;
                    font-weight: 400;
                    margin-right: 16px;
                }
                .form-title {
                    line-height: 32px;
                    border-radius: 2px;
                    padding: 0 10px;
                    background-color: #F9F9FB;
                    color:#292B33;
                    font-size: 16px;
                    font-weight: 400;
                    margin-right: 16px;
                }
            }
        }
        .info-look {
            color: #217AFF;
            margin-left: 16px;
        }
        
    }

    .chart-wrap {
        padding: 0 24px 24px 24px;
        display: flex;
        .chart-item-left-wrap {
            margin: 16px 8px 0 0;
            border-radius: 5px;
            border: 1px solid #E9EBF0;
            width: calc(50% - 8px);
            // height: 734px;
        }
        .chart-item-right-wrap {
            margin: 16px 0 0 8px;
            border-radius: 5px;
            border: 1px solid #E9EBF0;
            width: calc(50% - 8px);
            // height: 734px;
        }

        .info-item-head {
            height: 56px;
            padding: 0 16px;
            display: flex;
            align-items: center;
            .info-item-before-icon {
                width: 3px;
                height: 16px;
                background-image: url('~@/assets/station/consno-before.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                margin-right: 8px;
            }
            .info-item-head-text {
                flex:1;
                font-weight: 500;
                font-size: 16px;
                color: #12151A;

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: -3px; /* 调整这个值来改变边框的宽度 */
                    width: 0;
                    border-top: 3px solid transparent;
                    border-bottom: 3px solid transparent;
                    border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
                }        
            }

        }
    }


  ::v-deep  .bd3001-content{
    padding: 0 !important;
  }
  }
 
  .wallet-wrap {
    padding: 16px;
    box-sizing: border-box;
    .wallet-title-wrap {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .wallet-title-item {
            width: calc(50% - 8px);
            height: 88px;
            display: flex;
            align-items: center;
            .wallet-title-icon {
                width: 48px;
                height: 48px;
                margin-right: 24px;
            }
            .wallet-title {
                font-weight: 400;
                font-size: 16px;
                line-height: 16px;
                color: #292B33;
                margin-bottom: 16px;
            }
            .wallet-info {
                font-weight: 500;
                font-size: 24px;
                line-height: 24px;
                letter-spacing: 0px;
                color: #12151A;
            }
            .wallet-unit {
                font-weight: 400;
                font-size: 14px;
                color: #505363;
                // margin-top: 6px;
            }
        }
    }
  }

  .charge-wrap {
    padding: 16px;
    box-sizing: border-box;
    .charge-title-wrap {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        .charge-title-item {
            width: calc(33% - 10px);
            height: 88px;
            display: flex;
            align-items: center;
            margin-right: 16px;
            background: linear-gradient(180deg, #EBF3FF 0%, rgba(235, 243, 255, 0) 100%);

            padding: 0 24px;
            box-sizing: border-box;
            position: relative;
            .status-use {
                position: absolute;
                top: 0;
                right: 0;
                width: 74px;
                height: 24px;
                background-color: #217AFF;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                color: #fff;
                border-radius:  0 0 0 12px ;
            }
            .wallet-title-icon {
                width: 48px;
                height: 48px;
                margin-right: 24px;
            }
            .wallet-title {
                font-weight: 400;
                font-size: 16px;
                line-height: 16px;
                color: #292B33;
                margin-bottom: 16px;
            }
            .charge-info {
                font-weight: 500;
                font-size: 24px;
                line-height: 24px;
                letter-spacing: 0px;
                color: #12151A;
                display: flex;
                align-items: center;
                justify-content: space-between;
                .charge-unit {
                    font-weight: 400;
                    font-size: 14px;
                    color: #505363;
                    // margin-bottom: 6px;
                }
                .charge-bind-time {
                    color: #292B33;
                    font-weight: 400;
                    font-size: 16px;
                    text-align: right;
                }
            }
            
        }

        .charge-title-item-last {
            flex: 1;
            height: 88px;
            display: flex;
            align-items: center;
            // margin-right: 16px;
            background: linear-gradient(180deg, #EBF3FF 0%, rgba(235, 243, 255, 0) 100%);

            padding: 0 24px;
            box-sizing: border-box;
            position: relative;
            .status-use-stop {
                position: absolute;
                top: 0;
                right: 0;
                width: 88px;
                height: 24px;
                background-color: #818496;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                color: #fff;
                border-radius:  0 0 0 12px ;
            }
            .wallet-title-icon {
                width: 48px;
                height: 48px;
                margin-right: 24px;
            }
            .wallet-title {
                font-weight: 400;
                font-size: 16px;
                line-height: 16px;
                color: #292B33;
                margin-bottom: 16px;
            }
            .charge-info {
                font-weight: 500;
                font-size: 24px;
                line-height: 24px;
                letter-spacing: 0px;
                color: #12151A;
                display: flex;
                align-items: center;
                justify-content: space-between;
                .charge-unit {
                    font-weight: 400;
                    font-size: 14px;
                    color: #505363;
                    // margin-top: 6px;
                }
                .charge-bind-time {
                    color: #292B33;
                    font-weight: 400;
                    font-size: 16px;
                    text-align: right;
                }
            }
        }
    }
  }
  </style>
  