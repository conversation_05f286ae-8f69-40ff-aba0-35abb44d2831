<template>
    <div class="container container-float ">
      <div class="table-wrap">
            <BuseCrud
                ref="crud"
                :loading="loading"
                :filterOptions="filterOptions"
                :tablePage="tablePage"
                :tableColumn="tableColumn"
                :tableData="tableData"
                :pagerProps="pagerProps"
                :modalConfig="modalConfig"
                  class="buse-wrap-station"
                @loadData="loadData"
            >
            <template slot="defaultHeader">
                <div>
                    <div class="card-head">
                        <div class="card-head-text">优惠券列表</div>

                        <div class="top-button-wrap">
                          <el-button
                              type="primary"
                              class="set-btn"
                              @click="handleExport"
                          >
                              <svg-icon iconClass="a-export-black"></svg-icon>
                            
                              批量导出
                          </el-button>

                          <el-button
                              type="primary"
                              @click="handleAdd"
                          >
                              <svg-icon iconClass="a-add"></svg-icon>
                              新增优惠券
                          </el-button>

                        </div>
                    </div>
                    

                    
                </div>
                
            </template>

                <template slot="operate" slot-scope="{ row }">
                    <div class="menu-box">
                        <el-button
                            class="button-border"
                            @click="hanleDetail(row)"
                        >
                            详情
                        </el-button>

                        <el-button
                            v-if="row.generateType === 'BG'"
                            class="button-border"
                            @click="handelAdjustInventory(row)"
                        >
                            调整库存
                        </el-button>

                        <el-button
                            v-if="row.reviewStatus === 'WAITING_APPROVAL'"
                            class="button-border"
                            @click="handleAudit(row)"
                        >
                            审核
                        </el-button>

                        
                        <el-button
                            v-if="row.couponStatus === 'DISABLE'"
                            class="button-border"
                            @click="handleStatus(row,'ENABLE')"
                        >
                            启用
                        </el-button>

                        <el-button
                          v-if="row.couponStatus === 'ENABLE'"
                            class="button-border"
                            @click="handleStatus(row,'DISABLE')"
                        >
                            停用
                        </el-button>

                        <el-button
                            v-if="row.couponStatus === 'DISABLE'"
                            class="button-border"
                            @click="handleDelete(row)"
                        >
                            删除
                        </el-button>



                    
                    </div>
                
                </template>

            </BuseCrud>
        </div>
      

        <AdjustInventoryModal ref="adjustInventoryModal"  @loadData="loadData"/>
    </div>
    
  </template>
  
  <script>

  import AdjustInventoryModal from './components/adjustInventoryModal.vue'
import StatusInfo from '@/components/Business/StatusInfo';
import StatusDot from '@/components/Business/StatusDot';



  import {
    getCouponList,
    changeCouponStatus,
    deleteCoupon,
  } from '@/api/operator/coupon'
  
    export default {
    components: {
      AdjustInventoryModal,
      StatusInfo,
      StatusDot
    },
    dicts: [
      'ls_charging_audit_type',
    ],
    data() {
      return {
        loading: false,
        tablePage: { total: 0, currentPage: 1, pageSize: 10 },
        tableColumn: [
          {
            type: 'seq',
            title: '序号',
            width: 60,
            minWidth: 60,
          },
          {
            field: 'couponName',
            title: '优惠券名称',
            minWidth: 180,
          },
          {
            field: 'couponNo',
            title: '优惠券编码',
            minWidth: 180,
          },
          {
            field: 'displayTitle',
            title: '展示标题',
            minWidth: 180,
          },
          {
            field: 'couponBizType',
            title: '券业务类型',
            minWidth: 180,
            formatter: ({ cellValue }) => {
              return this.selectDictLabel(
                this.couponBusinessTypeList,
                cellValue
              );
            },
          },
          {
            field: 'couponType',
            title: '券基本分类',
            minWidth: 180,
            formatter: ({ cellValue }) => {
              return this.selectDictLabel(
                this.basicTypeList,
                cellValue
              );
            },
          },
          {
            field: 'discountDesc',
            title: '优惠方式',
            minWidth: 180,
            slots: {
                  default: ({ row }) => [
                      <el-tooltip 
                          content={row.discountDesc} 
                          placement="top" 
                          disabled={!row.discountDesc || row.discountDesc.length < 10}
                      >
                          <span class="ellipsis-text">{row.discountDesc}</span>
                      </el-tooltip>
                  ]
              }
          },
          {
            field: 'generateType',
            title: '生成方式',
            minWidth: 180,
            formatter: ({ cellValue }) => {
              return this.selectDictLabel(
                this.generationMethodList,
                cellValue
              );
            },
          },
          {
            field: 'undertaker',
            title: '动态成本单位',
            minWidth: 180,
          },
          {
            field: 'totalNum',
            title: '优惠券总量',
            minWidth: 180,
          },
          {
            field: 'residueNum',
            title: '可用库存',
            minWidth: 180,
          },
          {
            field: 'periodDesc',
            title: '有效期',
            minWidth: 180,
            slots: {
                  default: ({ row }) => [
                      <el-tooltip 
                          content={row.periodDesc} 
                          placement="top" 
                          disabled={!row.periodDesc || row.periodDesc.length < 10}
                      >
                          <span class="ellipsis-text">{row.periodDesc}</span>
                      </el-tooltip>
                  ]
              }
          },
         
          {
            field: 'createUser',
            title: '创建人',
            minWidth: 180,
          },
          {
            field: 'createTime',
            title: '创建时间',
            minWidth: 180,
          },
          {
            field: 'couponStatus',
            title: '券生效状态',
            minWidth: 180,
            fixed: 'right',
            slots: {
                    // 自定义render函数
                    default: ({ row }) => {
                        return (
                            <StatusInfo
                                value={row.couponStatus}
                                dictValue={this.couponStatusList}
                                colors={[ 'low','medium' ]}
                            ></StatusInfo>
                            );
                        },
            },

          },
          {
            field: 'reviewStatus',
            title: '审核状态',
            minWidth: 180,
            fixed: 'right',
            slots: {
                    // 自定义render函数
                    default: ({ row }) => {
                        return (
                        <StatusDot
                            value={row.reviewStatus}
                            dictValue={this.reviewStatusList}
                            colors={['success', 'danger','info','warning', ]}
                        ></StatusDot>
                        );
                    },
                },
          },
          {
            title: '操作',
            slots: { default: 'operate' },
            width: 300,
            align: 'center',
            fixed: 'right',
          },
        ],
        tableData: [],
        params: {
                couponName: '',
                couponCode: '',
                basicCategorie: '',
                generationMethod: '',
                effectiveStatus: '',
                auditStatus: '',
                createTime: [],
        },


        couponBusinessTypeList: [
            { label: '充电券', value: 'CHARGE' },
        ],

        basicTypeList: [
            { label: '满减券', value: 'CASH' },
            { label: '折扣券', value: 'DISCOUNT' },
        ],

        generationMethodList: [
            { label: '动态生成', value: 'DG' },
            { label: '批量生成', value: 'BG' },
        ],

        couponStatusList: [
            { label: '启用', value: 'ENABLE' },
            { label: '停用', value: 'DISABLE' },
        ],

        reviewStatusList: [
          { label: '审核通过', value: 'PASS' },
          { label: '审核不通过', value: 'REJECT' },
          { label: '待审核', value: 'WAITING_APPROVAL' },
          { label: '审核中', value: 'IN_APPROVAL' },
        ]
      };
    },

    computed: {
      filterOptions() {
        return {
          config: [
            {
                field: 'couponName',
                title: '优惠券名称',
                element: 'el-input',
            },
            {
                field: 'couponCode',
                title: '优惠券编码',
                element: 'el-input',
            },
            {
                field: 'basicCategorie',
                title: '优惠券基本分类',
                element: 'el-select',
                props: {
                    placeholder: '请选择',
                    options: [
                      { label: '满减券', value: 'CASH' },
                      { label: '折扣券', value: 'DISCOUNT' },
                    ]
                }
            },
            {
                field: 'generationMethod',
                title: '生成方式',
                element: 'el-select',
                props: {
                    placeholder: '请选择',
                    options: [
                      { label: '动态生成', value: 'DG' },
                      { label: '批量生成', value: 'BG' },
                    ]
                }
            },
            {
                field: 'effectiveStatus',
                title: '生效状态',
                element: 'el-select',
                props: {
                    placeholder: '请选择',
                    options: [
                      { label: '启用', value: '1' },
                      { label: '停用', value: '2' },
                    ]
                }
            },
            {
                field: 'auditStatus',
                title: '审核状态',
                element: 'el-select',
                props: {
                    placeholder: '请选择',
                    options: [
                      { label: '待审核', value: 'WAITING_APPROVAL' },
                      { label: '审核通过', value: 'PASS' },
                      { label: '审核未通过', value: 'REJECT' },
                      { label: '审核中	', value: 'IN_APPROVAL' },
                    ]
                }
            },
            {
                field: 'createTime',
                title: '创建时间',
                element: 'el-date-picker',
                props: {
                    type: 'daterange',
                    valueFormat: 'yyyy-MM-dd',
                    options: [],
                }
            },

          ],
          params: this.params,
        };
      },
      modalConfig() {
        return {
            addBtn: false,
            viewBtn: false,
            menu: false,
            editBtn: false,
            delBtn: false,
        }
      },
    },
    mounted() {
      this.loadData();
    },
    methods: {
      // 新增优惠券
      handleAdd() {
        this.$router.push({
          path: '/v2g-charging/operatorManage/couponManage/create',
        })
      },

      // 优惠券详情
      hanleDetail(row) {
        const {
          couponId
        } = row;
        this.$router.push({
          path: '/v2g-charging/operatorManage/couponManage/detail',
          query: {
            couponId,
          }
        })
      },

      // 优惠券审核
      handleAudit(row) {
        const {
          couponId
        } = row;
        this.$router.push({
          path: '/v2g-charging/operatorManage/couponManage/audit',
          query: {
            couponId,
          }
        })
      },

      // // 调整库存
      handelAdjustInventory(row) {
        this.$refs.adjustInventoryModal.couponId = row.couponId;

        
        this.$refs.adjustInventoryModal.form = {
            couponBusinessType: this.selectDictLabel(
                this.couponBusinessTypeList,
                row.couponBizType
            ),
            basicType: this.selectDictLabel(
                this.basicTypeList,
                row.couponType
              ),
            couponName: row.couponName,
            couponNo: row.couponNo,
        }

        this.$refs.adjustInventoryModal.dialogVisible = true
      },

      async loadData() {
            const {
                couponName,
                couponCode,
                basicCategorie,
                generationMethod,
                effectiveStatus,
                auditStatus,
                createTime,
            } = this.params

            let createTimeStart = ''
            let createTimeEnd = ''
            if (createTime && createTime.length) {
                createTimeStart = createTime[0]
                createTimeEnd = createTime[1]
            }


            const params = {
              couponName: couponName ,
              couponNo: couponCode,
              couponStatus: effectiveStatus,
              couponType: basicCategorie,
              generateType: generationMethod,
              reviewStatus: auditStatus,
              createTimeStart,
              createTimeEnd,
              pageNum: this.tablePage.currentPage,
              pageSize: this.tablePage.pageSize,
            }

            this.loading = true;
            const [err, res] = await getCouponList(params)

            this.loading = false;
            if (err) return;

            const { data, total } = res;

            this.tableData = data;
            this.tablePage.total = total;

        },

         // 导出
     async handleExport() {
      const {
                couponName,
                couponCode,
                basicCategorie,
                generationMethod,
                effectiveStatus,
                auditStatus,
                createTime,
            } = this.params

            let createTimeStart = ''
            let createTimeEnd = ''
            if (createTime && createTime.length) {
                createTimeStart = createTime[0]
                createTimeEnd = createTime[1]
            }


            const params = {
              couponName: couponName ,
              couponNo: couponCode,
              couponStatus: effectiveStatus,
              couponType: basicCategorie,
              generateType: generationMethod,
              reviewStatus: auditStatus,
              createTimeStart,
              createTimeEnd,
            }

            this.download(
              '/vehicle-charging-admin/coupon/export',
              {
                ...params,
              },
              `优惠券列表.xlsx`
            );
     },

     // 启用/停用
     handleStatus(row,status) {
      this.$confirm(
        `'确定${status === 'ENABLE' ? '启用' : '停用'}该优惠券吗？'`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        const [err, res] = await changeCouponStatus({
          status: status,
          couponIds: [row.couponId],
        });

        if (err) return;
        this.$message({
          type: 'success',
          message: `${status === 'ENABLE' ? '启用' : '停用'}成功!`,
        });

        this.loadData();
      });
     },

     // 删除
     handleDelete(row) {
        this.$confirm(
          '确定删除该优惠券吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        ).then(async () => {
          const [err, res] = await deleteCoupon({
            couponId: row.couponId,
          });

          if (err) return;
          this.$message({
            type: 'success',
            message: '删除成功!',
          });

          this.loadData();
        });
     }
    },
}
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }
   

  .table-wrap {
    ::v-deep .bd3001-table-select-box {
        display: none;
    }
    ::v-deep .bd3001-header  {
        display: block;
    }
    ::v-deep .bd3001-button {
        display: block !important;
    }
    
    .card-head {
        // position: relative; 
        height: 56px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        margin-top: -20px;
        .card-head-text {
        flex:1;
        width: 520px;
            height: 26px;
            background-image: url('~@/assets/images/bg-title.png');
            background-size: 520px 26px;
            background-repeat: no-repeat;
            font-size: 20px;
            font-style: normal;
            font-weight: 500;
            line-height: 16px;
            padding-left: 36px;
            color: #21252e;
        &::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: -3px; /* 调整这个值来改变边框的宽度 */
            width: 0;
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
            border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
        }
        }   

    }

    .card-head-after {
        width: 100%;
        height: 1px;
        background-color: #DCDEE2;
        margin-bottom: 16px;
    }
    .info-wrap {
        margin-top: 16px;
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .info-item {
            background-color: #FAFBFC;
            flex: 1 1 0;
            // min-width: 180px;
            
            border-radius: 5px;
            padding: 8px 24px;
            box-sizing: border-box;
            // margin-right: 16px;
            display: flex;
            .info-icon {
                width: 42px;
                height: 42px;
            }
            .info-right-wrap {
                flex:1;
                margin-left: 8px;
                .info-title {
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 14px;
                    margin-bottom: 8px;
                }
                .info-number {
                    font-size: 20px;
                    font-weight: 500;
                    .info-unit {
                        font-size: 14px;
                        font-weight: 400;
                    }
                }
            }
        }
        .info-item:last-child {
            margin-right: 0;
        }
    }

    .top-button-wrap {
        display:flex;
        margin: 16px 0;
        .set-btn {
          background-color: #FFFFFF;
          color: #292B33;
          border-color: #DFE1E5;
      }
    }
}

.button-border {
    border: 0.01rem solid #217AFF;
    color: #217AFF;
    background-color: #fff;
}
 

.ellipsis-text {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
  </style>
  