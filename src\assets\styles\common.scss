// 定义一个混入（mixin）用于基本的flex布局设置
@mixin flex($direction: row, $justify-content: flex-start, $align-items: flex-start, $flex-wrap: nowrap) {
    display: flex;
    flex-direction: $direction;
    justify-content: $justify-content;
    align-items: $align-items;
    flex-wrap: $flex-wrap;
  }
  
  // 水平居中对齐的flex布局混入
  @mixin flex-center-horizontal {
    @include flex($justify-content: center);
  }
  
  // 垂直居中对齐的flex布局混入
  @mixin flex-center-vertical {
    @include flex($align-items: center);
  }
  
  // 水平和垂直都居中对齐的flex布局混入
  @mixin flex-center-both {
    @include flex($justify-content: center, $align-items: center);
  }


  @mixin gradient-text($clipColor:#00B9FF, $deg:180deg){
    font-family: YouSheBiaoTiHei;
    background: linear-gradient($deg, #FFFFFF 48.51%, $clipColor 99.98%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }