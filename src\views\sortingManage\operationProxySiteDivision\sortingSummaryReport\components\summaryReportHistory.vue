<template>
  <el-dialog
    title="清分汇总报表历史记录"
    :visible.sync="dialogVisible"
    width="1598px"
  >
    <div class="edit-info-wrap">
      <div class="edit-info-item">
        <div class="info-wrap">
          <div class="info-item-wrap">
            <div class="info-title">分成编号：</div>
            <div class="info-detail">
              <a href="">2323465786</a>
            </div>
          </div>
          <div class="info-item-wrap">
            <div class="info-title">分成周期：</div>
            <div class="info-detail">2024-09-01 ~ 2024-09-01</div>
          </div>
          <div class="info-item-wrap">
            <div class="info-title">结算方：</div>
            <div class="info-detail">省电动</div>
          </div>
          <div class="info-item-wrap">
            <div class="info-title">分成方：</div>
            <div class="info-detail">场站运营商1</div>
          </div>
          <div class="info-item-wrap">
            <div class="info-title">充电站名称：</div>
            <div class="info-detail">充电站1</div>
          </div>

          <div class="info-item-wrap">
            <div class="info-title">地市：</div>
            <div class="info-detail">长沙</div>
          </div>

          <div class="info-item-wrap">
            <div class="info-title">分成类型：</div>
            <div class="info-detail">服务费</div>
          </div>

          <div class="info-item-wrap">
            <div class="info-title">分成条款：</div>
            <div class="info-detail">服务费*30%</div>
          </div>
        </div>
      </div>
      <BuseCrud
        style="margin-bottom: 16px; padding: 0 24px"
        ref="periodTableInfo"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :modalConfig="{ addBtn: false, menu: false }"
        :pagerProps="pagerProps"
        @loadData="loadData"
      ></BuseCrud>
    </div>
  </el-dialog>
</template>
<script>
import { clearInfoDetail } from '@/api/sortingManage/operationProxySiteDivision';
export default {
  props: {
    detailObj: {
      type: Object,
      default: () => {},
    },
  },
  components: {},
  dicts: [],
  data() {
    return {
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      dialogVisible: false,
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'totalCapacity',
          title: '充电总电量（KWH）',
          minWidth: 120,
        },
        {
          field: 'chargingElectricityFee',
          title: '充电电费（元）',
          minWidth: 150,
        },
        {
          field: 'serviceFee',
          title: '充电服务费（元）',
          minWidth: 150,
        },
        {
          field: 'totalFee',
          title: '充电总金额（元）',
          minWidth: 150,
        },
        {
          field: 'pendingCostSharing',
          title: '待分成费用',
          minWidth: 150,
        },
        {
          field: 'settlementBill',
          title: '结算电费（元）',
          minWidth: 150,
        },
        {
          field: 'serviceBill',
          title: '结算服务费（元）',
          minWidth: 150,
        },
        {
          field: 'settlementAmount',
          title: '结算金额（元）',
          minWidth: 150,
        },
        {
          field: 'createTime',
          title: '生成时间',
          minWidth: 150,
        },
        {
          field: 'operater',
          title: '操作人',
          minWidth: 150,
        },
      ],

      tableData: [
        {
          type: '尖时段',
          totalCapacity: '12',
          chargingElectricityFee: '23',
          serviceFee: '23',
          totalFee: '23',
          pendingCostSharing: '23',
          settlementBill: '23',
          serviceBill: '23',
          settlementAmount: '23',
          createTime: '23',
          operater: '23',
        },
        {
          type: '尖时段',
          totalCapacity: '12',
          chargingElectricityFee: '23',
          serviceFee: '23',
          totalFee: '23',
          pendingCostSharing: '23',
          settlementBill: '23',
          serviceBill: '23',
          settlementAmount: '23',
          createTime: '23',
          operater: '23',
        },
        {
          type: '尖时段',
          totalCapacity: '12',
          chargingElectricityFee: '23',
          serviceFee: '23',
          totalFee: '23',
          pendingCostSharing: '23',
          settlementBill: '23',
          serviceBill: '23',
          settlementAmount: '23',
          createTime: '23',
          operater: '23',
        },
      ],
    };
  },
  watch: {
    dialogVisible(value) {
      if (value) {
        this.loadData();
      }
    },
  },
  computed: {},
  mounted() {},
  methods: {
    async loadData() {
      const [err, res] = await clearInfoDetail({
        id: this.detailObj.infoId,
      });

      if (err) return;
    },
  },
};
</script>

<style lang="scss" scoped>
.edit-info-wrap {
  min-height: 520px;
  .edit-info-item {
    .edit-info-item-title-after {
      display: flex;
      height: 88px;
      width: 100%;
      background: linear-gradient(180deg, #d9ecff 0%, #ffffff 100%);
      padding: 0 0 0 24px;
      box-sizing: border-box;
      align-items: center;
      font-weight: 500;
      font-size: 24px;
      .edit-detail-title-icon {
        width: 32px;
        height: 32px;
        background-image: url('~@/assets/station/compare-detail-title-icon.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 12px;
      }
    }
    .title-wrap {
      height: 18px;
      padding: 0 0 0 24px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      font-weight: 500;
      font-size: 18px;
      margin-bottom: 24px;
      color: #12151a;
      .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
      }
    }
    .info-wrap {
      padding: 0 0 0 24px;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      margin-bottom: 24px;

      .info-item-wrap {
        display: flex;
        flex: 1 1 25%;
        margin-bottom: 24px;
        .info-title {
          font-weight: 400;
          font-size: 16px;
          line-height: 16px;
          color: #505363;
        }
        .info-detail {
          font-weight: 400;
          font-size: 16px;
          line-height: 16px;
          color: #292b33;
          .price-number {
            color: #ff8d24;
            font-weight: 500;
          }
          a {
            color: #217aff;
            text-decoration: underline;
          }
        }
      }
    }
  }
}

::v-deep .bd3001-content {
  padding: 0 !important;
}
</style>
