<template>
  <div class="region-select">
    <div class="region-search">
      <el-input
        v-model="name"
        clearable
        size="medium"
        placeholder="请输入资源名称进行搜索"
      >
        <i slot="suffix" class="el-icon-search el-input__icon" />
      </el-input>
    </div>
    <div class="region-list">
      <el-tree
        class="filter-tree"
        :data="treeData"
        :props="defaultProps"
        default-expand-all
        highlight-current
        node-key="code"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        @current-change="nodeClick"
        ref="nodeTree"
      >
        <template #default="{ data }">
          <div class="region-tree-item">
            <img
              v-if="data.children && data.children.length"
              src="@/assets/images/icon-folder.png"
              class="folder-icon"
              alt=""
            />
            <img
              v-else
              src="@/assets/images/icon-file.png"
              class="folder-icon"
              alt=""
            />

            {{ data.name }}
          </div>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    treeData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      name: '',
      defaultProps: {
        children: 'children',
        label: 'name',
      },
    };
  },
  watch: {
    name(val) {
      this.$refs.nodeTree.filter(val);
    },
  },
  async mounted() {
    if (this.treeData.length > 0) {
      this.$nextTick(() => {
        this.$refs.nodeTree.setCurrentKey(this.treeData[0].code);
        // 手动触发点击事件
        this.nodeClick(this.treeData[0]);
      });
    }
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    nodeClick(node) {
      this.$emit('nodeClick', node);
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.region-select {
  // max-height: 100%;
  // overflow: auto;
  float: left;
  width: 300px;
  padding: 8px 0px;
  background-color: #fff;
  border-radius: 5px;
  background: #fff;
  margin-right: 16px;
  box-shadow: 0px 5px 12px 4px rgba(0, 34, 101, 0.04),
    0px 3px 6px 0px rgba(0, 34, 101, 0.04),
    0px 1px 2px -2px rgba(0, 34, 101, 0.04);
  .region-search {
    padding: 0 16px;
    margin-bottom: 16px;
  }
}
.region-tree-item {
  display: flex;
  align-items: center;
  font-size: 16px;
  line-height: 40px;
  color: #12151a;
  .folder-icon {
    width: 20px;
    height: 20px;
    display: block;
    margin-right: 8px;
  }
}
.region-list {
  ::v-deep .el-tree-node__content {
    height: auto;
  }
  ::v-deep .el-tree-node__expand-icon {
    margin-left: 10px;
  }
}
</style>
