<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <!-- 头部信息 -->
    <div class="device-head">
      <img
        src="@/assets/stationDivision/settlement-icon.png"
        class="device-head-icon"
      />

      <div class="device-info-wrap">
        <div class="device-title-wrap">
          <div class="device-title">
            计划编号：{{ planDetail.patrolPlanId || '' }}
          </div>
          <div class="device-status">
            {{ selectDictLabel(this.statusList, planDetail.status) }}
          </div>
        </div>
        <div class="device-info-wrap">
          <el-row>
            <el-col :span="24">
              <span class="label">计划名称：</span>
              <span class="value">{{ planDetail.patrolPlanName || '' }}</span>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>

    <!-- 基础信息 -->
    <div class="info-card">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">
          基础信息
          <div class="top-button-wrap"></div>
        </div>
      </div>

      <div class="form-wrap">
        <el-row :gutter="20" style="margin-bottom: 24px">
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">分组名称：</div>
              <div class="info-detail">{{ planDetail.groupName || '' }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">运维班组：</div>
              <div class="info-detail">
                {{ planDetail.maintenanceTeamName || '' }}
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">巡视类型：</div>
              <div class="info-detail">
                {{
                  selectDictLabel(
                    this.dict.type.ls_charging_patrol_type,
                    planDetail.patrolType
                  )
                }}
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-bottom: 24px">
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">巡视频次：</div>
              <div class="info-detail">
                {{ planDetail.patrolFrequencies || '' }}
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">巡视时效：</div>
              <div class="info-detail">{{ planDetail.patrolDay || '' }}</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="info-card">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">巡视项目</div>
      </div>

      <div class="form-wrap">
        <div class="table-container">
          <BuseCrud
            ref="settlementCrud"
            :tableColumn="tableColumn"
            :tableData="planDetail.patrolContentList"
            :modalConfig="{ addBtn: false, menu: false }"
          ></BuseCrud>
        </div>
      </div>
    </div>

    <div class="info-card">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">充电站信息</div>
      </div>

      <div class="form-wrap">
        <div class="table-container">
          <BuseCrud
            ref="settlementCrud"
            :tableColumn="stationTableColumn"
            :tableData="stationTableList"
            :modalConfig="{ addBtn: false, menu: false }"
          ></BuseCrud>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getPlanDetail } from '@/api/interconnection/patrolPlan';

export default {
  data() {
    return {
      planId: '',
      planDetail: {},
      patrolContentList: [],
      stationList: [], // 实际项目中需要从后端获取关联的充电站列表
      statusList: [
        { label: '启用', value: 0 },
        { label: '停用', value: 1 },
      ],
      tableColumn: [
        {
          field: 'patrolContentNo',
          title: '巡视项目编号',
          minWidth: 120,
        },
        {
          field: 'patrolContentName',
          title: '巡视项目名称',
          minWidth: 120,
        },
        {
          field: 'patrolRequire',
          title: '巡视要求',
          minWidth: 200,
        },
        {
          field: 'sort',
          title: '排序',
          minWidth: 120,
        },
        {
          field: 'watermark',
          title: '是否需要上传水印照片',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_watermark,
              cellValue
            );
          },
        },
      ],
      stationTableColumn: [
        {
          field: 'patrolContentNo',
          title: '充电站编号',
          minWidth: 120,
        },
        {
          field: 'patrolContentNo',
          title: '充电站名称',
          minWidth: 120,
        },
        {
          field: 'patrolContentNo',
          title: '资产属性',
          minWidth: 120,
        },
        {
          field: 'patrolContentNo',
          title: '运营模式',
          minWidth: 120,
        },
        {
          field: 'patrolContentNo',
          title: '行政区域',
          minWidth: 120,
        },
        {
          field: 'patrolContentNo',
          title: '场站位置',
          minWidth: 120,
        },
      ],
      stationTableList: [],
    };
  },
  dicts: [
    'ls_charging_patrol_type',
    'ls_charging_patrol_frequencies_type',
    'ls_charging_watermark',
  ],
  created() {
    this.planId = this.$route.query.patrolPlanId;
    if (this.planId) {
      this.loadPlanDetail();
    }
  },
  methods: {
    watermarkFilter(watermark) {
      return this.selectDictLabel(
        this.dict.type.ls_charging_watermark,
        watermark
      );
    },
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 加载计划详情
    async loadPlanDetail() {
      try {
        const [err, res] = await getPlanDetail({ patrolPlanId: this.planId });
        if (err) return;

        this.planDetail = res.data || {};

        // 加载关联的充电站列表
        // 实际项目中需要从后端获取关联的充电站列表
        // 这里模拟一些数据
        this.loadStationList();
      } catch (error) {
        console.error('加载计划详情失败', error);
      }
    },

    // 加载充电站列表（模拟数据）
    loadStationList() {
      // 实际项目中需要从后端获取关联的充电站列表
      this.stationList = [
        {
          stationId: '123456',
          stationCode: '123456',
          stationName: '能充充电站',
          stationType: '01',
          operateType: '01',
          adminArea: '华南省-长沙市-岳麓区',
          stationAddress: '湖南省长沙市岳麓区中电广场1号',
        },
        {
          stationId: '123457',
          stationCode: '123457',
          stationName: '能充充电站2',
          stationType: '02',
          operateType: '02',
          adminArea: '华南省-长沙市-岳麓区',
          stationAddress: '湖南省长沙市岳麓区中电广场2号',
        },
      ];
    },
  },
};
</script>

<style lang="scss" scoped>
.container-float {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.device-head {
  background-color: #fff;
  display: flex;
  height: 112px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  box-sizing: border-box;

  .device-head-icon {
    width: 48px;
    height: 48px;
    margin-right: 24px;
  }

  .device-info-wrap {
    flex: 1;

    .device-title-wrap {
      height: 32px;
      display: flex;
      align-items: center;

      .device-title {
        font-weight: 500;
        font-size: 24px;
        color: #12151a;
      }

      .device-status {
        padding: 0 10px;
        height: 24px;
        border-radius: 10px 0 10px 0;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: center;
        color: #fff;
        background: linear-gradient(321.01deg, #ffb624 8.79%, #ff8d24 100.27%);
        margin-left: 12px;
      }
    }

    .device-info-wrap {
      height: 16px;
      margin-top: 16px;
      font-size: 16px;
      font-weight: 400;
      color: #292b33;

      .label {
        color: #505363;
        margin-right: 8px;
      }

      .value {
        color: #292b33;
      }
    }
  }
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    background: linear-gradient(180deg, #e9f2ff 0%, #ffffff 100%);

    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }

    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .form-wrap {
    padding: 0 16px 16px 16px;

    .info-title {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #505363;
      margin-right: 8px;
    }

    .info-detail {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #292b33;
    }
  }
}

.table-container {
  width: 100%;
  overflow-x: auto;
}

.upload-section {
  margin-top: 20px;

  .upload-title {
    font-size: 16px;
    color: #505363;
    margin-bottom: 10px;
  }

  .upload-area {
    width: 100%;
    border-radius: 6px;

    .upload-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 8px;
    }
  }
}

.bottom-wrap {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 86px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-color: #ffffff;
  padding-right: 32px;
  box-sizing: border-box;
  z-index: 100;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

::v-deep .el-table {
  .cell {
    padding: 8px 5px;
  }

  th.is-leaf {
    background-color: #f5f7fa;
    font-weight: 500;
  }
}

::v-deep .el-upload-dragger {
  width: 100%;
  height: 180px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .el-icon-upload {
    font-size: 40px;
    color: #c0c4cc;
    margin-bottom: 10px;
  }

  .el-upload__text {
    font-size: 14px;
    text-align: center;
    color: #606266;
  }
}

// .form-label {
//   font-size: 16px;
//   color: #505363;
//   display: inline-block;
//   margin-bottom: 10px;

//   &.required::before {
//     content: '*';
//     color: #f56c6c;
//     margin-right: 4px;
//   }
// }
</style>
