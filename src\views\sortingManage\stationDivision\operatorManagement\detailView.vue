<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <!-- 头部信息 -->
    <div class="device-head">
      <img
        src="@/assets/stationDivision/settlement-icon.png"
        class="device-head-icon"
      />

      <div class="device-info-wrap">
        <div class="device-title-wrap">
          <div class="device-title">分成编号：{{ sharingNo }}</div>
          <div class="device-status" v-if="status">{{ status }}</div>
        </div>
        <div class="device-info-wrap">
          <el-row>
            <el-col :span="8">
              <span class="label">充电站：</span>
              <span class="value">{{ stationName }}</span>
            </el-col>
            <el-col :span="8">
              <span class="label">地市：</span>
              <span class="value">{{ cityName }}</span>
            </el-col>
            <el-col :span="8">
              <span class="label">分成周期：</span>
              <span class="value">{{ sharingPeriod }}</span>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>

    <!-- 基础信息 -->
    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">基本信息</div>
      </div>

      <div class="form-wrap">
        <el-row :gutter="20" style="margin-bottom: 24px">
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">分成方：</div>
              <div class="info-detail">{{ siteParty }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">分成类型：</div>
              <div class="info-detail">{{ sharingType }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">结算时间：</div>
              <div class="info-detail">{{ settlementTime }}</div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-bottom: 24px">
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">账单生成时间：</div>
              <div class="info-detail">{{ billGenerationTime }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">账单生成方式：</div>
              <div class="info-detail">{{ billGenerationMethod }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">录入人：</div>
              <div class="info-detail">{{ enteredBy }}</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 结算信息 -->
    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">结算信息</div>
      </div>

      <div class="form-wrap">
        <div class="settlement-amount-wrap">
          <div class="settlement-amount-label">结算金额：</div>
          <div class="settlement-amount-value">{{ settlementAmount }} 元</div>
        </div>

        <div class="settlement-terms">
          <div class="info-title">结算数据：</div>
        </div>

        <div class="table-container">
          <BuseCrud
            ref="settlementCrud"
            :tableColumn="settlementTableColumn"
            :tableData="tableData"
            :modalConfig="{ addBtn: false, menu: false }"
          ></BuseCrud>
        </div>

        <div class="settlement-terms">
          <div class="info-title">分成条款：</div>
          <div class="info-detail">{{ sharingTerms }}</div>
        </div>
      </div>
    </div>

    <!-- 结算凭证 -->
    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">结算凭证</div>
      </div>

      <div class="form-wrap">
        <div class="voucher-list">
          <div
            class="voucher-item"
            v-for="(item, index) in voucherList"
            :key="index"
          >
            <div class="voucher-icon">
              <i class="el-icon-document"></i>
            </div>
            <div class="voucher-info">
              <div class="voucher-name">{{ item.name }}</div>
            </div>
            <div class="voucher-action">
              <el-button type="text" @click="downloadVoucher(item)">
                下载
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <!-- <div class="bottom-wrap">
      <el-button @click="goBack">返回</el-button>
      <el-button type="primary" @click="handlePrint">打印</el-button>
    </div> -->
    <div class="bottom-wrap">
      <el-button @click="goBack">返回</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SettlementDetail',
  data() {
    return {
      // 头部信息
      sharingNo: '3456789',
      status: '已结算',
      stationName: '充电站1',
      cityName: '长沙市',
      sharingPeriod: '2024-09-01~2024-09-30',

      // 基础信息
      siteParty: '场地1',
      sharingType: '服务费',
      settlementTime: '2024-12-23 17:23:45',
      billGenerationTime: '2024-12-23 17:23:45',
      billGenerationMethod: '手动录入',
      enteredBy: '张三',

      // 结算信息
      settlementAmount: '150',
      sharingTerms: '(充电服务费-指标电费) *30%',

      // 结算数据表格列配置
      settlementTableColumn: [
        {
          field: 'chargingPower',
          title: '充电总电量(KWH)',
          minWidth: 160,
        },
        {
          field: 'chargingFee',
          title: '充电电费(元)',
          minWidth: 120,
        },
        {
          field: 'internalChargingFee',
          title: '内部车辆充电电费(元)',
          minWidth: 180,
        },
        {
          field: 'externalChargingFee',
          title: '外部车辆充电电费(元)',
          minWidth: 180,
        },
        {
          field: 'serviceFee',
          title: '充电服务费(元)',
          minWidth: 140,
        },
        {
          field: 'internalServiceFee',
          title: '内部车辆充电服务费(元)',
          minWidth: 200,
        },
        {
          field: 'externalServiceFee',
          title: '外部车辆充电服务费(元)',
          minWidth: 200,
        },
        {
          field: 'totalIncome',
          title: '充电总收入(元)',
          minWidth: 140,
        },
        {
          field: 'supplyPower',
          title: '供电电量(KWH)',
          minWidth: 140,
        },
        {
          field: 'supplyFee',
          title: '供电电费(元)',
          minWidth: 140,
        },
        {
          field: 'powerLossPower',
          title: '电损电量(KWH)',
          minWidth: 140,
        },
        {
          field: 'powerLossType',
          title: '电损计量类型',
          minWidth: 120,
        },
        {
          field: 'powerLossFee',
          title: '电损电费(元)',
          minWidth: 120,
        },
        {
          field: 'operationCost',
          title: '运维成本(元)',
          minWidth: 120,
        },
        {
          field: 'platformFee',
          title: '平台服务费(元)',
          minWidth: 140,
        },
        {
          field: 'siteFee',
          title: '场地租金(元)',
          minWidth: 120,
        },
        {
          field: 'sharingPartyLossFee',
          title: '分成方承担损耗电费(元)',
          minWidth: 200,
        },
        {
          field: 'pendingSharingFee',
          title: '待分成费用(元)',
          minWidth: 140,
        },
        {
          field: 'settlementElectricityFee',
          title: '结算电费(元)',
          minWidth: 120,
        },
        {
          field: 'settlementServiceFee',
          title: '结算服务费(元)',
          minWidth: 140,
        },
        {
          field: 'settlementRentFee',
          title: '结算租金(元)',
          minWidth: 120,
        },
        {
          field: 'settlementAmount',
          title: '结算金额(元)',
          minWidth: 120,
        },
      ],

      // 结算数据
      tableData: [
        {
          chargingPower: 2000,
          chargingFee: 1000,
          internalChargingFee: 500,
          externalChargingFee: 2000,
          serviceFee: 950,
          internalServiceFee: 200,
          externalServiceFee: 100,
          totalIncome: 1600,
          supplyPower: 1000,
          supplyFee: 1000,
          powerLossPower: 1000,
          powerLossType: '电能损耗',
          powerLossFee: 180,
          operationCost: '-',
          platformFee: '-',
          siteFee: '-',
          sharingPartyLossFee: '-',
          pendingSharingFee: 500,
          settlementElectricityFee: '-',
          settlementServiceFee: 150,
          settlementRentFee: '-',
          settlementAmount: 150,
        },
      ],

      // 结算凭证
      voucherList: [
        { name: '结算凭证名称xxxxxxxx名称.pdf', url: '#' },
        { name: '结算凭证名称xxxxxxxx名称.pdf', url: '#' },
        { name: '结算凭证名称xxxxxxxx名称.pdf', url: '#' },
      ],
    };
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 打印页面
    handlePrint() {
      window.print();
    },

    // 下载凭证
    downloadVoucher(item) {
      this.$message.success(`凭证 ${item.name} 下载成功`);
    },
  },
};
</script>

<style lang="scss" scoped>
.container-float {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.device-head {
  background-color: #fff;
  display: flex;
  height: 112px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  box-sizing: border-box;

  .device-head-icon {
    width: 48px;
    height: 48px;
    margin-right: 24px;
  }

  .device-info-wrap {
    flex: 1;

    .device-title-wrap {
      height: 32px;
      display: flex;
      align-items: center;

      .device-title {
        font-weight: 500;
        font-size: 24px;
        color: #12151a;
      }

      .device-status {
        padding: 0 10px;
        height: 24px;
        border-radius: 10px 0 10px 0;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: center;
        color: #fff;
        background: linear-gradient(321.01deg, #00c864 8.79%, #38f3ca 100.27%);
        margin-left: 12px;
      }
    }

    .device-info-wrap {
      height: 16px;
      margin-top: 16px;
      font-size: 16px;
      font-weight: 400;
      color: #292b33;

      .label {
        color: #505363;
        margin-right: 8px;
      }

      .value {
        color: #292b33;
      }
    }
  }
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    background: linear-gradient(180deg, #e9f2ff 0%, #ffffff 100%);

    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }

    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .form-wrap {
    padding: 16px;

    .info-title {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #505363;
      margin-right: 8px;
    }

    .info-detail {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #292b33;
    }

    .settlement-amount-wrap {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .settlement-amount-label {
        font-size: 16px;
        font-weight: 500;
        color: #505363;
      }

      .settlement-amount-value {
        font-size: 24px;
        font-weight: bold;
        color: #ff9900;
        margin-left: 8px;
      }
    }

    .settlement-terms {
      display: flex;
      margin-bottom: 16px;
    }
  }
}

.table-container {
  width: 100%;
  overflow-x: auto;
  margin-top: 16px;
}

.voucher-list {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .voucher-item {
    display: flex;
    align-items: center;
    padding: 12px 6px;
    border: 1px solid #ebeef5;
    width: 49%;
    margin-bottom: 6px;

    // &:last-child {
    //   border-bottom: none;
    // }

    .voucher-icon {
      font-size: 24px;
      color: #409eff;
      margin-right: 12px;
    }

    .voucher-info {
      flex: 1;

      .voucher-name {
        font-size: 14px;
        color: #333;
      }
    }
  }
}

.bottom-wrap {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 86px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-color: #ffffff;
  padding-right: 32px;
  box-sizing: border-box;
  z-index: 100;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);

  .el-button {
    margin: 0 10px;
  }
}

@media print {
  .bottom-wrap {
    display: none;
  }
}
</style>
