<template>
    <el-dialog title="详情" :visible.sync="dialogVisible" width="1598px">
        <div class="edit-info-wrap">
            <div class="edit-info-item">
                <div class="edit-info-item-title-after">
                    <div class="edit-detail-title-icon"></div>
                    <div>当前计费规则</div>         
                </div>

                <div v-if="JSON.stringify(beforeData) === '{}'" style="display: flex; align-items: center;justify-content: center;">
                    暂无数据
                </div>

                <div v-else>

                    

                    <div class="title-wrap">
                        <div class="before-icon"></div>
                        <div>基础信息</div>
                    </div>

                    <div class="info-wrap">
                        <div class="info-item-wrap">
                            <div class="info-title">计费编号：</div>
                            <div class="info-detail">{{ beforeData.billNo }}</div>
                        </div>

                        <div class="info-item-wrap">
                            <div class="info-title">计费名称：</div>
                            <div class="info-detail">{{ beforeData.billModelName }}</div>
                        </div>

                        <div class="info-item-wrap">
                            <div class="info-title">适用区域：</div>
                            <div class="info-detail">{{ beforeData.applyArea }}</div>
                        </div>

                        <div class="info-item-wrap">
                            <div class="info-title">单位：</div>
                            <div class="info-detail">{{ beforeData.unit }}</div>
                        </div>
                    </div>
                
                    <div class="title-wrap">
                        <div class="before-icon"></div>
                        <div>计费明细</div>
                    </div>

                    <div class="info-wrap"  v-if="beforeData.billModel === '0201'">
                        <div class="info-item-wrap">
                            <div class="info-title">计费模式：</div>
                            <div class="info-detail">{{ beforeData.billModelDesc }}</div>
                        </div>

                        <div class="info-item-wrap">
                            <div class="info-title">电价(元/kWh)：</div>
                            <div class="info-detail">{{ beforeData.elePrice }}</div>
                        </div>

                        <div class="info-item-wrap" >
                            <div class="info-title">服务费(元/kWh)：</div>
                            <div class="info-detail">{{ beforeData.servicePrice }}</div>
                        </div>

                        <div class="info-item-wrap" >
                            <div class="info-title">总价(元/kWh)：</div>
                            <div class="info-detail">{{ beforeData.totalPrice }}</div>
                        </div>


                    </div>

                    <div class="info-wrap"  v-if="beforeData.billModel === '0202'">
                        <div class="info-item-wrap">
                            <div class="info-title">计费模式：</div>
                            <div class="info-detail">{{ beforeData.billModelDesc }}</div>
                        </div>

                        <div class="info-item-wrap">
                            
                        </div>

                        <div class="info-item-wrap" >
                            <div class="info-title">尖电价：</div>
                            <div class="info-detail">
                                <span class="price-number">{{ beforeData.superPeak }}</span> kWh/元
                            </div>
                        </div>

                        <div class="info-item-wrap" >
                            <div class="info-title">峰电价：</div>
                            <div class="info-detail">
                                <span class="price-number">{{ beforeData.peak }}</span> kWh/元
                            </div>
                        </div>

                        <div class="info-item-wrap" >
                            <div class="info-title">平电价：</div>
                            <div class="info-detail">
                                <span class="price-number">{{ beforeData.normal }}</span> kWh/元
                            </div>
                        </div>

                        <div class="info-item-wrap" >
                            <div class="info-title">谷电价：</div>
                            <div class="info-detail">
                                <span class="price-number">{{ beforeData.valley }}</span> kWh/元
                            </div>
                        </div>

                        <div class="info-item-wrap" >
                            <div class="info-title">尖服务费：</div>
                            <div class="info-detail">
                                <span class="price-number">{{ beforeData.topServicePrice }}</span> kWh/元
                            </div>
                            
                        </div>

                        <div class="info-item-wrap">
                            <div class="info-title">峰服务费：</div>
                            <div class="info-detail">
                                <span class="price-number">{{ beforeData.peakServicePrice }}</span> kWh/元
                            </div>
                        </div>

                        <div class="info-item-wrap" >
                            <div class="info-title">平服务费：</div>
                            <div class="info-detail">
                                <span class="price-number">{{ beforeData.flatServicePrice }}</span> kWh/元
                            </div>
                        
                        </div>

                        <div class="info-item-wrap" >
                            <div class="info-title">谷服务费：</div>
                            <div class="info-detail">
                                <span class="price-number">{{ beforeData.valleyServicePrice }}</span> kWh/元
                            </div>
                        </div>

                        <div class="info-item-wrap" >
                            <div class="info-title">电价时段：</div>
                            <div class="info-detail">{{ beforeData.timePeriod }}</div>
                        </div>

                    </div>
                    
                    <el-radio-group v-if="beforeData.billModel === '0202'" v-model="beforeChooseTimeIn"  @change="changeTime" style="margin-bottom: 16px;">
                        <el-radio-button v-for="(item, index) in beforeTimeList" :key="index" :label="item.month">{{ item.month }}</el-radio-button>
                    </el-radio-group>
                            <BuseCrud
                                v-if="beforeData.billModel === '0202'"
                                style="margin-bottom: 16px;"
                                ref="periodInfo"
                                :tableColumn="tableColumn"
                                :tableData="beforeTableDataIn"
                                :modalConfig="{ addBtn: false, menu: false }"
                            >
                            </BuseCrud>

                </div>
                

              

            </div>
            <div class="edit-info-item">
                <div class="edit-info-item-title-after">
                    <div class="edit-detail-title-icon"></div>
                    <div>即将下发计费规则</div>
                </div>

                <div v-if="JSON.stringify(afterData) === '{}'" style="display: flex; align-items: center;justify-content: center;">
                    暂无数据
                </div>
                <div v-else>

                    <div class="title-wrap">
                        <div class="before-icon"></div>
                        <div>基础信息</div>
                    </div>
                
                    <div class="info-wrap">
                        <div class="info-item-wrap">
                            <div class="info-title">计费编号：</div>
                            <div class="info-detail">{{afterData.billNo  }}</div>
                        </div>

                        <div class="info-item-wrap">
                            <div class="info-title">计费名称：</div>
                            <div class="info-detail">{{ afterData.billModelName }}</div>
                        </div>

                        <div class="info-item-wrap">
                            <div class="info-title">适用区域：</div>
                            <div class="info-detail">{{ afterData.applyArea }}</div>
                        </div>

                        <div class="info-item-wrap">
                            <div class="info-title">单位：</div>
                            <div class="info-detail">{{ afterData.unit }}</div>
                        </div>
                    </div>

                    <div class="title-wrap">
                        <div class="before-icon"></div>
                        <div>计费明细</div>
                    </div>

                    
                    <div class="info-wrap"  v-if="afterData.billModel === '0201'">
                        <div class="info-item-wrap">
                            <div class="info-title">计费模式：</div>
                            <div class="info-detail">{{ afterData.billModelDesc }}</div>
                        </div>

                        <div class="info-item-wrap">
                            <div class="info-title">电价(元/kWh)：</div>
                            <div class="info-detail">{{ afterData.elePrice }}</div>
                        </div>

                        <div class="info-item-wrap" >
                            <div class="info-title">服务费(元/kWh)：</div>
                            <div class="info-detail">{{ afterData.servicePrice }}</div>
                        </div>

                        <div class="info-item-wrap" >
                            <div class="info-title">总价(元/kWh)：</div>
                            <div class="info-detail">{{ afterData.totalPrice }}</div>
                        </div>


                    </div>

                    <div class="info-wrap"  v-if="afterData.billModel === '0202'">
                        <div class="info-item-wrap">
                            <div class="info-title">计费模式：</div>
                            <div class="info-detail">{{ afterData.billModelDesc }}</div>
                        </div>

                        <div class="info-item-wrap">
                            
                        </div>

                        <div class="info-item-wrap" >
                            <div class="info-title">尖电价：</div>
                            <div class="info-detail">
                                <span class="price-number">{{ afterData.superPeak }}</span> kWh/元
                            </div>
                        </div>

                        <div class="info-item-wrap" >
                            <div class="info-title">峰电价：</div>
                            <div class="info-detail">
                                <span class="price-number">{{ afterData.peak }}</span> kWh/元
                            </div>
                        </div>

                        <div class="info-item-wrap" >
                            <div class="info-title">平电价：</div>
                            <div class="info-detail">
                                <span class="price-number">{{ afterData.normal }}</span> kWh/元
                            </div>
                        </div>

                        <div class="info-item-wrap" >
                            <div class="info-title">谷电价：</div>
                            <div class="info-detail">
                                <span class="price-number">{{ afterData.valley }}</span> kWh/元
                            </div>
                        </div>

                        <div class="info-item-wrap" >
                            <div class="info-title">尖服务费：</div>
                            <div class="info-detail">
                                <span class="price-number">{{ afterData.topServicePrice }}</span> kWh/元
                            </div>
                            
                        </div>

                        <div class="info-item-wrap">
                            <div class="info-title">峰服务费：</div>
                            <div class="info-detail">
                                <span class="price-number">{{ afterData.peakServicePrice }}</span> kWh/元
                            </div>
                        </div>

                        <div class="info-item-wrap" >
                            <div class="info-title">平服务费：</div>
                            <div class="info-detail">
                                <span class="price-number">{{ afterData.flatServicePrice }}</span> kWh/元
                            </div>
                        
                        </div>

                        <div class="info-item-wrap" >
                            <div class="info-title">谷服务费：</div>
                            <div class="info-detail">
                                <span class="price-number">{{ afterData.valleyServicePrice }}</span> kWh/元
                            </div>
                        </div>

                        <div class="info-item-wrap" >
                            <div class="info-title">电价时段：</div>
                            <div class="info-detail">{{ afterData.timePeriod }}</div>
                        </div>

                    </div>
                    
                    <el-radio-group v-if="afterData.billModel === '0202'" v-model="afterChooseTimeIn"  @change="changeTime" style="margin-bottom: 16px;">
                        <el-radio-button v-for="(item, index) in afterTimeList" :key="index" :label="item.month">{{ item.month }}</el-radio-button>
                    </el-radio-group>
                            <BuseCrud
                                v-if="afterData.billModel === '0202'"
                                style="margin-bottom: 16px;"
                                ref="periodInfo"
                                :tableColumn="tableColumn"
                                :tableData="afterTableDataIn"
                                :modalConfig="{ addBtn: false, menu: false }"
                            >
                            </BuseCrud>
                    
                </div>

            </div>

        </div>

  
    </el-dialog>
   
</template>
<script>  



  export default {
    props: {
        beforeData: {
            type: Object,
            default: () => {},
        },
        beforeTimeList: {
            type: Array,
            default: () => [],
        },
        beforeChooseTime: {
            type: String,
            default: () => '',
        },
        beforeTableData: {
            type: Array,
            default: () => [],
        },
        afterData: {
            type: Object,
            default: () => {},
        },
        afterTimeList: {
            type: Array,
            default: () => [],
        },
        afterChooseTime: {
            type: String,
            default: () => '',
        },
        afterTableData: {
            type: Array,
            default: () => [],
        }


    },
    components: {},
    dicts: [

    ],
    data() {
        return {
            dialogVisible: false,
            tableColumn: [
            {
                    field: 'priceType',
                    title: '类型',
                    minWidth: 120, 
                    formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                            this.priceTypeList,
                            cellValue
                        );
                    },
                },
                {
                    field: 'startTime',
                    title: '开始时间',
                    minWidth: 120, 
                },
                {
                    field: 'endTime',
                    title: '结束时间',
                    minWidth: 120, 
                },
            ],

            priceTypeList: [
                { label: '尖', value: '1' },
                { label: '峰', value: '2' },
                { label: '平', value: '3' },
                { label: '谷', value: '4' },
            ],

            tableData: [],

            beforeChooseTimeIn: '',

            beforeTableDataIn: [],

            afterChooseTimeIn: '',

            afterTableDataIn: [],

        };
    },
    watch: {
        
    },
    computed: {
        
    },
    mounted() {
        
    },
    methods: {
        handleBeforeData() {
            this.beforeChooseTimeIn = this.beforeChooseTime;
            this.beforeTableDataIn = this.beforeTableData;
        }, 
        changeTime() {
            this.beforeTableDataIn = this.beforeTimeList.find(item => item.month === this.beforeChooseTimeIn).priceList;
        },

        handleAfterData() {
            this.afterChooseTimeIn = this.afterChooseTime;
            this.afterTableDataIn = this.afterTableData;
        }, 
        changeAfterTime() {
            this.afterTableDataIn = this.afterTimeList.find(item => item.month === this.afterChooseTimeIn).priceList;
        },
    },
  };
  </script>

<style lang="scss" scoped>
.edit-info-wrap {
    display: flex;
    min-height: 520px;
    justify-content: space-between;
    .edit-info-item {
        width: 743px;
        .edit-info-item-title-after {
            display: flex;
            height: 88px;
            width: 100%;
            background: linear-gradient(180deg, #D9ECFF 0%, #FFFFFF 100%);
            padding: 0 0 0 24px;
            box-sizing: border-box;
            align-items: center;
            font-weight: 500;
            font-size: 24px;
            .edit-detail-title-icon {
                width: 32px;
                height: 32px;
                background-image: url('~@/assets/station/compare-detail-title-icon.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                margin-right: 12px;
            }
        }
        .title-wrap {
            height: 18px;
            padding: 0 0 0 24px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            font-weight: 500;
            font-size: 18px;
            margin-bottom: 24px;
            color: #12151A;
            .before-icon {
                width: 3px;
                height: 16px;
                background-image: url('~@/assets/station/consno-before.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                margin-right: 8px;
            }
        }
        .info-wrap {
            padding: 0 0 0 24px;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            margin-bottom: 16px;
           
            .info-item-wrap {
                display: flex;
                flex: 1 1 50%; 
                margin-bottom: 16px;
                .info-title {
                    font-weight: 400;
                    font-size: 16px;
                    line-height: 16px;
                    color: #505363;
                }
                .info-detail {
                    font-weight: 400;
                    font-size: 16px;
                    line-height: 16px;
                    color: #292B33;
                    .price-number {
                        color: #FF8D24;
                        font-weight: 500;
                    }
                }
            }
           
        }
        

    }
}


::v-deep .bd3001-content {
    padding: 0 !important;
  }

  </style>
  