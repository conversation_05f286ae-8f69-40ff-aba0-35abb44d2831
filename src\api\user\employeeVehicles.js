import request from '@/utils/request';

const baseUrl = '/vehicle-charging-admin';

/**
 * 批量导入车辆
 * @param {FormData} data - 包含文件的表单数据
 * @param {File} data.file - 车辆信息文件
 * @returns {Promise<Object>} 返回结果
 * @returns {string} response.code - 返回码
 * @returns {string} response.msg - 返回信息
 * @returns {string} response.subCode - 子返回码
 * @returns {string} response.subMsg - 子返回信息
 * @returns {Object} response.data - 返回数据
 * @returns {number} response.total - 总条数
 */
export function importVehicle(data) {
  return request({
    url: baseUrl + '/vehicle/import',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 下载车辆批量导入模板
 * @returns {Promise<Blob>} 返回模板文件
 */
export function downloadVehicleTemplate() {
  return request({
    url: baseUrl + '/vehicle/template',
    method: 'get',
    responseType: 'blob',
  });
}

/**
 * 批量启用/禁用车辆
 * @param {Object} data - 请求参数
 * @param {string[]} data.ids - 车辆ID集合
 * @param {string} data.type - 状态：ENABLE启用 DISABLE禁用
 * @returns {Promise<Object>} 返回结果
 * @returns {string} response.code - 返回码
 * @returns {string} response.msg - 返回信息
 * @returns {string} response.subCode - 子返回码
 * @returns {string} response.subMsg - 子返回信息
 * @returns {string} response.data - 返回数据
 * @returns {number} response.total - 总条数
 */
export function batchUpdateVehicleStatus(data) {
  return request({
    url: baseUrl + '/vehicle/batchStatus',
    method: 'post',
    data: data,
  });
}

/**
 * 编辑车辆信息
 * @param {Object} data - 车辆信息
 * @returns {Promise<Object>} 返回结果
 * @returns {string} response.code - 返回码
 * @returns {string} response.msg - 返回信息
 * @returns {string} response.subCode - 子返回码
 * @returns {string} response.subMsg - 子返回信息
 * @returns {string} response.data - 返回数据
 * @returns {number} response.total - 总条数
 */
export function editVehicle(data) {
  return request({
    url: baseUrl + '/vehicle/edit',
    method: 'post',
    data: data,
  });
}

/**
 * 获取车辆详情
 * @param {Object} data - 请求参数
 * @param {string} data.id - 车辆ID
 * @returns {Promise<Object>} 返回结果
 * @returns {string} response.code - 返回码
 * @returns {string} response.msg - 返回信息
 * @returns {string} response.subCode - 子返回码
 * @returns {string} response.subMsg - 子返回信息
 * @returns {Object} response.data - 返回数据
 * @returns {number} response.total - 总条数
 */
export function getVehicleDetail(data) {
  return request({
    url: baseUrl + '/vehicle/detail',
    method: 'post',
    data: data,
  });
}

/**
 * 获取车辆列表分页
 * @param {Object} data - 查询参数
 * @param {number} data.pageNum - 页码，默认1
 * @param {number} data.pageSize - 每页条数，默认10
 * @param {string} [data.licenceNo] - 车牌号
 * @param {string} [data.vin] - 车架号
 * @param {string} [data.enterpriseId] - 车辆所属企业
 * @param {string} [data.status] - 状态
 * @param {string} [data.mobile] - 车主手机号
 * @param {string} [data.ownership] - 车辆归属（ENP：企业；ENP_E：企业员工）
 * @param {number} [data.plugChargeStatus] - 是否启用即插即充状态：（1：是；0：否）
 * @returns {Promise<Object>} 返回结果
 * @returns {string} response.code - 返回码
 * @returns {string} response.msg - 返回信息
 * @returns {string} response.subCode - 子返回码
 * @returns {string} response.subMsg - 子返回信息
 * @returns {Object} response.data - 返回数据
 * @returns {number} response.total - 总条数
 */
export function getVehicleList(data) {
  return request({
    url: baseUrl + '/vehicle/page',
    method: 'post',
    data: data,
  });
}

/**
 * 新增车辆
 * @param {Object} data - 车辆信息
 * @returns {Promise<Object>} 返回结果
 * @returns {string} response.code - 返回码
 * @returns {string} response.msg - 返回信息
 * @returns {string} response.subCode - 子返回码
 * @returns {string} response.subMsg - 子返回信息
 * @returns {string} response.data - 返回数据
 * @returns {number} response.total - 总条数
 */
export function addVehicle(data) {
  return request({
    url: baseUrl + '/vehicle/add',
    method: 'post',
    data: data,
  });
}

// 企业客户不分页查询
export function enterpriseList(data) {
  return request({
    url: baseUrl + '/enterprise/enterpriseList',
    method: 'post',
    data: data,
  });
}

// 根据id查询企业用户不分页
export function queryList(data) {
  return request({
    url: baseUrl + '/enterpriseUser/queryList',
    method: 'post',
    data: data,
  });
}

// 车辆品牌数据
export function carBrandList(data) {
  return request({
    url: baseUrl + '/car/brand',
    method: 'post',
    data: data,
  });
}

// 车辆型号数据
export function carSeriesList(data) {
  return request({
    url: baseUrl + '/car/series',
    method: 'post',
    data: data,
  });
}
