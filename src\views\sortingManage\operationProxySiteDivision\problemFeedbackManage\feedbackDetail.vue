<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <!-- 异常规则管理标签栏内容 -->

    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="manageLoading"
        :tableColumn="manageTableColumn"
        :tableData="manageTableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
      >
        <template slot="defaultHeader">
          <div>
            <div class="card-head">
              <div class="card-head-text">问题反馈</div>
            </div>

            <div class="info-wrap">
              <div class="info-item-wrap">
                <div class="info-title">分成编号：</div>
                <div class="info-detail">
                  <a>{{ detailObj.clearNo || '' }}</a>
                </div>
              </div>
              <div class="info-item-wrap">
                <div class="info-title">分成周期：</div>
                <div class="info-detail">
                  {{ detailObj.clearBegDate || '' }} -
                  {{ detailObj.clearEndDate || '' }}
                </div>
              </div>
              <div class="info-item-wrap">
                <div class="info-title">结算方：</div>
                <div class="info-detail">{{ detailObj.fromObjName || '' }}</div>
              </div>
              <div class="info-item-wrap">
                <div class="info-title">分成方：</div>
                <div class="info-detail">{{ detailObj.toObjName || '' }}</div>
              </div>
              <div class="info-item-wrap">
                <div class="info-title">充电站名称：</div>
                <div class="info-detail">{{ detailObj.stationName || '' }}</div>
              </div>
              <div class="info-item-wrap">
                <div class="info-title">地市：</div>
                <div class="info-detail">{{ detailObj.cityName || '' }}</div>
              </div>
              <div class="info-item-wrap">
                <div class="info-title">分成类型：</div>
                <div class="info-detail">
                  {{ clearTypeFilter(detailObj.clearType || '') }}
                </div>
              </div>
              <div class="info-item-wrap">
                <div class="info-title">分成条款：</div>
                <div class="info-detail">{{ detailObj.ruleClause || '' }}</div>
              </div>
            </div>
            <div class="info-wrap-btns">
              <el-button type="primary" @click="handleChoose()">
                清分明细订单选择
              </el-button>
              <el-button type="primary" plain :disabled="true">
                已选择
                <span style="color: #217aff; font-size: 16px; opacity: 1">
                  {{ manageTableData.length || 0 }}
                </span>
                单
              </el-button>
            </div>
          </div>
        </template>
        <template slot="orderNo" slot-scope="{ row }">
          <el-button type="text">{{ row.orderNo || '' }}</el-button>
        </template>

        <template slot="feedbackContent" slot-scope="{ row }">
          <div class="remark-content" @click="handleEdit(row)">
            <div class="remark-text">{{ row.feedbackContent || '' }}</div>
            <img src="@/assets/order/order-edit.png" alt="" />
          </div>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <div>
            <el-button type="danger" plain @click="deleteRow(row)">
              删除
            </el-button>
          </div>
        </template>
      </BuseCrud>
      <div class="bottom-wrap" v-if="isChecked === '0'">
        <div>
          <el-button @click="handleBack">取消</el-button>
          <el-button
            @click="handleSubmit"
            :loading="submitLoading"
            type="primary"
          >
            提交
          </el-button>
        </div>
      </div>
    </div>
    <editFeedback
      ref="editFeedback"
      :selectOrderRule="selectOrderRule"
      @editFeedbackSave="editFeedbackSave"
    ></editFeedback>
    <chooseOrderList
      ref="chooseOrderList"
      :id="id"
      :manageTableData="manageTableData"
      @chooseOrderSave="chooseOrderSave"
    ></chooseOrderList>
  </div>
</template>

<script>
import {
  clearInfoDetail,
  clearProblemFeedbackDetailInsert,
  clearProblemFeedbackDetailPage,
} from '@/api/sortingManage/operationProxySiteDivision';
import editFeedback from './components/editFeedback';
import chooseOrderList from './components/chooseOrderList';
export default {
  components: { editFeedback, chooseOrderList },
  dicts: [],
  data() {
    return {
      activeName: 'manage',
      manageLoading: false,
      submitLoading: false,
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      id: '',

      manageTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'orderNo',
          title: '订单编号',
          minWidth: 190,
        },
        {
          field: 'orderCreateTime',
          title: '订单下单时间',
          minWidth: 190,
        },
        {
          field: 'chargeBegTime',
          title: '充电开始时间',
          minWidth: 150,
        },
        {
          field: 'chargeEndTime',
          title: '充电结束时间',
          minWidth: 120,
        },
        {
          field: 'chargePower',
          title: '充电电量（KWH）',
          minWidth: 100,
        },
        {
          field: 'chargeFee',
          title: '充电电费（元）',
          minWidth: 150,
        },
        {
          field: 'inCarChargeFee',
          title: '内部车辆充电电费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'outCarChargeFee',
          title: '外部车辆充电电费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'serviceFee',
          title: '充电服务费（元）',
          minWidth: 150,
        },
        {
          field: 'inServiceFee',
          title: '内部车辆充电服务费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'outServiceFee',
          title: '外部车辆充电服务费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'chargeIncome',
          title: '充电总金额（元）',
          minWidth: 150,
        },
        {
          field: 'settleElecFee',
          title: '结算电费（元）',
          minWidth: 150,
        },
        {
          field: 'settleServiceFee',
          title: '结算服务费（元）',
          minWidth: 150,
        },
        {
          field: 'totalSettleFee',
          title: '结算金额（元）',
          minWidth: 150,
        },
        {
          field: 'settleStatus',
          title: '清分状态',
          minWidth: 120,
          fixed: 'right',
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(this.settleStatusList, cellValue);
          },
        },
        {
          field: 'feedbackContent',
          title: '问题描述',
          minWidth: 180,
          slots: { default: 'feedbackContent' },
          align: 'center',
          fixed: 'right',
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 180,
          align: 'center',
          fixed: 'right',
        },
      ],
      manageTableData: [],
      selectOrderRule: {},
      detailObj: {},
      feedbackId: '',
      clearTypeList: [
        { label: '服务费', value: 'service' },
        { label: '电费+服务费', value: 'elecAndService' },
        { label: '场地租金', value: 'space' },
        { label: '场地租金+服务费', value: 'spaceAndService' },
      ],
      settleStatusList: [
        { label: '未清分', value: '0' },
        { label: '已清分', value: '1' },
      ],
      isChecked: '0',
    };
  },

  computed: {
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.id = this.$route.query?.id || '';
    this.feedbackId = this.$route.query?.feedbackId || '';
    this.isChecked = this.$route.query?.isChecked || '0';
    this.queryDeatil();
  },
  methods: {
    handleBack() {
      this.$router.go(-1);
    },
    async handleSubmit() {
      if (this.manageTableData.length === 0) {
        this.$message.error('请先选择清分订单明细！');
        return;
      }
      this.submitLoading = true;
      const [err, res] = await clearProblemFeedbackDetailInsert({
        clearNo: this.detailObj.clearNo || '',
        details: this.manageTableData,
        id: this.detailObj.infoId || '',
      });
      this.submitLoading = false;
      if (err) return;
      this.$message({
        type: 'success',
        message: '反馈成功!',
      });
      this.handleBack();
    },
    clearTypeFilter(cellValue) {
      return this.selectDictLabel(this.clearTypeList, cellValue);
    },
    // 获取详情
    async queryDeatil() {
      const [err, res] = await clearInfoDetail({
        id: this.id || '',
      });

      if (err) return;
      this.detailObj = res?.data || {};
      this.queryFeedbackDetailPage();
    },
    async queryFeedbackDetailPage() {
      const [err, res] = await clearProblemFeedbackDetailPage({
        clearNo: this.detailObj.clearNo || '',
        feedbackId: this.feedbackId || '',
      });

      if (err) return;
      this.manageTableData = res?.data || [];
    },
    chooseOrderSave(data) {
      console.log(data);
      this.manageTableData = [...this.manageTableData, ...data];
    },
    editFeedbackSave(data) {
      this.manageTableData.forEach((item) => {
        if (item.clearDetailId === this.selectOrderRule.clearDetailId) {
          item.feedbackContent = data;
        }
      });
      this.$forceUpdate();
    },
    handleChoose() {
      this.$refs.chooseOrderList.dialogVisible = true;
    },
    handleEdit(row) {
      this.selectOrderRule = row;
      this.$refs.editFeedback.dialogVisible = true;
    },
    deleteRow(row) {
      this.$confirm(`'确定删除订单编号：${row?.orderNo || ''}吗？'`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const INDEX = this.manageTableData.findIndex((item) => {
          return item.orderNo === row?.orderNo;
        });
        this.manageTableData.splice(INDEX, 1);
        this.$message({
          type: 'success',
          message: '删除成功!',
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-tabs {
  margin-top: 16px;
  background-color: #f5f6f9;
  .el-tabs__header {
    padding-left: 0;
    display: flex;
    justify-content: center;
    text-align: center;
    margin-bottom: 1px;
    .el-tabs__item {
      padding: 0;
      width: 164px;
      font-size: 18px;
      font-weight: 400;
      background-color: #fff;
    }
    .el-tabs__item.is-active {
      background-color: #1677fe;
      color: #fff;
    }
    .el-tabs__nav-scroll {
      border-radius: 25px;
      border: solid 1px #dfe1e5;
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__nav-wrap::after {
      width: 0;
    }
  }
}

.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }
  margin: 16px;

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
  }
}
.info-wrap {
  padding: 0 0 0 12px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin: 24px 0 0;

  .info-item-wrap {
    display: flex;
    flex: 1 1 25%;
    margin-bottom: 24px;
    .info-title {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #505363;
    }
    .info-detail {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #292b33;
      .price-number {
        color: #ff8d24;
        font-weight: 500;
      }
      a {
        color: #217aff;
        text-decoration: underline;
      }
    }
  }
}
.remark-content {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  cursor: pointer;
  .remark-text {
    color: #292b33;
    max-width: 138px;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    margin-right: 4px;
    white-space: nowrap; /* 禁止换行 */
    overflow: hidden; /* 隐藏超出部分 */
    text-overflow: ellipsis; /* 超出部分显示为省略号 */
  }
  img {
    width: 16px;
    height: 16px;
  }
}
.info-wrap-btns {
  margin-bottom: 16px;
}
::v-deep .menu-box .el-button {
  color: #217aff;
  border-color: #217aff;
}

.bottom-wrap {
  // position: fixed;
  // bottom: 0;
  // left: 0;
  // width: 100%;
  // height: 86px;
  margin-top: 30px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 32px;
  box-sizing: border-box;
}
</style>
