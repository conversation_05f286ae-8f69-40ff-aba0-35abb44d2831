FROM registry.cn-hangzhou.aliyuncs.com/leo_library/node:16.12.0 as build
WORKDIR /tmp
COPY . .
# 设置私有源
RUN npm config set @bangdao:registry https://registry.npmmirror.com
RUN npm cache verify
RUN npm cache clean -f
RUN npm config get --registry
# 安装指定版本
RUN npm install
RUN npm run build:test
## run with express
## EXPOSE 3000
## CMD ["npm", "run", "express:run"] 
FROM registry.cn-hangzhou.aliyuncs.com/leo_library/nginx:1.12.3-gzip
WORKDIR /usr/share/nginx/html/v2g-charging-web
RUN rm -f *
COPY --from=build /tmp/dist .