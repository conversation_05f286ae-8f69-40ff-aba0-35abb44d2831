<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <div class="device-head">
      <img
        src="@/assets/charge/price-period-title-icon.png"
        class="device-head-icon"
      />

      <div class="device-info-wrap">
        <div class="device-title-wrap">
          <div class="device-title">活动名称：周年庆活动</div>
          <!-- <div class="device-status">{{ status }}</div> -->
        </div>
        <div class="device-info-wrap">
          <el-row>
            <el-col :span="8">
              <span class="label">活动ID：</span>
              <span class="value">{{ creator }}</span>
            </el-col>
            <el-col :span="8">
              <span class="label">创建人：</span>
              <span class="value">{{ createTime }}</span>
            </el-col>
            <el-col :span="8">
              <span class="label">创建时间：</span>
              <span class="value">{{ createTime }}</span>
            </el-col>
          </el-row>
        </div>
      </div>
      <el-button
        type="primary"
        style="background: #1ab2ff; border-color: #1ab2ff"
        @click="activeRulerShow"
      >
        活动规则
      </el-button>
      <el-button type="primary" @click="drawer = true">审核轨迹</el-button>
    </div>

    <div class="info-card">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">基础信息</div>
      </div>

      <div class="form-wrap">
        <el-row :gutter="20">
          <el-col :span="7" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">展示标题：</div>
              <div class="info-detail">周年庆活动</div>
            </div>
          </el-col>
          <el-col :span="7" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">活动类型：</div>
              <div class="info-detail">定向发券活动</div>
            </div>
          </el-col>
          <el-col :span="10" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">活动有效时间：</div>
              <div class="info-detail">
                2021-10-26 15:10:05 ～ 2021-10-26 15:10:05
              </div>
            </div>
          </el-col>
          <el-col :span="7" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">活动承办单位：</div>
              <div class="info-detail">湖南省电动</div>
            </div>
          </el-col>
          <el-col :span="7" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">活动区域：</div>
              <div class="info-detail">湖南省长沙市</div>
            </div>
          </el-col>
          <el-col :span="24" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">活动描述：</div>
              <div class="info-detail">
                周年庆活动信息信息内容信息内容，周年庆活动信息信息内容信息内容周年庆活动信息信息内容信息内容周年庆活动信息信息内容信息内容
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="info-card">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">奖品配置</div>
      </div>

      <div class="form-wrap">
        <BuseCrud
          style="margin-bottom: 16px"
          ref="periodInfo"
          :tableColumn="tableColumn"
          :tableData="tableData"
          :modalConfig="{ addBtn: false, menu: false }"
        ></BuseCrud>
      </div>
    </div>

    <div class="info-card">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">审核信息</div>
      </div>

      <div class="form-wrap">
        <el-form :model="form" :rules="rules" ref="form" label-position="top">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="审核结果"
                prop="result"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="form.result"
                  placeholder="请选择审核结果"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in resultList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="审核意见"
                prop="remark"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="form.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入审核意见"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="bottom-wrap">
      <el-button @click="() => handleCancel()">取消</el-button>
      <el-button type="primary" @click="() => handleConfirm()">提交</el-button>
    </div>

    <el-drawer :visible.sync="drawer" :with-header="false" :size="825">
      <div class="draw-wrap">
        <div class="draw-card-head">
          <div class="card-head-text">审核轨迹</div>
          <div class="card-head-close" @click="onClickCloseDrawer"></div>
        </div>
        <div class="draw-card-head-after"></div>

        <div class="card-head" style="margin-bottom: 8px">
          <div class="before-icon"></div>
          <div class="card-head-text">申请信息</div>
        </div>

        <div class="approval-steps">
          <el-steps direction="vertical" :active="3" space="100px">
            <el-step v-for="(item, index) in examineList" :key="index">
              <!-- 自定义步骤图标 -->
              <template #icon>
                <div :class="`step-icon step-icon-${item.status}`">
                  {{ index + 1 }}
                </div>
              </template>

              <!-- 自定义步骤内容 -->
              <template #title>
                <div class="step-header">
                  <span class="title">{{ item.title }}</span>
                </div>
              </template>

              <template #description>
                <div
                  class="remark-box"
                  v-if="index !== 3 && item.status !== '05'"
                >
                  <div class="remark-icon"></div>
                  <!-- 处理人信息 -->
                  <div v-if="item.name" class="person-info">
                    <div class="person-title">
                      <span>{{ item.name }}</span>
                      <span class="identity">{{ item.identity }}</span>
                      <span>{{ item.department }}</span>
                    </div>

                    <div
                      :class="`person-info-status person-info-status-${item.status} `"
                    >
                      {{ statusObj[item.status] }}
                    </div>

                    <div v-if="item.remark" class="remark">
                      <span>审核意见：</span>
                      <span>{{ item.remark }}</span>
                    </div>
                  </div>

                  <div class="remark-time">
                    {{ item.time }}
                  </div>
                </div>
              </template>
            </el-step>
          </el-steps>
        </div>
      </div>
    </el-drawer>
    <activeRuler ref="activeRuler"></activeRuler>
  </div>
</template>

<script>
import activeRuler from '../components/activeRuler';
export default {
  components: { activeRuler },
  dicts: [],
  data() {
    return {
      pricePeriodId: '21231231231231',
      status: '审核中',
      creator: '张三',
      createTime: '2022-12-12 12:12:12',

      label: {
        pricePeriodName: '电价时段名称',
        createUnit: '创建单位',
        applicableArea: '适用区域',
      },
      timeList: [
        {
          time: '2024-01',
          data: [
            {
              type: '尖时段',
              startTime: '00:00',
              endTime: '02:00',
            },
            {
              type: '峰时段',
              startTime: '02:00',
              endTime: '06:00',
            },
            {
              type: '平时段',
              startTime: '06:00',
              endTime: '08:00',
            },
            {
              type: '谷时段',
              startTime: '08:00',
              endTime: '00:00',
            },
          ],
        },
        {
          time: '2024-02',
          data: [
            {
              type: '尖时段',
              startTime: '00:00',
              endTime: '02:00',
            },
            {
              type: '峰时段',
              startTime: '02:00',
              endTime: '06:00',
            },
            {
              type: '平时段',
              startTime: '06:00',
              endTime: '08:00',
            },
            {
              type: '谷时段',
              startTime: '08:00',
              endTime: '00:00',
            },
            {
              type: '尖时段',
              startTime: '10:00',
              endTime: '12:00',
            },
          ],
        },
        {
          time: '2024-03',
          data: [
            {
              type: '尖时段',
              startTime: '00:00',
              endTime: '02:00',
            },
            {
              type: '峰时段',
              startTime: '02:00',
              endTime: '06:00',
            },
            {
              type: '平时段',
              startTime: '06:00',
              endTime: '08:00',
            },
            {
              type: '谷时段',
              startTime: '08:00',
              endTime: '00:00',
            },
            {
              type: '尖时段',
              startTime: '10:00',
              endTime: '12:00',
            },
            {
              type: '谷时段',
              startTime: '12:00',
              endTime: '15:00',
            },
          ],
        },
      ],
      chooseTime: '',

      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'type',
          title: '优惠券编码',
          minWidth: 120,
        },
        {
          field: 'startTime',
          title: '优惠券名称',
          minWidth: 120,
        },
        {
          field: 'endTime',
          title: '投放库存',
          minWidth: 120,
        },
      ],

      tableData: [],

      form: {
        result: '',
        remark: '',
      },
      rules: {
        result: [
          { required: true, message: '请选择审核结果', trigger: 'blur' },
        ],
      },
      formLabelWidth: '120px',

      resultList: [
        { label: '审核通过', value: '01' },
        { label: '审核不通过', value: '02' },
      ],

      drawer: false, // 审核轨迹抽屉

      examineList: [
        {
          title: '发起人',
          name: '张三',
          identity: '项目经理',
          department: '研发技术中心',
          status: '01',
          time: '2021-01-01 12:00:00',
        },
        {
          title: '部门初审',
          name: '李四',
          identity: '项目经理1',
          department: '研发技术中心',
          status: '02',
          remark: '描述描述描述内容信息信息',
          time: '2021-01-01 12:00:00',
        },
        {
          title: '部门复审',
          name: '王五',
          identity: '项目经理1',
          department: '研发技术中心',
          status: '04',
          // remark: '描述描述描述内容信息信息',
          time: '2021-01-01 12:00:00',
        },
        {
          title: '流程结束',
          status: '05',
        },
      ], // 审核轨迹列表

      statusObj: {
        '01': '已提交',
        '02': '已通过',
        '03': '已拒绝',
        '04': '处理中',
        '05': '等待中',
      }, // 审核状态对象

      statusTagType: {
        '01': 'info',
        '02': 'success',
        '03': 'danger',
        '04': 'warning',
        '05': 'info',
      },
    };
  },

  computed: {},
  mounted() {
    this.chooseTime = this.timeList[0].time;
    this.tableData = this.timeList[0].data;
  },
  methods: {
    activeRulerShow() {
      this.$refs.activeRuler.dialogVisible = true;
    },
    changeTime() {
      this.tableData = this.timeList.find(
        (item) => item.time === this.chooseTime
      ).data;
    },

    // 关闭抽屉
    onClickCloseDrawer() {
      this.drawer = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.device-head {
  background-color: #fff;

  display: flex;
  height: 112px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  box-sizing: border-box;
  .device-head-icon {
    width: 48px;
    height: 48px;
    margin-right: 24px;
  }
  .device-info-wrap {
    flex: 1;
    .device-title-wrap {
      height: 32px;
      display: flex;
      align-items: center;
      .device-title {
        font-weight: 500;
        font-size: 24px;
        color: #12151a;
      }
      .device-status {
        // width: 50px;
        padding: 0 10px;
        height: 24px;
        border-radius: 10px 0 10px 0;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: center;
        color: #fff;
        background: linear-gradient(321.01deg, #ffb624 8.79%, #ff8d24 100.27%);
        margin-left: 12px;
      }
    }
    .device-info-wrap {
      height: 16px;
      margin-top: 16px;
      font-size: 16px;
      font-weight: 400;
      color: #292b33;
    }
  }
  .device-status-wrap {
    display: flex;
    align-items: center;
    .device-status-item-wrap {
      width: 150px;
      .device-status-item-title {
        font-weight: 400;
        font-size: 14px;
        line-height: 14px;
        color: #505363;
        margin: 0 auto 12px auto;
        text-align: center;
      }
      .device-status {
        width: 86px;
        height: 34px;
        border-radius: 4px;
        display: flex;
        margin: 0 auto;
        align-items: center;
        justify-content: center;
        background-color: #ebf3ff;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #217aff;
      }
      .device-status-success {
        width: 86px;
        height: 34px;
        border-radius: 4px;
        display: flex;
        margin: 0 auto;
        align-items: center;
        justify-content: center;
        background-color: #ebfff1;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #00c864;
      }
    }
    .device-status-split {
      width: 1px;
      height: 36px;
      background-color: #e9ebf0;
    }
  }
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    background: linear-gradient(180deg, #e9f2ff 0%, #ffffff 100%);
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
        background-color: #1ab2ff;
        border-color: #1ab2ff;
      }
      ::v-deep .el-button--small {
        font-size: 14px;
      }
      .distribution {
        margin-left: 24px;
        margin-right: 24px;
        display: flex;
        align-items: center;
      }
    }
  }

  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
    padding: 0 16px 0 16px;
    .info-title {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #505363;
    }
    .info-detail {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #292b33;
      word-break: break-all;
    }
    .info-amount {
      height: 28px;
      background-color: #fff7e6;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 8px;
      font-weight: 400;
      font-size: 20px;
      color: #fe8921;
      margin-top: -6px;
      margin-right: 4px;
    }
    .info-img {
      width: 140px;
      height: 140px;
    }
  }
}

::v-deep .bd3001-content {
  padding: 0;
}

.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .bottom-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
  }
}

.draw-wrap {
  .draw-card-head {
    // position: relative;
    height: 82px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    // margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 24px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }

    .card-head-close {
      width: 24px;
      height: 24px;
      background-image: url('~@/assets/station/drawer-close.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }

  .draw-card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .card-head {
    height: 18px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-bottom: 32px;
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }
  .approval-steps {
    padding: 20px;
    background: #fff;
    border-radius: 4px;
  }

  :deep(.el-step__head) {
    padding-bottom: 10px;
  }

  :deep(.el-step__title) {
    line-height: 1.5;
    max-width: 600px;
  }
  ::v-deep .el-step__icon {
    border: 0px;
  }

  .step-icon {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .step-icon-01 {
    background-color: #ebf3ff;
    border-radius: 50%;
    color: #217aff;
  }

  .step-icon-02 {
    background-color: #ebf3ff;
    border-radius: 50%;
    color: #217aff;
  }
  .step-icon-03 {
    background-color: #fc1e31;
    border-radius: 50%;
    color: #fff;
  }
  .step-icon-04 {
    background-color: #217aff;
    border-radius: 50%;
    color: #fff;
  }
  .step-icon-05 {
    background-color: #f3f6fc;
    border-radius: 50%;
    color: #818496;
  }
  .step-header {
    display: flex;
    align-items: center;
    //   margin-bottom: 8px;
  }

  .title {
    margin-right: 12px;
    font-weight: 400;
    font-size: 16px;
    color: #12151a;
  }

  .status-tag {
    margin-right: 12px;
  }

  .time {
    color: #999;
    font-size: 12px;
    margin-left: auto;
  }

  .remark-box {
    margin-top: 8px;
    padding: 16px 12px 16px 16px;
    background: #f9f9fb;
    border-radius: 5px;
    display: flex;
    margin-bottom: 32px;
    margin-right: 12px;
    .remark-icon {
      width: 48px;
      height: 48px;
      background-image: url('~@/assets/station/drawer-icon.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 16px;
    }
    .person-info {
      font-weight: 400;
      font-size: 16px;
      line-height: 20px;
      flex: 1;
      .person-title {
        color: #292b33;
        .identity {
          margin: 0 8px;
        }
      }
      .person-info-status {
        font-weight: 400;
        font-size: 16px;
        line-height: 20px;
        margin: 10px 0 0 0;
      }
      .person-info-status-01 {
        color: #00c864;
      }
      .person-info-status-02 {
        color: #00c864;
      }
      .person-info-status-03 {
        color: #fc1e31;
      }
      .person-info-status-04 {
        color: #217aff;
      }

      .remark {
        font-weight: 400;
        font-size: 16px;
        line-height: 20px;
        color: #292b33;
        margin-top: 16px;
      }
    }

    .remark-time {
      font-weight: 400;
      font-size: 16px;
      line-height: 20px;
      color: #292b33;
      margin-top: 15px;
    }
  }

  .remark-label {
    color: #666;
    margin-right: 6px;
  }

  .remark-text {
    color: #999;
  }

  .processing {
    animation: rotating 2s linear infinite;
  }

  @keyframes rotating {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}
</style>
