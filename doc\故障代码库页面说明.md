# 故障代码库页面说明

## 概述

故障代码库页面是运维配置模块下的一个子页面，用于管理系统中的故障代码信息。该页面基于BuseCrud组件构建，提供了完整的CRUD（增删改查）功能。

## 功能特性

### 1. 数据展示
- **表格展示**：使用BuseCrud组件展示故障代码列表
- **分页功能**：支持数据分页显示，默认每页10条记录
- **加载状态**：数据加载时显示loading动画

### 2. 筛选功能
页面提供4个筛选条件：
- **故障代码**：输入框，支持模糊搜索
- **故障类型**：下拉选择（告警/故障）
- **故障等级**：下拉选择（严重/普通）
- **状态**：下拉选择（启用/停用）

### 3. 表格列配置
| 列名 | 字段名 | 说明 | 特殊处理 |
|------|--------|------|----------|
| 序号 | - | 自动序号 | - |
| 故障代码 | faultCode | 故障代码标识 | - |
| 故障类型 | faultType | 故障分类 | 彩色标签显示 |
| 故障描述 | faultDescription | 故障详细描述 | - |
| 故障原因 | faultReason | 故障产生原因 | - |
| 故障等级 | faultLevel | 故障严重程度 | 彩色标签显示 |
| 状态 | status | 启用/停用状态 | 彩色标签显示 |
| 创建时间 | createTime | 记录创建时间 | - |
| 操作 | - | 操作按钮 | 查看/编辑/删除 |

### 4. 标签颜色配置
- **故障类型**：
  - 告警：warning（橙色）
  - 故障：danger（红色）
- **故障等级**：
  - 严重：danger（红色）
  - 普通：primary（蓝色）
- **状态**：
  - 启用：success（绿色）
  - 停用：info（灰色）

### 5. 操作功能
- **新增**：点击"新增"按钮打开表单对话框
- **查看**：查看故障代码详细信息
- **编辑**：修改故障代码信息
- **删除**：删除故障代码记录（需确认）

### 6. 表单验证
新增/编辑表单包含以下验证规则：
- 故障代码：必填
- 故障类型：必选
- 故障描述：必填
- 故障原因：必填
- 故障等级：必选
- 状态：必选

## 技术实现

### 组件依赖
- **BuseCrud**：主要的表格组件
- **Element UI**：UI组件库（按钮、表单、对话框等）

### 数据结构
```javascript
// 故障代码数据结构
{
  id: Number,           // 唯一标识
  faultCode: String,    // 故障代码
  faultType: String,    // 故障类型（告警/故障）
  faultDescription: String, // 故障描述
  faultReason: String,  // 故障原因
  faultLevel: String,   // 故障等级（严重/普通）
  status: String,       // 状态（启用/停用）
  createTime: String    // 创建时间
}
```

### 关键方法
- `loadData()`：加载表格数据
- `handleAdd()`：新增故障代码
- `handleEdit(row)`：编辑故障代码
- `handleDelete(row)`：删除故障代码
- `handleSubmit()`：提交表单数据
- `getFaultTypeColor(type)`：获取故障类型标签颜色
- `getFaultLevelColor(level)`：获取故障等级标签颜色

## 样式说明

### CSS类名
- `.container`：页面容器
- `.table-wrap`：表格包装器
- `.card-head`：表格头部区域
- `.card-head-text`：标题文字
- `.top-button-wrap`：按钮组容器
- `.dialog-footer`：对话框底部

### 响应式设计
- 表格支持横向滚动
- 操作列固定在右侧
- 按钮组在小屏幕下自动换行

## 使用说明

1. **查看数据**：页面加载后自动显示故障代码列表
2. **筛选数据**：使用顶部筛选条件快速查找目标数据
3. **新增记录**：点击"新增"按钮，填写表单信息后提交
4. **编辑记录**：点击操作列的"编辑"按钮，修改信息后保存
5. **删除记录**：点击操作列的"删除"按钮，确认后删除

## 注意事项

1. 当前使用模拟数据，实际使用时需要替换为真实的API调用
2. 删除操作不可恢复，请谨慎操作
3. 表单验证确保数据完整性，必填字段不能为空
4. 状态标签颜色遵循系统设计规范
