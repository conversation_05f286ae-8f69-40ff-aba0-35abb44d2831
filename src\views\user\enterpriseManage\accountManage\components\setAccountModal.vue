<template>
    <el-dialog title="账户设置" :visible.sync="dialogVisible"  width="630px">
        <el-form :model="form" :rules="rules" ref="accountForm"  label-position="top">
            <div class="company-wrap">
                <div class="company-brief">企业名称：</div>
                <div class="company-name">{{ companyName }}</div>
            </div>
            <div class="balance-wrap">
                <div class="balance-item">
                    <div class="balance-label">企业余额（元）：</div>
                    <div class="balance-value">{{ companyBalance }}</div>
                </div>
                <div class="balance-item">
                    <div class="balance-label">可用余额（元）：</div>
                    <div class="balance-value">{{  useableBalance }}</div>
                </div>
            </div>

            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item
                        label="信用额度："
                        prop="creditLimit"
                        :label-width="formLabelWidth"
                    >
                    <div style="display: flex;">
                        <el-input-number
                           v-model="form.creditLimit"
                            placeholder="最多保留两位小数"
                            :min="0"
                            :precision="2"
                            :step="0.01"
                            :controls="false"
                            style="width: 100%;"
                        >
                        </el-input-number>
                        <div style="margin-left: 8px;">元</div>
                    </div>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="安全额度："
                        prop="safeLimit"
                        :label-width="formLabelWidth"
                    >
                    <div style="display: flex;">
                        <el-input-number
                           v-model="form.safeLimit"
                            placeholder="最多保留两位小数"
                            :min="0"
                            :precision="2"
                            :step="0.01"
                            :controls="false"
                            style="width: 100%;"
                        >
                        </el-input-number>
                        <div style="margin-left: 8px;">元</div>
                    </div>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="备注"
                        prop="remark"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.remark"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入备注"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form> 

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
    </el-dialog>
   
</template>
<script>  

import _ from 'lodash';

import {
    enterpriseSet
} from '@/api/user/enterprise'

  export default {
    props: {
        editInfo: {
            type: Object,
            default: () => {},
        },
    },
    dicts: [
        'ls_order_except_level',
        'ls_order_except_type',
    ],
    components: {
        
    },
    data() {
        return {
            dialogVisible: false,
            companyName: '',
            userId: '',

            companyBalance: '',
            useableBalance: '',

            formLabelWidth: '120px',
            form: {
                creditLimit: '',
                safeLimit: '',
                remark: '',
            },
            rules: {
                creditLimit: [
                    { required: true, message: '请输入信用额度', trigger: 'blur' },
                ],
                safeLimit: [
                    { required: true, message: '请输入安全额度', trigger: 'blur' },
                ],
            }
        };
    },
    watch: {
        
    },
    computed: {
        
    },
    mounted() {
       
    },
    methods: {
        // 新增按钮防抖        
        handleConfirm: _.debounce(function() {
            this.$refs.accountForm.validate(async (valid) => {
                if (valid) {
                    console.log(this.form, 'this.form')
                    const {
                        creditLimit,
                        safeLimit,
                        remark,
                    } = this.form;
                    
                    const params = {
                        userId: this.userId,
                        creditLimit,
                        balanceThreshold: safeLimit,
                        remark,
                    }
                    const [err, res] = await enterpriseSet(params);
                    if (err) return;
                    
                    this.$message.success('设置成功');
                    this.dialogVisible = false;
                    this.userId  = '';
                    this.handleResetData()
                    this.$emit('loadData');
                    
                }
            });
        }, 300) ,

        handleCancel() {
            this.dialogVisible = false;
            this.handleResetData()
        },

        handleResetData() {
            this.form =  {
                creditLimit: '',
                safeLimit: '',
                remark: '',
            }
        }

    },
  };
  </script>

<style lang="scss" scoped>
.bottom-button-wrap {
    height: 86px;
    // margin-top: 46px;
    width: 100%;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    // padding-right: 32px;
    box-sizing: border-box;
}

.company-wrap {
    height: 20px;
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 20px;
    .company-brief {
        color: #505363
    }
    .company-name {
        color: #12151A;
    }
}
.balance-wrap {
    height: 40px;
    width: 100%;
    margin-top: 24px;
    padding: 0 16px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    background-color: #EBF3FF;
    margin-bottom: 24px;
    .balance-item {
        flex: 1;
        display: flex;
        align-items: center;
        .balance-label {
            font-weight: 400;
            font-size: 16px;
            color: #505363;
        }
        .balance-value {
            font-weight: 400;
            font-size: 16px;
            color: #12151A  ;
        }
    }
   

}
  </style>
  