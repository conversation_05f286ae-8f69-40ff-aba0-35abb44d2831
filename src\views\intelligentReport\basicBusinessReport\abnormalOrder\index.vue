<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">未纳入统计的异常订单</div>
            <div class="top-button-wrap">
              <el-button
                type="primary"
                icon="el-icon-download"
                @click="handleDownload"
              >
                导出
              </el-button>
            </div>
          </div>
          <div class="card-head-after"></div>
        </template>
      </BuseCrud>
    </div>
  </div>
</template>

<script>
import { getStationList } from '@/api/pile/index';
import { getAbnormalOrderList } from '@/api/order/index';

export default {
  dicts: [
    'ls_charge_user_type', // 用户类型
    'ls_charge_order_status', // 订单状态
    'ls_order_except_type', //  异常类型
    'ls_order_except_level', // 异常等级
    'ls_except_order_transact_satus', // 处理状态
    'ls_order_apply_mode', // 下单渠道
    'ls_order_source', // 订单来源
    'ls_pile_in_type', // 桩接入方式
    'ls_pile_source', // 桩来源
  ],
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'orderId',
          title: '订单编号',
          minWidth: 200,
        },
        {
          field: 'partOrderNo',
          title: '第三方订单编号',
          minWidth: 200,
          slots: {
            default: ({ row }) => [
              <el-tooltip
                content={row.partOrderNo}
                placement="top"
                disabled={!row.partOrderNo || row.partOrderNo.length < 15}
              >
                <span class="ellipsis-text">{row.partOrderNo}</span>
              </el-tooltip>,
            ],
          },
        },
        {
          field: 'stationName',
          title: '充电站',
          minWidth: 160,
        },
        {
          field: 'pileName',
          title: '充电桩',
          minWidth: 160,
        },
        {
          field: 'applyMode',
          title: '下单渠道',
          minWidth: 140,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_order_apply_mode,
              cellValue
            );
          },
        },
        {
          field: 'orderSource',
          title: '订单来源',
          minWidth: 140,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_order_source,
              cellValue
            );
          },
        },
        {
          field: 'pileInType',
          title: '桩接入方式',
          minWidth: 140,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_pile_in_type,
              cellValue
            );
          },
        },
        {
          field: 'pileSource',
          title: '桩来源',
          minWidth: 140,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_pile_source,
              cellValue
            );
          },
        },
        {
          field: 'userType',
          title: '用户类型',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charge_user_type,
              cellValue
            );
          },
        },
        {
          field: 'mobile',
          title: '用户手机号',
          minWidth: 140,
        },
        {
          field: 'enterpriseNo',
          title: '所属企业编号',
          minWidth: 160,
        },
        {
          field: 'applyTime',
          title: '下单时间',
          minWidth: 200,
        },
        {
          field: 'bgnTime',
          title: '修正前充电开始时间',
          minWidth: 200,
        },
        {
          field: 'bgnTimeAfterC',
          title: '修正后充电开始时间',
          minWidth: 200,
        },
        {
          field: 'endTime',
          title: '修正前充电结束时间',
          minWidth: 200,
        },
        {
          field: 'endTimeAfterC',
          title: '修正后充电结束时间',
          minWidth: 200,
        },
        {
          field: 'reportTime',
          title: '异常上报时间',
          minWidth: 200,
        },
        {
          field: 'chargeStatus',
          title: '充电状态',
          minWidth: 200,
        },
        {
          field: 'orderStatus',
          title: '订单状态',
          minWidth: 200,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charge_order_status,
              cellValue
            );
          },
        },
        {
          field: 'exceptLevel',
          title: '异常等级',
          minWidth: 200,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_order_except_level,
              cellValue
            );
          },
        },
        {
          field: 'exceptType',
          title: '异常类型',
          minWidth: 200,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_order_except_type,
              cellValue
            );
          },
        },
        {
          field: 'exceptId',
          title: '异常编号',
          minWidth: 260,
        },
        {
          field: 'exceptName',
          title: '异常名称',
          minWidth: 220,
        },
        {
          field: 'exceptDescription',
          title: '异常描述',
          minWidth: 300,
        },
        {
          field: 'chargePq',
          title: '修正前充电电量(kwh)',
          minWidth: 200,
        },
        {
          field: 'chargePqAfterC',
          title: '修正后充电电量(kwh)',
          minWidth: 200,
        },
        {
          field: 'tmr',
          title: '修正前抄表电量(kwh)',
          minWidth: 200,
        },
        {
          field: 'tmrAfterC',
          title: '修正后抄表电量(kwh)',
          minWidth: 200,
        },
        {
          field: 'elecAmt',
          title: '修正前充电电费(元)',
          minWidth: 200,
        },
        {
          field: 'elecAmtAfterC',
          title: '修正后充电电费(元)',
          minWidth: 200,
        },
        {
          field: 'serviceAmt',
          title: '修正前充电服务费(元)',
          minWidth: 200,
        },
        {
          field: 'serviceAmtAfterC',
          title: '修正后充电服务费(元)',
          minWidth: 200,
        },
        {
          field: 'chargeAmt',
          title: '修正前充电总金额(元)',
          minWidth: 200,
        },
        {
          field: 'chargeAmtAfterC',
          title: '修正后充电总金额(元)',
          minWidth: 200,
        },
        {
          field: 'electricityPriceDiscounted',
          title: '修正前优惠电费(元)',
          minWidth: 200,
        },
        {
          field: 'electricityPriceDiscountedAfterC',
          title: '修正后优惠电费(元)',
          minWidth: 200,
        },
        {
          field: 'servicePriceDiscounted',
          title: '修正前优惠服务费(元)',
          minWidth: 200,
        },
        {
          field: 'servicePriceDiscountedAfterC',
          title: '修正后优惠服务费(元)',
          minWidth: 200,
        },
        {
          field: 'discountedPrice',
          title: '修正前优惠金额(元)',
          minWidth: 200,
        },
        {
          field: 'discountedPriceAfterC',
          title: '修正后优惠金额(元)',
          minWidth: 200,
        },
        {
          field: 'electricityPrice',
          title: '修正前实扣电费(元)',
          minWidth: 200,
        },
        {
          field: 'electricityPriceAfterC',
          title: '修正后实扣电费(元)',
          minWidth: 200,
        },
        {
          field: 'servicePrice',
          title: '修正前实扣服务费(元)',
          minWidth: 200,
        },
        {
          field: 'servicePriceAfterC',
          title: '修正后实扣服务费(元)',
          minWidth: 200,
        },
        {
          field: 'realPrice',
          title: '修正前实扣金额(元)',
          minWidth: 200,
        },
        {
          field: 'realPriceAfterC',
          title: '修正后实扣金额(元)',
          minWidth: 200,
        },
        {
          field: 'status',
          title: '处理状态',
          minWidth: 100,
          fixed: 'right',
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_except_order_transact_satus,
              cellValue
            );
          },
        },
        {
          field: 'transactor',
          title: '处理人',
          minWidth: 120,
        },
        {
          field: 'transactTime',
          title: '处理时间',
          minWidth: 180,
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        stationId: '',
        pileId: '',
        orderId: '',
        partOrderNo: '',
        mobile: '',
        userType: '',
        enterpriseNo: '',
        orderTime: [],
        orderStatus: '',
        exceptType: '',
        exceptLevel: '',
        transactStatus: '',
        reportTime: '',
        processingTime: '',
        applyMode: '',
        orderSource: '',
        chargingStartTime: [],
        chargingStartTimeAfterC: [],
        chargingEndTime: [],
        chargingEndTimeAfterC: [],
        pileInType: '',
        pileSource: '',
      },
      stationList: [],
      stationLoading: false,
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'stationId',
            title: '充电站',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.stationList,
              filterable: true,
              remote: true,
              remoteMethod: this.debouncedStationSearch,
              loading: this.stationLoading,
            },
          },
          {
            field: 'pileId',
            title: '充电桩',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              // todo 新增充电桩接口
              options: [],
            },
          },
          {
            field: 'orderId',
            title: '订单编号',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'partOrderNo',
            title: '第三方订单编号',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'mobile',
            title: '用户手机号',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'userType',
            title: '用户类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charge_user_type,
            },
          },
          {
            field: 'enterpriseNo',
            title: '企业编号',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'orderTime',
            title: '下单时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              rangeSeparator: '至',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-DD HH:mm:ss',
              defaultTime: ['00:00:00', '23:59:59'],
            },
          },
          {
            field: 'orderStatus',
            title: '订单状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charge_order_status,
            },
          },
          {
            field: 'exceptType',
            title: '异常类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_order_except_type,
            },
          },
          {
            field: 'exceptLevel',
            title: '异常等级',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_order_except_level,
            },
          },
          {
            field: 'transactStatus',
            title: '处理状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_except_order_transact_satus,
            },
          },
          {
            field: 'reportTime',
            title: '上报时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              options: [],
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'processingTime',
            title: '处理时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              options: [],
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'applyMode',
            title: '下单渠道',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_order_apply_mode,
            },
          },
          {
            field: 'orderSource',
            title: '订单来源',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_order_source,
            },
          },
          {
            field: 'chargingStartTime',
            title: '修正前充电开始时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              rangeSeparator: '至',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-DD HH:mm:ss',
              defaultTime: ['00:00:00', '23:59:59'],
            },
          },
          {
            field: 'chargingStartTimeAfterC',
            title: '修正后充电开始时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              rangeSeparator: '至',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-DD HH:mm:ss',
              defaultTime: ['00:00:00', '23:59:59'],
            },
          },
          {
            field: 'chargingEndTime',
            title: '修正前充电结束时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              rangeSeparator: '至',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-DD HH:mm:ss',
              defaultTime: ['00:00:00', '23:59:59'],
            },
          },
          {
            field: 'chargingEndTimeAfterC',
            title: '修正后充电结束时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              rangeSeparator: '至',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-DD HH:mm:ss',
              defaultTime: ['00:00:00', '23:59:59'],
            },
          },
          {
            field: 'pileInType',
            title: '桩接入方式',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_pile_in_type,
            },
          },
          {
            field: 'pileSource',
            title: '桩来源',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_pile_source,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    console.log('this.$route', this.$route.query);
    this.loadData();
  },
  methods: {
    async loadData() {
      console.log('parms', this.params);
      let applyTimeStart = '';
      let applyTimeEnd = '';
      if (this.params.orderTime && this.params.orderTime.length > 0) {
        applyTimeStart = this.params.orderTime[0];
        applyTimeEnd = this.params.orderTime[1];
      }
      let reportTimeStart = '';
      let reportTimeEnd = '';
      if (this.params.reportTime && this.params.reportTime.length > 0) {
        reportTimeStart = this.params.reportTime[0];
        reportTimeEnd = this.params.reportTime[1];
      }
      let transactTimeStart = '';
      let transactTimeEnd = '';
      if (this.params.processingTime && this.params.processingTime.length > 0) {
        transactTimeStart = this.params.processingTime[0];
        transactTimeEnd = this.params.processingTime[1];
      }
      let bgnTimeStart = '';
      let bgnTimeEnd = '';
      if (
        this.params.chargingStartTime &&
        this.params.chargingStartTime.length > 0
      ) {
        bgnTimeStart = this.params.chargingStartTime[0];
        bgnTimeEnd = this.params.chargingStartTime[1];
      }

      let endTimeStart = '';
      let endTimeEnd = '';
      if (
        this.params.chargingEndTime &&
        this.params.chargingEndTime.length > 0
      ) {
        endTimeStart = this.params.chargingEndTime[0];
        endTimeEnd = this.params.chargingEndTime[1];
      }

      let bgnTimeAfterCStart = '';
      let bgnTimeAfterCEnd = '';
      if (
        this.params.chargingStartTimeAfterC &&
        this.params.chargingStartTimeAfterC.length > 0
      ) {
        bgnTimeAfterCStart = this.params.chargingStartTimeAfterC[0];
        bgnTimeAfterCEnd = this.params.chargingStartTimeAfterC[1];
      }

      let endTimeAfterCStart = '';
      let endTimeAfterCEnd = '';
      if (
        this.params.chargingEndTimeAfterC &&
        this.params.chargingEndTimeAfterC.length > 0
      ) {
        endTimeAfterCStart = this.params.chargingEndTimeAfterC[0];
        endTimeAfterCEnd = this.params.chargingEndTimeAfterC[1];
      }
      let params = {
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        stationId: this.params.stationId,
        pileId: this.params.pileId,
        orderId: this.params.orderId,
        partOrderNo: this.params.partOrderNo,
        mobile: this.params.mobile,
        userType: this.params.userType,
        enterpriseNo: this.params.enterpriseNo,
        applyTimeStart: applyTimeStart,
        applyTimeEnd: applyTimeEnd,
        orderStatus: this.params.orderStatus,
        exceptType: this.params.exceptType,
        exceptLevel: this.params.exceptLevel,
        transactStatus: this.params.transactStatus,
        reportTimeStart: reportTimeStart,
        reportTimeEnd: reportTimeEnd,
        transactTimeStart: transactTimeStart,
        transactTimeEnd: transactTimeEnd,
        applyMode: this.params.applyMode,
        orderSource: this.params.orderSource,
        bgnTimeStart: bgnTimeStart,
        bgnTimeEnd: bgnTimeEnd,
        bgnTimeAfterCStart: bgnTimeAfterCStart,
        bgnTimeAfterCEnd: bgnTimeAfterCEnd,
        endTimeStart: endTimeStart,
        endTimeEnd: endTimeEnd,
        endTimeAfterCStart: endTimeAfterCStart,
        endTimeAfterCEnd: endTimeAfterCEnd,
        pileInType: this.params.pileInType,
        pileSource: this.params.pileSource,
      };
      this.loading = true;
      const [err, res] = await getAbnormalOrderList(params);

      this.loading = false;

      if (err) return;

      const { data, total } = res;

      this.tableData = data;
      this.tablePage.total = total;
    },
    // 下载
    handleDownload() {},
    async debouncedStationSearch(query) {
      if (query !== '') {
        this.stationLoading = true;
        setTimeout(async () => {
          const [err, res] = await getStationList({
            stationName: query,
          });

          if (err) return;
          this.stationLoading = false;
          this.stationList = res.data.map((item) => ({
            label: item.stationName,
            value: item.stationId,
          }));
        }, 200);
      } else {
        this.stationList = [];
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  background: #fff;
  padding: 20px;
  border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
}
</style>
