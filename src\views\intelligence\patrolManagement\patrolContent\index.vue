<template>
  <div class="container container-float" style="padding: 0">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="tableLoading"
        :filterOptions="tableFilterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        class="buse-wrap-station"
        @loadData="loadTableData"
      >
        <template slot="defaultHeader">
          <div>
            <div class="card-head">
              <div class="card-head-text">巡视内容列表</div>

              <div class="top-button-wrap">
                <el-button
                  type="primary"
                  @click="() => handleContentEdit('create', {})"
                >
                  新增
                </el-button>
              </div>
            </div>
          </div>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button type="primary" plain @click="toggleContentStatus(row)">
              {{ row.status === 0 ? '停用' : '启用' }}
            </el-button>
            <el-button
              type="primary"
              plain
              @click="handleContentEdit('edit', row)"
            >
              编辑
            </el-button>
            <el-button type="danger" plain @click="handleContentDel(row)">
              删除
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
    <editContent
      ref="editContent"
      :type="contentType"
      :detailObj="selectContent"
      @contentAdd="loadTableData"
    />
  </div>
</template>

<script>
import editContent from './components/editContent.vue';
import {
  getContentPage,
  toggleContentStatus,
  deleteContent,
} from '@/api/interconnection/patrolContent';

export default {
  components: {
    editContent,
  },
  dicts: ['ls_charging_patrol_enabled_status', 'ls_charging_watermark'],
  data() {
    return {
      tableLoading: false,
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        patrolContentName: '',
        status: '',
      },
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'patrolContentNo',
          title: '巡视内容编号',
          minWidth: 120,
        },
        {
          field: 'patrolContentName',
          title: '巡视内容名称',
          minWidth: 120,
        },
        {
          field: 'patrolRequire',
          title: '巡视要求',
          minWidth: 200,
        },

        {
          field: 'sort',
          title: '排序',
          minWidth: 100,
        },
        {
          field: 'status',
          title: '状态',
          fixed: 'right',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_patrol_enabled_status,
              cellValue
            );
          },
        },
        {
          field: 'createTime',
          title: '创建时间',
          minWidth: 160,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],
      tableData: [],
      selectContent: {},
      contentType: 'create',
    };
  },

  computed: {
    tableFilterOptions() {
      return {
        config: [
          {
            field: 'patrolContentName',
            title: '巡视内容名称',
            element: 'el-input',
          },
          {
            field: 'status',
            title: '状态',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_patrol_enabled_status,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadTableData();
  },
  methods: {
    // 加载表格数据
    async loadTableData() {
      this.tableLoading = true;
      try {
        const params = {
          ...this.params,
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize,
        };

        const [err, res] = await getContentPage(params);
        if (err) return;
        this.tableData = res.data || [];
        this.tablePage.total = res.total || 0;
      } finally {
        this.tableLoading = false;
      }
    },

    // 编辑巡视内容
    async handleContentEdit(type, row) {
      this.contentType = type;
      this.selectContent = type === 'create' ? {} : { ...row };
      this.$refs.editContent.dialogVisible = true;
    },

    // 启用、停用巡视内容
    toggleContentStatus(row) {
      this.$confirm(
        `确定${row.status === 0 ? '停用' : '启用'}巡视内容：${
          row?.patrolContentName || ''
        }吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        const [err, res] = await toggleContentStatus({
          patrolContentId: row.patrolContentId,
          enable: row.status === 0 ? 1 : 0,
        });
        if (err) return;
        if (row.status === 0) {
          this.$message({
            type: 'success',
            message: '停用成功!',
          });
        } else {
          this.$message({
            type: 'success',
            message: '启用成功!',
          });
        }
        this.loadTableData();
      });
    },

    // 删除巡视内容
    handleContentDel(row) {
      this.$confirm(
        `确定删除巡视内容：${row?.patrolContentName || ''}吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        const [err, res] = await deleteContent({
          patrolContentId: row?.patrolContentId || '',
        });
        if (err) return;
        this.$message({
          type: 'success',
          message: '删除成功!',
        });
        this.loadTableData();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }
  margin: 16px;

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
  }
}
</style>
