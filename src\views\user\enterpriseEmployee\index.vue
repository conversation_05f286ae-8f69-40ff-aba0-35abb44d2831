<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        class="buse-wrap-station"
        @loadData="loadData"
        :tableOn="{
          'checkbox-change': handleCheckboxChange,
          'checkbox-all': handleCheckboxChange,
        }"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">企业员工管理</div>
            <div class="top-button-wrap">
              <el-button type="primary" @click="handleEnable('ENABLE')">
                批量启用
              </el-button>
              <el-button type="primary" @click="handleEnable('DISABLE')">
                批量禁用
              </el-button>
              <el-button type="primary" @click="handleExport">
                批量导出
              </el-button>
              <el-button type="primary" @click="handleInput">
                批量导入
              </el-button>
              <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
                新增员工
              </el-button>
            </div>
          </div>
          <div class="card-head-after"></div>
        </template>
        <template slot="status" slot-scope="{ row }">
          <el-switch v-model="row.status" @change="handleStatusChange(row)" />
        </template>
        <template slot="operate" slot-scope="{ row }">
          <!-- <div class="menu-box">
            <el-button type="primary" plain @click="handleDetail(row)">
              详情
            </el-button>
            <el-button
              type="primary"
              plain
              v-if="!row.status"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="row.status"
              type="primary"
              plain
              @click="handleAccountManagement(row)"
            >
              员工账户管理
            </el-button>
            <el-button
              @click="handleDelete(row)"
              v-if="!row.status"
              type="danger"
              plain
            >
              删除
            </el-button>
          </div> -->

          <el-dropdown trigger="click">
            <el-button class="button-border" type="primary" plain>
              操作
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <div @click="handleDetail(row)">详情</div>
              </el-dropdown-item>

              <el-dropdown-item>
                <div v-if="!row.status" @click="handleEdit(row)">编辑</div>
              </el-dropdown-item>

              <el-dropdown-item>
                <div v-if="row.status" @click="handleAccountManagement(row)">
                  员工账户管理
                </div>
              </el-dropdown-item>

              <el-dropdown-item>
                <div v-if="!row.status" @click="handleDelete(row)">删除</div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </BuseCrud>
    </div>
    <!-- 导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="760px"
      height="398px"
      append-to-body
      @close="handleCancel"
    >
      <div>模板下载：</div>
      <div class="box link-box">
        <el-link
          type="primary"
          :underline="false"
          style="font-size: 16px; vertical-align: baseline"
          @click="downloadTemplate"
        >
          导入模板.xlsx
        </el-link>
      </div>
      <div>上传文件：</div>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :auto-upload="false"
        :data="upload.data"
        :on-exceed="handleExceed"
        :on-error="handleError"
        :on-success="handleSuccess"
        :http-request="customUpload"
      >
        <div class="box el-upload__text">
          <img
            class="upload-icon"
            src="@/assets/icons/responseAndRegulation/upload.png"
          />
          选择要导入上传的文件
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryPage,
  batchModifyStatus,
  enterpriseUserRemove,
  batchImport,
} from '@/api/user/enterpriseEmployee';
import { getToken } from '@/utils/auth';
export default {
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      statusList: [
        { label: '启用', value: 'ENABLE' },
        { label: '禁用', value: 'DISABLE' },
      ],
      tableData: [],
      tableColumn: [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
        },
        {
          type: 'seq',
          title: '序号',
          minWidth: 60,
        },
        {
          field: 'fullName',
          title: '员工名称',
          minWidth: 120,
        },
        {
          field: 'mobile',
          title: '员工手机号',
          minWidth: 120,
        },
        {
          field: 'userId',
          title: '员工编号',
          minWidth: 120,
          slots: {
            default: ({ row }) => [
              <el-tooltip
                content={row.userId}
                placement="top"
                disabled={!row.userId || row.userId.length < 10}
              >
                <span class="ellipsis-text">{row.userId}</span>
              </el-tooltip>,
            ],
          },
        },
        {
          field: 'enterpriseName',
          title: '所属企业',
          minWidth: 120,
        },
        {
          field: 'enterpriseId',
          title: '企业编号',
          minWidth: 120,
          slots: {
            default: ({ row }) => [
              <el-tooltip
                content={row.enterpriseId}
                placement="top"
                disabled={!row.enterpriseId || row.enterpriseId.length < 10}
              >
                <span class="ellipsis-text">{row.enterpriseId}</span>
              </el-tooltip>,
            ],
          },
        },
        // {
        //   field: 'department',
        //   title: '所属机构',
        //   minWidth: 120,
        // },
        {
          field: 'role',
          title: '岗位角色',
          minWidth: 120,
        },
        {
          field: 'status',
          title: '员工状态',
          minWidth: 100,
          slots: {
            default: 'status',
          },
          fixed: 'right',
        },
        {
          field: 'accountBalance',
          title: '企业员工账户余额',
          minWidth: 150,
        },
        {
          field: 'updateUser',
          title: '修改人',
          minWidth: 120,
        },
        {
          field: 'updateTime',
          title: '最新修改时间',
          minWidth: 180,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          minWidth: 100,
          align: 'center',
          fixed: 'right',
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        enterpriseName: '',
        enterpriseId: '',
        fullName: '',
        userId: '',
        status: '',
      },
      selectedList: [],
      // 导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API +
          '/vehicle-charging-admin/enterpriseUser/batchImport',
        data: {},
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'enterpriseName',
            title: '所属企业名称',
            element: 'el-input',
          },
          {
            field: 'enterpriseId',
            title: '所属企业编号',
            element: 'el-input',
          },
          {
            field: 'fullName',
            title: '员工名称',
            element: 'el-input',
          },
          {
            field: 'userId',
            title: '员工编号',
            element: 'el-input',
          },
          {
            field: 'status',
            title: '员工状态',
            element: 'el-select',
            props: {
              options: this.statusList,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    // 批量导出
    async handleExport() {
      this.download(
        '/vehicle-charging-admin/enterprise/enterpriseExport',
        {
          ...this.manageParams,
        },
        `企业管理列表.xlsx`
      );
    },
    handleExceed(files, fileList) {
      console.log(files, fileList);
      // 清空已选文件列表
      this.$refs.upload.clearFiles();
      // 手动添加新选择的文件（首个文件）
      this.$refs.upload.handleStart(files[0]);
    },
    handleError(err, file, fileList) {
      console.log('文件上传失败', err);
      // this.$message.error('接口请求失败');
    },
    // 文件上传成功
    handleSuccess(res, file, fileList) {
      console.log('文件上传成功', res, file, fileList);
      // if (res.code == 10000) {
      //   this.$message.success('导入成功');
      // } else {
      //   this.$message.error(res.subMsg);
      // }
    },
    async customUpload({ action, file, data }) {
      this.upload.isUploading = true;
      this.upload.open = false;
      const formData = new FormData();
      console.log(file, data, 8888);
      formData.append('file', file);
      try {
        const [err, res] = await batchImport(formData);
        if (err) {
          return this.$message.error(err.message);
        }
        // this.$message.success('导入成功');
        this.$message.success(
          `导入成功${res.data.succeedNum}条，导入失败${res.data.failNum}条`
        );
        if (res.data.failNum > 0) {
          this.download(
            `/vehicle-charging-admin/enterpriseUser/export/${res.data.failInfoId}`,
            {},
            `企业用户导入失败数据.xlsx`,
            'getXlsx'
          );
          this.$message.success(`导入失败文件下载成功`);
        }
        this.handleCancel();
        this.loadData();
      } finally {
        this.upload.isUploading = false;
      }
    },
    // 导入
    handleInput() {
      this.upload.title = '导入';
      this.upload.open = true;
    },
    // 导入关闭
    handleCancel() {
      this.upload.open = false;
      this.$refs.upload.clearFiles();
    },
    // 下载模板
    downloadTemplate() {
      this.download(
        '/vehicle-charging-admin//enterpriseUser/template',
        {},
        `企业用户批量导入模版.xlsx`
      );
    },
    // 提交上传文件
    submitFileForm() {
      console.log('upload data', this.upload.data);
      this.$refs.upload.submit();
    },
    async loadData() {
      this.selectedList = [];
      const { enterpriseName, enterpriseId, fullName, userId, status } =
        this.params;

      this.loading = true;
      const [err, res] = await queryPage({
        enterpriseName,
        enterpriseId,
        status,
        userId,
        fullName,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      });
      this.loading = false;

      if (err) {
        return this.$message.error(err.message);
      }
      const { data, total } = res;

      this.tableData = data.map((item) => {
        return { ...item, status: item.status === 'ENABLE' ? true : false };
      });
      this.tablePage.total = total;
    },
    // 删除
    handleDelete(row) {
      const { userId, fullName } = row;
      this.$confirm(`确定删除${fullName}员工吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const [err, res] = await enterpriseUserRemove({
            userId,
          });
          if (err) {
            return this.$message.error(err.message);
          }
          this.$message.success(`删除成功`);
          this.loadData();
        })
        .catch(() => {});
    },
    // 批量选择
    handleCheckboxChange({ records }) {
      this.selectedList = records;
    },
    // 批量启用
    handleEnable(status) {
      if (this.selectedList.length == 0) {
        this.$message.warning('请先选择要操作的项');
        return;
      }
      this.$confirm(
        `确定批量${status === 'ENABLE' ? '启用' : '禁用'}选中的员工吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        const [err, res] = await batchModifyStatus({
          userIds: this.selectedList.map((item) => item.userId),
          type: status,
        });
        if (err) {
          return this.$message.error(err.message);
        }
        this.$message.success(
          `批量${status === 'ENABLE' ? '启用' : '禁用'}成功`
        );
        this.loadData();
      });
    },

    handleAdd() {
      this.$router.push({
        path: '/v2g-charging/baseInfo/userManage/enterpriseEmployee/addOrEdit',
      });
    },
    // 修改状态
    handleStatusChange(row) {
      const { status, userId, fullName } = row;
      this.$confirm(
        `确定${!status ? '停用' : '启用'}${fullName}员工吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(async () => {
          const [err, res] = await batchModifyStatus({
            type: status ? 'ENABLE' : 'DISABLE',
            userIds: [userId],
          });
          if (err) {
            this.submitLoading = false;
            return this.$message.error(err.message);
          }

          this.$message.success(`${!status ? '停用' : '启用'}成功`);
          this.loadData();
        })
        .catch(() => {
          row.status = !row.status;
        });
    },
    handleDetail(row) {
      this.$router.push({
        path: '/v2g-charging/baseInfo/userManage/enterpriseEmployee/addOrEdit',
        query: {
          ...row,
          isDetail: true,
        },
      });
    },
    // 编辑
    handleEdit(row) {
      this.$router.push({
        path: '/v2g-charging/baseInfo/userManage/enterpriseEmployee/addOrEdit',
        query: row,
      });
    },
    // 账号管理
    handleAccountManagement(row) {
      const { userId } = row;
      this.$router.push({
        path: '/v2g-charging/baseInfo/userManage/enterpriseEmployee/account',
        query: {
          userId,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  // background: #fff;
  // padding: 20px;
  // border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
}

::v-deep .el-upload {
  text-align: left;
  font-size: 16px;
}
.box {
  margin-top: 4px;
  width: 688px;
  height: 36px;
  line-height: 36px;
  border-radius: 2px;
  text-align: center;
}
.link-box {
  margin-bottom: 38px;
  border: 1px solid #dfe1e5;
}
::v-deep .el-upload__text {
  border: 1px dashed #dfe1e5;
}
.upload-icon {
  width: 14px;
  height: 14px;
  margin-right: 6px;
}

.ellipsis-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.button-border {
  border: 0.01rem solid #217aff;
  color: #217aff;
  background-color: #fff;
}
</style>
