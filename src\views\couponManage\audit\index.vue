<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
        <div class="device-head">
            <img src="@/assets/coupon/coupon-detail-top-icon.png" class="device-head-icon">
            
            <div class="device-info-wrap">
                <div class="device-title-wrap">
                    <div class="device-title">优惠券名称：{{ couponName }}</div>
                </div>
                <div class="device-info-wrap">
                    <el-row>
                        <el-col :span="8">
                            <span class="label">优惠券编码：</span>
                            <span class="value">{{couponNo}}</span>
                        </el-col>
                        <el-col :span="8">
                            <span class="label">券业务类型：</span>
                            <span class="value">{{couponBusinessType}}</span>
                        </el-col>
                        <el-col :span="8">
                            <span class="label">券基本类型：</span>
                            <span class="value">{{ couponBasicType}}</span>
                        </el-col>
                    </el-row>
                </div>
            </div>

            
        </div>

        <div class="info-card" >
            <div class="card-head" style="margin-bottom: 8px;">
                <div class="before-icon"></div>
                <div class="card-head-text">基础信息</div>
            </div>

            <div class="form-wrap">
                <el-row :gutter="20">
                    <el-col :span="8" v-for="(item, key) in baseInfo" :key="key" style="margin-bottom: 24px;">
                        <div style="display: flex;">
                            <div class="info-title">{{labels.baseInfo[key]}}：</div>
                            <div class="info-detail">{{ item }}</div>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>


        <div class="info-card" >
            <el-tabs v-model="activeName" >
                <el-tab-pane label="业务参数" name="business">
                    <div class="form-wrap" style="margin-top: 16px;">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <div style="display: flex; margin-bottom: 24px;">
                                    <div class="info-title">使用范围：</div>
                                    <div class="info-detail">{{ useScopeLimit }}</div>
            
                                </div>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :span="8" v-for="(item, key) in limitInfo" :key="key" style="margin-bottom: 24px;">
                                <div style="display: flex;">
                                    <div class="info-title">{{labels.limitInfo[key]}}：</div>
                                    <div class="info-detail">{{ item }}</div>
                                </div>
                            </el-col>
                        </el-row>

                        <BuseCrud
                            v-if="isShowBusinessList"
                            ref="crud1"
                            :loading="table.loading"
                            :tablePage="table.page"
                            :tableColumn="tableColumn"
                            :tableData="table.data"
                            :pagerProps="pagerProps"
                            :modalConfig="modalConfig"
                            @loadData="getTablePage"
                        >
                            <template slot="defaultHeader">
                                <div>
                                    <div class="top-button-wrap">
                                        <div class="choose-info-wrap" v-if="isShowStation">
                                            已选择
                                            <div class="choose-number">{{ stationNumber }} </div>
                                            个充电站，
                                            <div class="choose-number">{{ pileNumber }}</div>
                                            个充电桩
                                        </div>

                                        <div class="choose-info-wrap" v-if="isShowPile">
                                            已选择
                                            <div class="choose-number">{{ pileNumber }}</div>
                                            个充电桩
                                        </div>

                                        <div class="choose-info-wrap" v-if="isShowAsset">
                                            已选择
                                            <div class="choose-number">{{ assetNumber }}</div>
                                            个产权机构
                                        </div>
                                        
                                    </div>
                                </div>
                                    
                            </template>

                        </BuseCrud>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="用户参数" name="user">
                    <div class="form-wrap" style="margin-top: 16px;">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <div style="display: flex; margin-bottom: 24px;">
                                    <div class="info-title">用户类型：</div>
                                    <div class="info-detail">{{ userType }}</div>
            
                                </div>
                            </el-col>
                        </el-row>

                        <BuseCrud
                            v-if="isShowUserList"
                            ref="crud2"
                            :loading="userTable.loading"
                            :tablePage="userTable.page"
                            :tableColumn="userTableColumn"
                            :tableData="userTable.data"
                            :pagerProps="pagerProps"
                            :modalConfig="modalConfig"
                            @loadData="getUserTablePage"
                        >
                            <template slot="defaultHeader">
                                <div>
                                    <div class="top-button-wrap">

                                        <div class="choose-info-wrap" >
                                            圈选
                                            <div class="choose-number">{{ userNumber }}</div>
                                            个用户
                                        </div>
                                        
                                    </div>
                                </div>
                                    
                            </template>

                        </BuseCrud>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="限制参数" name="limit">
                    <div class="form-wrap">
                            <el-form
                                :model="limitDetailInfo.form"
                                :rules="limitDetailInfo.rules"
                                ref="limitInfoForm"
                                label-position="top"
                            >
                                        <el-row :gutter="20" style="margin-top: 16px;">
                                            <el-col :span="6">
                                                <el-form-item
                                                    label=""
                                                    prop="useLimit"
                                                    class="no-label"

                                                >
                                                <el-checkbox-group v-model="limitDetailInfo.form.useLimit" disabled>
                                                    <el-checkbox 
                                                        v-for="(item, index) in useLimitList"
                                                        :label="item.value"
                                                        :key="index"
                                                    >
                                                        {{ item.label }}
                                                    </el-checkbox >
                                                </el-checkbox-group>
                                            </el-form-item>
                                            </el-col>
                                        </el-row>
                                    </el-form>
                                </div>
                </el-tab-pane>
            </el-tabs>
        </div>

        <div class="info-card">
            <div class="card-head" style="margin-bottom: 8px;">
                <div class="before-icon"></div>
                <div class="card-head-text">审核信息</div>
            </div>

            <div class="form-wrap">
                <el-form :model="auditForm" :rules="rules" ref="auditForm"  label-position="top">
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item
                                label="审核结果"
                                prop="result"
  
                            >
                                <el-select
                                    v-model="auditForm.result"
                                    placeholder="请选择审核结果"
                                    style="width: 100%"
                                    >
                                    <el-option
                                        v-for="item in resultList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item
                                label="审核意见"
                                prop="remark"
       
                            >
                                <el-input
                                    v-model="auditForm.remark"
                                    type="textarea"
                                    :rows="3"
                                    placeholder="请输入审核意见"
                                ></el-input>
                            </el-form-item>
                        </el-col>  
                    </el-row>
                </el-form>
            </div>
        </div>

        <div class="bottom-wrap">
            <el-button
                @click="() => handleCancel()"
            >
                取消
            </el-button>
            <el-button
                type="primary"
                @click="() => handleConfirm()"
            >
                审核
            </el-button>
        </div>
    </div>
    
  </template>
  
  <script>
  
  import {
    getCouponDetail,
    auditCoupon,
  } from '@/api/operator/coupon'
    export default {
    components: {
        
    },
    dicts: [
        'ls_charging_subType',
        'ls_charging_station_type',
        'ls_charging_operation_mode',
        'ls_charging_asset_property',
    ],
    data() {
      return {
        couponId: '',

        activeName: 'business',

        couponName: '',
        couponNo: '',
        couponBusinessType: '',
        couponBasicType: '',
        labels: {
            baseInfo: {
                showTitle: '展示标题',
                generationMethod: '生成方式',
                validityPeriod: '有效期',
                initialInventory: '初始库存',
                discountMethod: '优惠方式',
                status: '券状态',
                ruleIntroduce: '规则说明',
                useIntroduce: '使用说明',
                creator: '创建人',
                createTime: '创建时间',
            },
            limitInfo: {
                discountScope: '优惠范围',
                holidayLimit: '节假日限制',
                weekLimit: '星期限制',
            }
        },

        baseInfo: {
            showTitle: '',
            generationMethod: '',
            validityPeriod:'',
            initialInventory: '',
            discountMethod: '',
            status: '',
            ruleIntroduce: '',
            useIntroduce: '',
            creator: '',
            createTime: '',
        },

        useScope: '',
        useScopeLimit: '',

        limitInfo: {
            discountScope: '',
            holidayLimit: '',
            weekLimit: ''
        },

        stationNumber: '0',
        pileNumber: '0',
        assetNumber: '0',

        isShowStation: false,
        isShowPile: false,
        isShowAsset: false,

        isShowBusinessList: false,


        table: {
            loading: false,
            page: {
                total: 0,
                currentPage: 1,
                pageSize: 10,
            },
            dataTotal: [],
            data: [],
        },

        tableColumn: [],

        tableStationColumn:[
            {
                type: 'seq',
                title: '序号',
                width: 60,
                minWidth: 60,
            },
            {
                field: 'stationNo',
                title: '充电站编号',
                minWidth: 120,
            },
            {
                field: 'stationName',
                title: '充电站名称',
                minWidth: 150,
            },
            {
                field: 'stationType',
                title: '站点类型',
                minWidth: 150,
                formatter: ({ cellValue }) => {
                    return this.selectDictLabel(
                        this.dict.type.ls_charging_station_type,
                        cellValue
                    );
                },
            },
            {
                field: 'operationMode',
                title: '运营模式',
                minWidth: 100, // 最小宽度
                formatter: ({ cellValue }) => {
                      return this.selectDictLabel(
                        this.dict.type.ls_charging_operation_mode,
                        cellValue
                      );
                    },
            },
            {
                field: 'assetProperty',
                title: '资产属性',
                minWidth: 100, // 最小宽度
                formatter: ({ cellValue }) => {
                      return this.selectDictLabel(
                        this.dict.type.ls_charging_asset_property,
                        cellValue
                      );
                    },
            },
        ],

        tablePileColumn: [
            {
                type: 'seq',
                title: '序号',
                width: 60,
                minWidth: 60,
            },
            {
                field: 'stationName',
                title: '所属充电站',
                minWidth: 150, // 最小宽度
            },
            {
                    field: 'operationMode',
                    title: '运营模式',
                    minWidth: 120, // 最小宽度
                    formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                          this.dict.type.ls_charging_operation_mode,
                          cellValue
                        );
                    },
                },
                {
                    field: 'pileNo',
                    title: '充电桩编号',
                    minWidth: 150, // 最小宽度
                },
                {
                    field: 'pileName',
                    title: '充电桩名称',
                    minWidth: 150, // 最小宽度
                },
            
        ],

        tableAssetColumn: [
            {
                type: 'seq',
                title: '序号',
                width: 60,
                minWidth: 60,
            },
            {
                field: 'operatorName',
                title: '产权机构',
            }
        ],

        userTable: {
            loading: false,
            page: {
                total: 0,
                currentPage: 1,
                pageSize: 10,
            },
            dataTotal: [],
            data: [],
        },

        userTableColumn: [],

        personTableColumn: [
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60,
                },
                {
                    field: 'userId', // todo 更新
                    title: '用户ID',
                    minWidth: 220, // 最小宽度
                },
                {
                    field: 'nickName', // todo 更新
                    title: '用户昵称',
                    minWidth: 220, // 最小宽度
                },
                {
                    field: 'mobile', // todo 更新
                    title: '手机号码',
                    minWidth: 220, // 最小宽度
                },
            ],

            enpTableColumn: [
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60,
                },
                {
                    field: 'userId', // todo 更新
                    title: '用户ID',
                    minWidth: 220, // 最小宽度
                },
                {
                    field: 'fullName', // todo 更新
                    title: '用户名称',
                    minWidth: 220, // 最小宽度
                },
                {
                    field: 'enterpriseName', // todo 更新
                    title: '企业名称',
                    minWidth: 220, // 最小宽度
                },
                {
                    field: 'mobile', // todo 更新
                    title: '手机号码',
                    minWidth: 220, // 最小宽度
                },
            ],


        pagerProps: {
            layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
        },


        userType: '',

        isShowUserList: false,


        limitDetailInfo: {
            form: {
                useLimit: [], 
            },
            rules: {
            }
        },

        auditForm: {
            result: '',
            remark: '',
        },
        rules: {
            result: [
                { required: true, message: '请选择审核结果', trigger: 'blur'}
            ],
        },


        couponBusinessTypeList: [
            { label: '充电券', value: 'CHARGE' },
        ],

        basicTypeList: [
            { label: '满减券', value: 'CASH' },
            { label: '折扣券', value: 'DISCOUNT' },
        ],

        generationMethodList: [
            { label: '动态生成', value: 'DG' },
            { label: '批量生成', value: 'BG' },
        ],

        couponStatusList: [
            { label: '启用', value: 'ENABLE' },
            { label: '停用', value: 'DISABLE' },
        ],

        discountScopeList: [
            { label: '服务费', value: 'SERVICE' },
            { label: '电费', value: 'ELEC' },
            { label: '服务费+电费', value: 'ALL' },
        ],

        holidayLimitList: [
            { label: '不区分', value: 'NONE' },
            { label: '仅限节假日', value: 'ONLY_HOLIDAY' },
            { label: '排除节假日', value: 'EXCLUDE_HOLIDAY' },
        ],
        weekLimitList: [
            { label: '周一', value: '1' },
            { label: '周二', value: '2' },
            { label: '周三', value: '3' },
            { label: '周四', value: '4' },
            { label: '周五', value: '5' },
            { label: '周六', value: '6' },
            { label: '周日', value: '7' },
        ],

        useScopeList: [
            { label: '按场站', value: 'STATION' },
            { label: '按充电桩', value: 'PILE' },
            { label: '按资产单位', value: 'UNDERTAKER' },
        ],

        stationLimitList:[
            { label: '不限制', value: 'NONE' },
            { label: '指定场站', value: 'ASSIGN' },
        ],
        pileLimitList:[
            { label: '不限制', value: 'NONE' },
            { label: '指定充电桩', value: 'ASSIGN' },
        ],
        institutionLimitList:[
            { label: '不限制', value: 'NONE' },
            { label: '指定资产单位', value: 'ASSIGN' },
        ],

        useTypeList: [
            {  label: '个人用户', value: 'PERSONAL' },
            {  label: '企业用户', value: 'ENP' },
        ],

        useLimitList: [
            {  label: '单笔订单仅可用一张券', value: '1' },
        ],

        resultList: [
            { label: '审核通过', value: 'PASS' },
            { label: '审核不通过', value: 'REJECT' },
        ],
      };
    },

    computed: {
        modalConfig() {
            return {
                addBtn: false,
                viewBtn: false,
                menu: false,
                editBtn: false,
                delBtn: false,
            }
        },
    },
    mounted() {
        const couponId = this.$route.query.couponId;
        this.couponId =    couponId;
        this.getCouponDetail();
    },
    methods: {
        // 获取优惠券详情
        async getCouponDetail() {
            const [err,res] = await getCouponDetail(
                {
                    couponId: this.couponId
                }
            )

            if(err)  return

            const {
                couponName,
                couponNo,
                couponBizType,
                couponType,
            } = res.data;

            this.couponName = couponName;
            this.couponNo = couponNo;
            this.couponBusinessType = this.selectDictLabel(
                this.couponBusinessTypeList,
                couponBizType
            );
            this.couponBasicType = this.selectDictLabel(
                this.basicTypeList,
                couponType
            );

            const {
                displayTitle,
                generateType,
                periodDesc,
                totalNum,
                discountDesc,
                couponStatus,
                ruleDesc,
                useDesc,
                createUser,
                createTime,
            }  = res.data;

            this.baseInfo = {
                showTitle: displayTitle,
                generationMethod: this.selectDictLabel(
                    this.generationMethodList,
                    generateType
                ),
                validityPeriod: periodDesc,
                initialInventory: totalNum,
                discountMethod: discountDesc,
                status: this.selectDictLabel(
                    this.couponStatusList,
                    couponStatus
                ),
                ruleIntroduce: ruleDesc,
                useIntroduce: useDesc,
                creator: createUser,
                createTime: createTime,
            }

            const  {
                assetScopeType,
                assetScopeLimit,
                stationList,
                pileList,
                assetUnitList,
                deductionType,
                holidayLimit,
                weekLimit,
            } = res.data

            this.useScope = this.selectDictLabel(
                this.useScopeList,
                assetScopeType,
            )

           let useScopeLimit = this.useScope

           let isShowBusinessList = false



            
            if (assetScopeType === 'STATION' && assetScopeLimit === 'ASSIGN') {
                useScopeLimit = '指定站点'
                isShowBusinessList = true

                this.isShowStation = true

                this.table.dataTotal = stationList ? stationList : []
                this.stationNumber =  this.table.dataTotal.length;

                let number = 0;
                this.table.dataTotal.forEach(item => {
                    number += Number(item.pileNum);
                })
                this.pileNumber = number;
                
                this.table.page.currentPage = 1;
                this.table.page.total = this.table.dataTotal.length;

                this.getTablePage();
                this.tableColumn = this.tableStationColumn

            } 

            if (assetScopeType === 'PILE' && assetScopeLimit === 'ASSIGN') {
                 useScopeLimit = '指定充电桩'
                 isShowBusinessList = true

                 this.isShowPile = true
                 
                this.table.dataTotal = pileList? pileList : []
              
                this.pileNumber = this.table.dataTotal.length;
                
                this.table.page.currentPage = 1;
                this.table.page.total = this.table.dataTotal.length;

                this.getTablePage();
                this.tableColumn = this.tablePileColumn
            }

            if (assetScopeType === 'UNDERTAKER' && assetScopeLimit === 'ASSIGN') {
                useScopeLimit = '指定资产单位'
                isShowBusinessList = true

                this.isShowAsset =  true

                this.table.dataTotal = assetUnitList? assetUnitList : []
                this.assetNumber = this.table.dataTotal.length;

                this.table.page.currentPage = 1;
                this.table.page.total = this.table.dataTotal.length;

                this.getTablePage();
                this.tableColumn = this.tableAssetColumn

            }

            this.isShowBusinessList = isShowBusinessList



            this.useScopeLimit = useScopeLimit

            const weekLimitList = weekLimit ? weekLimit.split(','): []
            console.log('weekLimitList', weekLimitList)

            const weekLimitText = weekLimitList.map(item => this.selectDictLabel(this.weekLimitList, item)).join(',')

            this.limitInfo = {
                discountScope: this.selectDictLabel(
                    this.discountScopeList,
                    deductionType,
                ),
                holidayLimit: this.selectDictLabel(
                    this.holidayLimitList,
                    holidayLimit,
                ),
                weekLimit: weekLimitText,
            }

            console.log('limitInfo', this.limitInfo)

        

            const  {
                userScopeLimit,
                userScope,
                singleUserList = [],
                enpUserList = [],
            } = res.data


            this.userType = this.selectDictLabel(
                this.useTypeList,
                userScopeLimit,
            )

            if ( userScopeLimit === 'PERSONAL' && userScope === 'ASSIGN') {
                this.isShowUserList = true

                this.userTable.dataTotal = singleUserList? singleUserList: []
                this.userNumber = this.userTable.dataTotal.length

                this.userTable.page.currentPage = 1;
                this.userTable.page.total = this.table.dataTotal.length;

                this.getUserTablePage();
                this.userTableColumn = this.personTableColumn

            } else if( userScopeLimit === 'ENP' && userScope === 'ASSIGN'){
                this.isShowUserList = true

                this.userTable.dataTotal = enpUserList? enpUserList: []


                this.userNumber = this.userTable.dataTotal.length

                this.userTable.page.currentPage = 1;
                this.userTable.page.total = this.table.dataTotal.length;

                this.getUserTablePage();
                this.userTableColumn = this.enpTableColumn
            }

            const {
                useNumLimit
            } = res.data
            
            if (useNumLimit ) {
                this.limitDetailInfo.form = {
                    useLimit: ['1',]
                }
            } 





        },

        
        getTablePage() {
            this.table.data = this.table.dataTotal.slice(
                (this.table.page.currentPage - 1) * this.table.page.pageSize,
                this.table.page.currentPage * this.table.page.pageSize
            );
        },

        getUserTablePage() {
            this.userTable.data = this.userTable.dataTotal.slice(
                (this.userTable.page.currentPage - 1) * this.userTable.page.pageSize,
                this.userTable.page.currentPage * this.userTable.page.pageSize
            );
        },

        handleCancel() {
            this.$router.back()
        },

        // 审核
        async handleConfirm() {
            this.$refs.auditForm.validate(async(valid) => {
                if (valid) {
                    const {
                        result,
                        remark,
                    } = this.auditForm;

                    const params = {
                        couponIds: [this.couponId],
                        reviewStatus: result,
                        reviewRemark: remark,
                    }

                    const [err,res] = await auditCoupon(
                        params
                    )

                    if(err)  return

                    this.$message.success('审核成功')
                    this.$router.back()


                }
            });
               
        }
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }
   

  .device-head {
    background-color: #fff;
    
    display: flex;
    height: 112px;
    display: flex;
    align-items: center;
    padding: 0 24px;
    box-sizing: border-box;
    .device-head-icon {
        width: 48px;
        height: 48px;
        margin-right: 24px;
    }
    .device-info-wrap {
        flex: 1;
        .device-title-wrap {
            height: 32px;
            display: flex;
            align-items: center;
            .device-title {
                font-weight: 500;
                font-size: 24px;
                color: #12151A;
            }
            .device-status {
                // width: 50px;
                padding: 0 10px;
                box-sizing: border-box;
                height: 24px;
                border-radius: 10px 0 10px 0;
                font-size: 16px;
                font-weight: 400;
                line-height: 24px;
                text-align: center;
                color: #fff;
                background: linear-gradient(321.01deg, #00C864 8.79%, #38F3CA 100.27%);
                margin-left: 12px;
            }
        }
        .device-info-wrap {
            height: 16px;
            margin-top: 16px;
            font-size: 16px;
            font-weight: 400;
            color: #292B33;
        }
    }
    
  
  }
  

  .info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    background: linear-gradient(180deg, #E9F2FF 0%, #FFFFFF 100%);
    .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
    }
    .card-head-text {
        flex:1;
        font-weight: 500;
        font-size: 16px;
        color: #12151A;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }

   
    }

    .button-wrap {
      display: flex;
      .invite-btn {
          background-color: #1ab2ff;
          border-color: #1ab2ff;
        }
        ::v-deep .el-button--small {
          font-size: 14px;
        }
      .distribution {
          margin-left: 24px;
          margin-right: 24px;
          display: flex;
          align-items: center;
        }
    }
  }

  
  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
      padding: 0 16px 16px 16px;
      .info-title {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #505363;
      }
      .info-detail {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #292B33;
        display: flex;
        .info-price {
            font-weight: 400;
            color: #FF8D24;
            margin-right: 5px;
        }
      }
      .info-amount {
        height: 28px;
        background-color: #FFF7E6;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 8px;
        font-weight: 400;
        font-size: 20px;
        color: #FE8921;
        margin-top: -6px;
        margin-right: 4px;

      }
      .info-img {
        width: 140px;
        height: 140px;
      }


  .top-button-wrap {
        // margin-left: 24px;
        display: flex;
        align-items: center;
        height: 34px;
        align-items: center;
        margin-bottom: 14px;
        .choose-info-wrap {
            border-radius: 2px;
            height: 34px;
            padding: 0 10px;
            display: flex;
            align-items: center;
            background-color: #EBF3FF;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 14px;
            margin-left: 16px;
            color: #217AFF;
            .choose-number {
                font-size: 16px;
                font-weight: 500;
                margin: 0 4px;
            }
        }
        
    }
    }
  }

  ::v-deep .el-tabs__header {
    box-sizing: border-box;
    background: linear-gradient(180deg, #E9F2FF 0%, #FFFFFF 100%);
  }



  .container {
      position: relative;
      padding-bottom: 100px;
      box-sizing: border-box;
      .bottom-wrap {
          position: fixed;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 86px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          background-color: #FFFFFF;
          padding-right: 32px;
          box-sizing: border-box;
      }
  }

  </style>
  