<template>
  <el-dialog
    title="巡视计划"
    :visible.sync="dialogVisible"
    width="630px"
    @close="handleCancel"
  >
    <div class="info-card">
      <div class="form-wrap" v-if="dialogVisible">
        <el-form
          :model="baseInfo.form"
          :rules="baseInfo.rules"
          ref="baseInfoForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <!-- <el-col :span="24">
              <el-form-item
                label="计划编号："
                prop="patrolPlanId"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.patrolPlanId"
                  placeholder="自动生成"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col> -->
            <el-col :span="24">
              <el-form-item
                label="计划名称："
                prop="patrolPlanName"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.patrolPlanName"
                  placeholder="请输入计划名称"
                  :disabled="type === 'detail'"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item
                label="分组名称："
                prop="groupId"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.groupId"
                  placeholder="请选择分组"
                  style="width: 100%"
                  :disabled="type === 'detail'"
                  filterable
                  clearable
                  remote
                  :remoteMethod="debouncedGroupListSearch"
                >
                  <el-option
                    v-for="item in groupList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="运维班组："
                prop="maintenanceTeamId"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.maintenanceTeamId"
                  placeholder="请选择运维班组"
                  style="width: 100%"
                  :disabled="type === 'detail'"
                  filterable
                  clearable
                  remote
                  :remoteMethod="debouncedTeamPageSearch"
                >
                  <el-option
                    v-for="item in teamPageList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="巡视类型："
                prop="patrolType"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.patrolType"
                  placeholder="请选择巡视类型"
                  style="width: 100%"
                  :disabled="type === 'detail'"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_charging_patrol_type"
                    :key="item.value"
                    :label="item.label"
                    :value="Number(item.value)"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="baseInfo.form.patrolType === 0">
              <el-form-item
                label="巡视频次："
                prop="patrolFrequenciesType"
                :label-width="formLabelWidth"
              >
                <el-radio-group
                  v-model="baseInfo.form.patrolFrequenciesType"
                  placeholder="请选择巡视频次"
                  style="width: 100%"
                  :disabled="type === 'detail'"
                >
                  <el-radio
                    v-for="dict in this.dict.type
                      .ls_charging_patrol_frequencies_type"
                    :key="dict.value"
                    :label="Number(dict.value)"
                  >
                    {{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="巡视次数："
                prop="patrolFrequenciesTime"
                :label-width="formLabelWidth"
              >
                <div class="flex">
                  <span style="margin-right: 4px">每</span>
                  <el-input-number
                    v-model="baseInfo.form.patrolFrequenciesTime"
                    :min="1"
                    :max="1000"
                    :disabled="type === 'detail'"
                    style="width: 50%"
                  ></el-input-number>
                  <span style="margin-left: 4px">
                    {{
                      selectDictLabel(
                        this.dict.type.ls_charging_patrol_frequencies_type,
                        baseInfo.form.patrolFrequenciesType
                      )
                    }}1次
                  </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="巡视内容："
                prop="patrolContentIds"
                :label-width="formLabelWidth"
              >
                <el-checkbox-group
                  v-model="baseInfo.form.patrolContentIds"
                  placeholder="请选择巡视内容"
                  style="width: 100%"
                  :disabled="type === 'detail'"
                >
                  <el-checkbox
                    v-for="(item, index) in patrolContentList"
                    :key="index"
                    :label="item.value"
                  >
                    {{ item.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="巡视时效(天)："
                prop="patrolDay"
                :label-width="formLabelWidth"
              >
                <el-input-number
                  v-model="baseInfo.form.patrolDay"
                  :min="1"
                  :max="365"
                  :disabled="type === 'detail'"
                  style="width: 100%"
                ></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="状态："
                prop="status"
                :label-width="formLabelWidth"
              >
                <el-radio-group
                  v-model="baseInfo.form.status"
                  :disabled="type === 'detail'"
                >
                  <el-radio
                    v-for="dict in statusList"
                    :key="dict.value"
                    :label="dict.value"
                  >
                    {{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        @click="handleSave"
        :loading="submitLoading"
        type="primary"
        v-if="type !== 'detail'"
      >
        提交
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  createPlan,
  updatePlan,
  getGroupList,
  getTeamPage,
} from '@/api/interconnection/patrolPlan';
import { getContentPage } from '@/api/interconnection/patrolContent';

export default {
  props: {
    type: {
      type: String,
      default: 'create',
    },
    detailObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },

  dicts: [
    'ls_charging_patrol_type', // 巡视类型
    'ls_charging_patrol_frequencies_type', // 巡视频次
  ],
  data() {
    return {
      dialogVisible: false,
      submitLoading: false,
      formLabelWidth: '120px',
      groupListLoading: false,
      groupList: [],
      teamPageLoading: false,
      teamPageList: [],
      patrolContentList: [],
      statusList: [
        { label: '启用', value: 0 },
        { label: '停用', value: 1 },
      ],
      baseInfo: {
        form: {
          patrolPlanId: '',
          patrolPlanName: '',
          groupId: '',
          maintenanceTeamId: '',
          patrolType: '',
          patrolFrequenciesType: '',
          patrolFrequenciesTime: '',
          patrolContentIds: [],
          patrolDay: 1,
          status: 0,
        },
        rules: {
          patrolPlanName: [
            { required: true, message: '请输入计划名称', trigger: 'blur' },
          ],
          groupId: [
            { required: true, message: '请选择分组', trigger: 'change' },
          ],
          maintenanceTeamId: [
            { required: true, message: '请选择运维班组', trigger: 'change' },
          ],
          patrolType: [
            { required: true, message: '请选择巡视类型', trigger: 'change' },
          ],
          patrolFrequenciesType: [
            {
              required: true,
              message: '请选择巡视频次类型',
              trigger: 'change',
            },
          ],
          patrolFrequenciesTime: [
            { required: true, message: '请输入巡视次数', trigger: 'blur' },
          ],
          patrolContentIds: [
            { required: true, message: '请选择巡视内容', trigger: 'change' },
            {
              validator: (rule, value, callback) => {
                if (value && value.length > 10) {
                  callback(new Error('巡视内容最多选择10个'));
                } else {
                  callback();
                }
              },
              trigger: 'change',
            },
          ],
          patrolDay: [
            { required: true, message: '请输入巡视时效', trigger: 'blur' },
          ],
        },
      },
    };
  },
  watch: {
    detailObj: {
      handler(val) {
        if (Object.keys(val).length > 0) {
          this.baseInfo.form = {
            ...this.baseInfo.form,
            ...val,
            // 确保patrolContentIds是数组
            patrolContentIds: val.patrolContentIds
              ? val.patrolContentIds.split(',').map((item) => item.trim())
              : [],
          };
          if (val.groupName) {
            this.debouncedGroupListSearch(val.groupName);
          }
          if (val.maintenanceTeamName) {
            this.debouncedTeamPageSearch(val.maintenanceTeamName);
          }
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    this.loadContentList();
  },
  methods: {
    async loadContentList() {
      const params = {
        status: '0',
        pageNum: 1,
        pageSize: 100,
      };

      const [err, res] = await getContentPage(params);
      if (err) return;
      this.patrolContentList = (res.data || []).map((item) => {
        return {
          label: item.patrolContentName || '',
          value: item.patrolContentId || '',
        };
      });
    },
    async debouncedGroupListSearch(query) {
      if (query !== '') {
        this.groupListLoading = true;
        setTimeout(async () => {
          const [err, res] = await getGroupList({
            groupName: query,
          });

          if (err) return;
          this.groupListLoading = false;
          this.groupList = res.data.map((item) => ({
            label: item.groupName,
            value: item.groupId,
          }));
        }, 200);
      } else {
        this.groupList = [];
      }
    },
    async debouncedTeamPageSearch(query) {
      if (query !== '') {
        this.teamPageLoading = true;
        setTimeout(async () => {
          const [err, res] = await getTeamPage({
            teamName: query,
          });

          if (err) return;
          this.teamPageLoading = false;
          this.teamPageList = res.data.map((item) => ({
            label: item.teamName,
            value: item.teamId,
          }));
        }, 200);
      } else {
        this.teamPageList = [];
      }
    },
    // 取消
    handleCancel() {
      this.dialogVisible = false;
      this.resetForm();
    },

    // 重置表单
    resetForm() {
      this.baseInfo.form = {
        patrolPlanId: '',
        patrolPlanName: '',
        groupId: '',
        maintenanceTeamId: '',
        patrolType: '',
        patrolFrequenciesType: '',
        patrolFrequenciesTime: '',
        patrolContentIds: [],
        patrolDay: 1,
        status: 0,
      };
      this.$nextTick(() => {
        this.$refs.baseInfoForm && this.$refs.baseInfoForm.clearValidate();
      });
    },

    // 保存
    handleSave() {
      this.$refs.baseInfoForm.validate(async (valid) => {
        if (!valid) return;

        this.submitLoading = true;
        try {
          // 处理巡视内容ID，转为逗号分隔的字符串

          const params = {
            ...this.baseInfo.form,
            patrolContentIds: this.baseInfo.form.patrolContentIds.join(','),
            groupName:
              this.groupList.filter(
                (item) => item.value === this.baseInfo.form.groupId
              )[0]?.label || '',
            maintenanceTeamName:
              this.teamPageList.filter(
                (item) => item.value === this.baseInfo.form.maintenanceTeamId
              )[0]?.label || '',
          };

          let err, res;
          if (this.type === 'create') {
            // 新增
            [err, res] = await createPlan(params);
          } else {
            // 编辑
            [err, res] = await updatePlan(params);
          }

          if (err) return;

          this.$message({
            type: 'success',
            message: this.type === 'create' ? '新增成功!' : '编辑成功!',
          });

          this.dialogVisible = false;
          this.resetForm();
          this.$emit('planAdd');
        } finally {
          this.submitLoading = false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.info-card {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;

  .form-wrap {
    margin-top: 16px;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
