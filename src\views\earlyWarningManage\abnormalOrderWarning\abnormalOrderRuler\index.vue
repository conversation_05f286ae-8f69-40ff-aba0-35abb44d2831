<template>
  <div class="container container-float" style="padding: 0">
    <!-- 异常规则管理标签栏内容 -->

    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="manageLoading"
        :filterOptions="manageFilterOptions"
        :tablePage="manageTablePage"
        :tableColumn="manageTableColumn"
        :tableData="manageTableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        class="buse-wrap-station"
        @loadData="loadManageData"
      >
        <template slot="defaultHeader">
          <div>
            <div class="card-head">
              <div class="card-head-text">订单异常预警规则列表</div>

              <div class="top-button-wrap">
                <el-button
                  type="primary"
                  @click="() => handleRuleEdit('create', {})"
                >
                  新增异常预警规则
                </el-button>
              </div>
            </div>
          </div>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button type="primary" plain @click="toggleRuleDel(row)">
              {{ row.enabled == '1' ? '停用' : '启用' }}
            </el-button>
            <el-button
              v-if="row.enabled == '0'"
              type="primary"
              plain
              @click="handleRuleEdit('edit', row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="row.enabled == '0'"
              type="danger"
              plain
              @click="handleRuleDel(row)"
            >
              删除
            </el-button>
            <el-button
              type="primary"
              plain
              @click="handleRuleEdit('detail', row)"
            >
              详情
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
    <editOrderRule
      ref="editOrderRule"
      :type="orderRuleType"
      :detailObj="selectOrderRule"
      @orderruleAdd="loadManageData"
    />
  </div>
</template>

<script>
import editOrderRule from './components/editOrderRule.vue';
import moment from 'moment';
import {
  orderPage,
  changeStatus,
  OrderDelete,
  orderDetail,
} from '@/api/earlyWarningManage/abnormalOrderWarning';

export default {
  components: {
    editOrderRule,
  },
  dicts: [
    'ls_order_except_level', // 异常级别
    'ls_order_except_type', // 异常类型
    'ls_order_except_measures', // 处理措施
    'ls_order_except_status', // 异常状态
    'o_charge_order_except_rule', // 异常编号
  ],
  data() {
    return {
      activeName: 'manage',
      manageLoading: false,
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        id: '',
        ruleName: '',
        abnormalId: '',
        abnormalName: '',
        abnormalLevel: '',
        abnormalType: '',
        measures: '',
        creatorTime: [],
        enabled: '',
      },
      manageTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      manageTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'id',
          title: '预警规则编号',
          minWidth: 190,
        },
        {
          field: 'ruleName',
          title: '预警规则名称',
          minWidth: 190,
        },
        {
          field: 'abnormalId',
          title: '异常编号',
          minWidth: 190,
        },
        {
          field: 'abnormalLevel',
          title: '异常等级',
          minWidth: 100,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_order_except_level,
              cellValue
            );
          },
        },
        {
          field: 'abnormalName',
          title: '异常名称',
          minWidth: 150,
        },
        {
          field: 'abnormalType',
          title: '异常类型',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_order_except_type,
              cellValue
            );
          },
        },
        {
          field: 'notifyChannelsSummaryDesc',
          title: '通知渠道',
          minWidth: 150,
        },
        {
          field: 'enabled',
          title: '状态',
          minWidth: 120,
          fixed: 'right',
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              [
                {
                  value: '0',
                  label: '停用',
                },
                {
                  value: '1',
                  label: '启用',
                },
              ],
              cellValue
            );
          },
        },
        {
          field: 'createdTime',
          title: '创建时间',
          minWidth: 200,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],
      manageTableData: [],
      selectOrderRule: {},
      orderRuleType: 'create',
    };
  },

  computed: {
    manageFilterOptions() {
      return {
        config: [
          {
            field: 'id',
            title: '预警规则编号',
            element: 'el-input',
          },
          {
            field: 'ruleName',
            title: '预警规则名称',
            element: 'el-input',
          },
          {
            field: 'abnormalId',
            title: '异常编号',
            element: 'el-input',
          },
          {
            field: 'abnormalName',
            title: '异常名称',
            element: 'el-input',
          },
          {
            field: 'abnormalLevel',
            title: '异常等级',
            element: 'el-select',
            props: {
              placeholder: '请选择异常等级',
              options: this.dict.type.ls_order_except_level,
            },
          },
          {
            field: 'abnormalType',
            title: '异常类型',
            element: 'el-select',
            props: {
              placeholder: '请选择异常类型',
              options: this.dict.type.ls_order_except_type,
            },
          },
          {
            field: 'creatorTime',
            title: '创建时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              rangeSeparator: '至',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
              options: [],
              defaultTime: ['00:00:00', '23:59:59'],
            },
          },
          {
            field: 'enabled',
            title: '状态',
            element: 'el-select',
            props: {
              placeholder: '请选择启用状态',
              options: [
                {
                  value: '0',
                  label: '停用',
                },
                {
                  value: '1',
                  label: '启用',
                },
              ],
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadManageData();
  },
  methods: {
    // 获取管理列表数据
    async loadManageData() {
      const {
        id,
        ruleName,
        abnormalId,
        abnormalName,
        abnormalLevel,
        abnormalType,
        measures,
        creatorTime,
        enabled,
      } = this.manageFilterOptions.params;

      this.manageLoading = true;
      const [err, res] = await orderPage({
        pageNum: this.manageTablePage.currentPage,
        pageSize: this.manageTablePage.pageSize,
        id: id,
        ruleName: ruleName,
        abnormalId: abnormalId,
        abnormalName: abnormalName,
        abnormalType: abnormalType,
        abnormalLevel: abnormalLevel,
        createTimeStart: creatorTime?.length > 0 ? creatorTime[0] : '',
        createTimeEnd: creatorTime?.length > 0 ? creatorTime[1] : '',
        enabled: enabled,
      });

      this.manageLoading = false;

      if (err) return;
      const { data, total } = res;

      this.manageTableData = data;
      this.manageTablePage.total = total;
    },

    // 编辑异常规则

    async handleRuleEdit(type, row) {
      this.orderRuleType = type;
      this.selectOrderRule = row;
      this.$refs.editOrderRule.baseInfo.rules.webPushTemplate[0].required = false;
      this.$refs.editOrderRule.baseInfo.rules.appPushTemplate[0].required = false;
      this.$refs.editOrderRule.baseInfo.rules.wxPushTemplate[0].required = false;
      this.$refs.editOrderRule.baseInfo.rules.smsPushTemplate[0].required = false;
      if (type == 'detail' || type == 'edit') {
        this.$refs.editOrderRule.getNameList();
        const [err, res] = await orderDetail({ operateId: row.id });
        if (err) return;
        // console.log(res.data);
        // console.log(row);
        this.$refs.editOrderRule.getExceptionRuleDetail(res.data.abnormalId);
        this.selectOrderRule = {
          id: res.data.id,
          ruleName: res.data.ruleName,
          type: row.abnormalType,
          name: res.data.abnormalId,
          exceptId: res.data.abnormalId,
          description: '',
          level: row.abnormalLevel,
          notificationChannels: [],
          webPushTemplate: '',
          appPushTemplate: '',
          wxPushTemplate: '',
          smsPushTemplate: '',
        };
        res.data.notifyChannels.forEach((item) => {
          console.log('item', item);
          this.selectOrderRule.notificationChannels.push(
            item.notifyChannelType
          );
          if (item.notifyChannelType == 'web') {
            this.selectOrderRule.webPushTemplate = item.notifyTemplateId;
            this.$refs.editOrderRule.baseInfo.rules.webPushTemplate[0].required = true;
          }
          if (item.notifyChannelType == 'app') {
            this.selectOrderRule.appPushTemplate = item.notifyTemplateId;
            this.$refs.editOrderRule.baseInfo.rules.appPushTemplate[0].required = true;
          }
          if (item.notifyChannelType == 'wechat') {
            this.selectOrderRule.wxPushTemplate = item.notifyTemplateId;
            this.$refs.editOrderRule.baseInfo.rules.wxPushTemplate[0].required = true;
          }
          if (item.notifyChannelType == 'sms') {
            this.selectOrderRule.smsPushTemplate = item.notifyTemplateId;
            this.$refs.editOrderRule.baseInfo.rules.smsPushTemplate[0].required = true;
          }
        });
        // console.log('selectOrderRule', this.selectOrderRule);
      }

      this.$refs.editOrderRule.dialogVisible = true;
    },

    // 启用、停用异常规则
    toggleRuleDel(row) {
      this.$confirm(
        `确定${row.enabled == '1' ? '停用' : '启用'}异常规则：${
          row?.ruleName || ''
        }吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        console.log(row);
        let params = {
          operateId: row.id,
        };
        const [err, res] = await changeStatus(params);
        if (err) return;
        if (row.enabled == '1') {
          this.$message({
            type: 'success',
            message: '停用成功!',
          });
        } else {
          this.$message({
            type: 'success',
            message: '启用成功!',
          });
        }
        this.manageTablePage.total = 0;
        this.manageTablePage.currentPage = 1;
        this.loadManageData();
      });
    },
    // 删除异常规则
    handleRuleDel(row) {
      this.$confirm(`'确定删除异常规则：${row?.ruleName || ''}吗？'`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const [err, res] = await OrderDelete({
          operateId: row?.id || '',
        });
        if (err) return;
        this.$message({
          type: 'success',
          message: '删除成功!',
        });
        this.manageTablePage.total = 0;
        this.manageTablePage.currentPage = 1;
        this.loadManageData();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-tabs {
  margin-top: 16px;
  background-color: #f5f6f9;
  .el-tabs__header {
    padding-left: 0;
    display: flex;
    justify-content: center;
    text-align: center;
    margin-bottom: 1px;
    .el-tabs__item {
      padding: 0;
      width: 164px;
      font-size: 18px;
      font-weight: 400;
      background-color: #fff;
    }
    .el-tabs__item.is-active {
      background-color: #1677fe;
      color: #fff;
    }
    .el-tabs__nav-scroll {
      border-radius: 25px;
      border: solid 1px #dfe1e5;
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__nav-wrap::after {
      width: 0;
    }
  }
}

.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }
  margin: 16px;

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
  }
}
</style>
