<template>
  <div class="container">
    <div class="info-card" style="margin-top: 0">
      <div class="card-head" style="margin-top: 0">
        <div class="before-icon"></div>
        <div class="card-head-text">企业信息配置</div>
        <div class="top-button-wrap">
          <el-button type="primary" @click="handleEdit()" v-if="!isEdit">
            编辑
          </el-button>
        </div>
      </div>
      <div class="card-head-after"></div>
      <div class="form-wrap">
        <el-form
          :model="enterpriseForm"
          :rules="enterpriseRules"
          ref="enterpriseForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="开票服务商"
                prop="stationSoc"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="enterpriseForm.channel"
                  placeholder="请选择开票服务商"
                  style="width: 100%"
                  :disabled="!isEdit"
                >
                  <el-option
                    v-for="item in channelList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="企业名称" prop="invoiceName">
                <el-input
                  v-model="enterpriseForm.invoiceName"
                  placeholder="请输入企业名称"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="纳税人识别号" prop="taxpayerNo">
                <el-input
                  v-model="enterpriseForm.taxpayerNo"
                  placeholder="请输入纳税人识别号"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="地址" prop="address">
                <el-input
                  v-model="enterpriseForm.address"
                  placeholder="请输入地址"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="电话" prop="phone">
                <el-input
                  v-model="enterpriseForm.phone"
                  placeholder="请输入电话"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="开户行" prop="bank">
                <el-input
                  v-model="enterpriseForm.bank"
                  placeholder="请输入开户行"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="开户账号" prop="acctNo">
                <el-input
                  v-model="enterpriseForm.acctNo"
                  placeholder="请输入开户账号"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="邮箱" prop="email">
                <el-input
                  v-model="enterpriseForm.email"
                  placeholder="请输入邮箱"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="enterpriseForm.remark"
                  placeholder="请输入备注"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item label="请求地址" prop="invRequestUrl">
                <el-input
                  v-model="enterpriseForm.invRequestUrl"
                  placeholder="请输入请求地址"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <!-- <el-col :span="8">
              <el-form-item label="AppKey" prop="appKey">
                <el-input
                  v-model="enterpriseForm.appKey"
                  placeholder="请输入AppKey"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="AppSecret" prop="appSecret">
                <el-input
                  v-model="enterpriseForm.appSecret"
                  placeholder="请输入AppSecret"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item label="开票回调" prop="invResponseUrl">
                <el-input
                  v-model="enterpriseForm.invResponseUrl"
                  placeholder="请输入开票回调地址"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="showLoginOrgCode">
              <el-form-item label="主数据单位编号" prop="loginOrgCode">
                <el-input
                  v-model="enterpriseForm.loginOrgCode"
                  placeholder="请输入主数据单位编号"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="停止开票开始结束时间" prop="stopTime">
                <el-date-picker
                  v-model="enterpriseForm.stopTime"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
                  :disabled="!isEdit"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="info-card">
      <div class="card-head" style="margin-top: 0">
        <div class="before-icon"></div>
        <div class="card-head-text">开票信息配置</div>
      </div>
      <div class="card-head-after"></div>
      <div class="form-wrap">
        <el-form
          :model="invoicingForm"
          :rules="invoicingRules"
          ref="invoicingForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="开票人" prop="drawer">
                <el-input
                  v-model="invoicingForm.drawer"
                  placeholder="请输入开票人"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="收费人" prop="payee">
                <el-input
                  v-model="invoicingForm.payee"
                  placeholder="请输入收费人"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="复核人" prop="reviewer">
                <el-input
                  v-model="invoicingForm.reviewer"
                  placeholder="请输入复核人"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="info-card" :style="isEdit ? 'margin-bottom: 100px' : ''">
      <div class="card-head" style="margin-top: 0">
        <div class="before-icon"></div>
        <div class="card-head-text">开票商品配置</div>
        <div class="top-button-wrap" v-if="isEdit">
          <el-button type="primary" @click="handleRowAdd()">新增</el-button>
        </div>
      </div>
      <div class="card-head-after"></div>
      <div class="form-wrap">
        <BuseCrud
          :tableColumn="goodsTableColumn"
          :tableData="goodsTableData"
          :loading="loading"
          :pagerProps="{
            layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
          }"
          :tablePage="goodsTablePage"
          :modalConfig="goodsModalConfig"
        >
          <template slot="operate" slot-scope="{ row }">
            <div class="menu-box">
              <el-button type="primary" plain @click="handleRowEdit(row)">
                编辑
              </el-button>
              <el-button type="danger" plain @click="handleRowDelete(row)">
                删除
              </el-button>
            </div>
          </template>
        </BuseCrud>
      </div>
    </div>

    <div class="bottom-wrap" v-if="isEdit">
      <el-button @click="() => handleCancel()">取消</el-button>
      <el-button type="primary" @click="() => handleConfirm()">保存</el-button>
    </div>

    <AddOrEditModal
      ref="addOrEditModal"
      @confirm="handleModalConfirm"
      :dialogTitle="dialogTitle"
    />
  </div>
</template>

<script>
import AddOrEditModal from './components/addOrEditModal';
import {
  getSellerConfig,
  saveOrUpdateSellerConfig,
  getInvGoodsConfigByPage,
  deleteGoodsConfig,
} from '@/api/invoicesManagement/invoiceConfiguration';

export default {
  components: {
    AddOrEditModal,
  },
  data() {
    return {
      isEdit: false,
      formLabelWidth: '120px',
      // 企业信息配置
      enterpriseForm: {
        channel: '1', // 开票服务商（已存在）
        invoiceName: '', // 企业名称
        taxpayerNo: '', // 纳税人识别号
        address: '', // 地址
        phone: '', // 电话
        bank: '', // 开户行
        acctNo: '', // 开户账号
        email: '', // 邮箱
        // remark: '', // 备注（非必填）
        invRequestUrl: '', // 请求地址
        // appKey: '', // AppKey
        // appSecret: '', // AppSecret
        invResponseUrl: '', // 开票回调
        loginOrgCode: '', // 主数据单位编号
        stopTime: [], // 停止开票开始结束时间
      },
      showLoginOrgCode: false,
      enterpriseRules: {
        channel: [
          { required: true, message: '请选择开票服务商', trigger: 'change' },
        ],
        invoiceName: [
          { required: true, message: '请输入企业名称', trigger: 'blur' },
        ],
        taxpayerNo: [
          { required: true, message: '请输入纳税人识别号', trigger: 'blur' },
        ],
        address: [{ required: true, message: '请输入地址', trigger: 'blur' }],
        phone: [{ required: true, message: '请输入电话', trigger: 'blur' }],
        bank: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
        acctNo: [
          { required: true, message: '请输入开户账号', trigger: 'blur' },
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          {
            type: 'email',
            message: '请输入正确的邮箱格式',
            trigger: ['blur', 'change'],
          },
        ],
        invRequestUrl: [
          { required: true, message: '请输入请求地址', trigger: 'blur' },
        ],
        // appKey: [{ required: true, message: '请输入AppKey', trigger: 'blur' }],
        // appSecret: [
        //   { required: true, message: '请输入AppSecret', trigger: 'blur' },
        // ],
        invResponseUrl: [
          { required: true, message: '请输入开票回调地址', trigger: 'blur' },
        ],
        loginOrgCode: [
          { required: false, message: '请输入主数据单位编号', trigger: 'blur' },
        ],
        stopTime: [
          {
            required: false,
            message: '请输入停止开票开始结束时间',
            trigger: 'blur',
          },
        ],
      },
      // 开票信息配置
      invoicingForm: {
        drawer: '', // 开票人
        payee: '', // 收费人
        reviewer: '', // 复核人
      },
      invoicingRules: {
        drawer: [{ required: true, message: '请输入开票人', trigger: 'blur' }],
        payee: [{ required: true, message: '请输入收费人', trigger: 'blur' }],
        reviewer: [
          { required: true, message: '请输入复核人', trigger: 'blur' },
        ],
      },
      // 开票商品配置
      loading: false,
      goodsTableData: [],
      goodsTableColumn: [
        { type: 'seq', title: '序号', minWidth: 60 },
        { field: 'code', title: '商品编号', minWidth: 120 },
        { field: 'name', title: '商品名称', minWidth: 150 },
        { field: 'taxRate', title: '税率(%)', minWidth: 100 },
        { field: 'specification', title: '规格型号', minWidth: 140 },
        { field: 'measureUnit', title: '计量单位', minWidth: 120 },
        { field: 'goodsCode', title: '税收分类编码', minWidth: 150 },
        { field: 'goodsName', title: '税收分类名称', minWidth: 160 },
        {
          field: 'taxFlag',
          title: '含税标志',
          minWidth: 120,
          // slots: {
          //   default: ({ row }) => {
          //     return row.taxFlag ? '是' : '否';
          //   },
          // },
        },
      ],
      goodsTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      goodsModalConfig: {
        addBtn: false,
        editBtn: false,
        delBtn: false,
        viewBtn: false,
        menu: false,
      },
      channelList: [
        {
          label: '乐企',
          value: '1',
        },
      ],
      dialogTitle: '',
    };
  },
  async mounted() {
    await this.getSellerData();
    await this.getGoodsData();
  },
  methods: {
    // 获取销方信息
    async getSellerData() {
      let params = {
        channel: 1,
      };
      const [err, res] = await getSellerConfig(params);
      if (err) return;
      this.enterpriseForm = {
        channel: res.data.channel, // 开票服务商（已存在）
        invoiceName: res.data.invoiceName, // 企业名称
        taxpayerNo: res.data.taxpayerNo, // 纳税人识别号
        address: res.data.address, // 地址
        phone: res.data.phone, // 电话
        bank: res.data.bank, // 开户行
        acctNo: res.data.acctNo, // 开户账号
        email: res.data.email, // 邮箱
        invRequestUrl: res.data.invRequestUrl, // 请求地址
        invResponseUrl: res.data.invResponseUrl, // 开票回调
        loginOrgCode: res.data.loginOrgCode,
        stopTime: [], // 停止开票开始结束时间
      };
      if (res.data?.stopStartTime && res.data?.stopEndTime) {
        this.enterpriseForm.stopTime = [
          res.data?.stopStartTime,
          res.data?.stopEndTime,
        ];
      }
      if (this.enterpriseForm.channel == 1) {
        this.showLoginOrgCode = true;
        this.enterpriseRules.loginOrgCode[0].required = true;
      } else {
        this.showLoginOrgCode = false;
        this.enterpriseRules.loginOrgCode[0].required = false;
      }
      this.invoicingForm = {
        drawer: res.data.drawer, // 开票人
        payee: res.data.payee, // 收费人
        reviewer: res.data.reviewer, // 复核人
      };
    },
    // 获取开票商品配置信息
    async getGoodsData() {
      let params = {
        pageNum: this.goodsTablePage.currentPage,
        pageSize: this.goodsTablePage.pageSize,
      };
      this.loading = true;
      const [err, res] = await getInvGoodsConfigByPage(params);
      this.loading = false;
      if (err) return;
      const { data, total } = res;
      this.goodsTableData = data;
      this.goodsTablePage.total = total;
    },
    // 编辑
    handleEdit() {
      this.goodsTableColumn.push({
        title: '操作',
        slots: { default: 'operate' },
        width: 200,
        align: 'center',
        fixed: 'right',
      });
      this.isEdit = true;
    },
    // 新增行数据
    handleRowAdd() {
      this.$refs.addOrEditModal.form = {
        code: '', // 商品编码
        name: '', // 商品名称
        taxRate: '', // 税率(%)
        specification: '', // 规格型号
        goodsCode: '', // 税费分类编码
        goodsName: '', // 税收分类名称
        zeroTaxType: '', // 费用类型
        taxFlag: '', // 含税标志（非必填）
        measureUnit: '', // 计量单位（非必填）
      };
      this.dialogTitle = '新增';
      this.$refs.addOrEditModal.dialogVisible = true;
    },
    // 编辑行数据
    handleRowEdit(row) {
      console.log(row);
      this.$refs.addOrEditModal.form = { ...row };
      this.dialogTitle = '编辑';
      this.$refs.addOrEditModal.dialogVisible = true;
    },
    // 删除行数据
    handleRowDelete(row) {
      this.$confirm('确定删除该充电站吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const [err, res] = await deleteGoodsConfig({
            id: row.id,
          });
          if (err) return;
          this.$message({
            message: '删除成功',
            type: 'success',
          });
          this.getGoodsData();
        })
        .catch(() => {});
    },
    // 取消
    handleCancel() {
      this.isEdit = false;
      const obj = this.goodsTableColumn;
      const keys = Object.keys(obj);
      const lastKey = keys[keys.length - 1];
      if (lastKey) {
        delete obj[lastKey];
      }
      this.goodsTableColumn = obj;
    },
    // 保存
    async handleConfirm() {
      let enterpriseFormValid = false;
      let invoicingFormValid = false;
      this.$refs.enterpriseForm.validate(async (valid) => {
        enterpriseFormValid = valid;
      });
      this.$refs.invoicingForm.validate(async (valid) => {
        invoicingFormValid = valid;
      });
      if (enterpriseFormValid && invoicingFormValid) {
        let params = {
          ...this.enterpriseForm,
          ...this.invoicingForm,
          stopStartTime:
            this.enterpriseForm?.stopTime.length > 0
              ? this.enterpriseForm.stopTime[0]
              : '',
          stopEndTime:
            this.enterpriseForm?.stopTime.length > 0
              ? this.enterpriseForm.stopTime[1]
              : '',
        };
        const [err, res] = await saveOrUpdateSellerConfig(params);
        if (err) {
          return;
        }
        this.$message({
          message: '保存成功',
          type: 'success',
        });
        this.handleCancel();
      }
    },
    // 弹窗确定
    handleModalConfirm() {
      this.$refs.addOrEditModal.dialogVisible = false;
      this.getGoodsData();
    },
  },
};
</script>

<style lang="scss" scoped>
.info-card {
  margin: 16px 0 16px 0;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  .form-wrap {
    padding: 0 16px 16px 16px;
    .custom-header {
      background: -webkit-gradient(
          linear,
          left top,
          left bottom,
          from(rgba(0, 149, 255, 0.5)),
          to(rgba(87, 152, 255, 0))
        ),
        #f5faff;
      background: linear-gradient(
          180deg,
          rgba(0, 149, 255, 0.5) 0%,
          rgba(87, 152, 255, 0) 100%
        ),
        #f5faff;
      background-repeat: no-repeat;
    }
  }
}

.card-head {
  // position: relative;
  height: 56px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  margin-top: -20px;
  .card-head-text {
    flex: 1;
    width: 520px;
    height: 26px;
    background-image: url('~@/assets/images/bg-title.png');
    background-size: 520px 26px;
    background-repeat: no-repeat;
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
    padding-left: 36px;
    color: #21252e;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      left: -3px; /* 调整这个值来改变边框的宽度 */
      width: 0;
      border-top: 3px solid transparent;
      border-bottom: 3px solid transparent;
      border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
    }
  }
}

.bottom-wrap {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 86px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-color: #ffffff;
  padding-right: 32px;
  box-sizing: border-box;
  z-index: 5;
}
</style>
