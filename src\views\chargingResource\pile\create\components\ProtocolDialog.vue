<template>
  <el-dialog
    title="新增协议规则"
    :visible.sync="visible"
    width="600px"
    @close="handleClose"
  >
    <el-form
      :model="form"
      :rules="rules"
      ref="protocolForm"
      label-width="180px"
    >
      <el-form-item label="所属通讯模块协议" prop="protocolCode" >
        <el-select
          v-model="form.protocolCode"
          placeholder="请选择"
          @change="handleProtocolChange"
        >
          <el-option
            v-for="item in protocolOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="计费模式" prop="chargeMode">
        <el-radio-group v-model="form.chargeMode">
            <el-radio
              v-for="dict in dict.type.ls_charging_charge_mode"
              :key="dict.value"
              :label="dict.value"
            >
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
      </el-form-item>

      <el-form-item label="下发分时段数限制" prop="issuePeriod" v-if="form.chargeMode === '0202'">
        <div style="display: flex;">
          <el-input-number
            v-model="form.issueMinPeriod"
            placeholder="请输入"
            :min="0"
            :controls="false"
            :precision="0"
          ></el-input-number>
           ~ 
          <el-input-number
            v-model="form.issueMaxPeriod"
            placeholder="请输入"
            :min="0"
            :controls="false"
            :precision="0"
          ></el-input-number>
        </div>
       
      </el-form-item>

      <el-form-item label="拆分策略" prop="timeShareSplit" v-if="form.chargeMode === '0202'">
        <el-select
          v-model="form.timeShareSplit"
          placeholder="请选择"
        >
          <el-option
            v-for="item in dict.type.ls_charging_time_share_split"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>


      <el-form-item label="上传账单格式" prop="uploadBillFormat" v-if="form.chargeMode === '0201'">
        <el-select
          v-model="form.uploadBillFormat"
          placeholder="请选择"
        >
          <el-option
            v-for="item in dict.type.ls_charging_time_share_match_common_pile"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="上传账单格式" prop="uploadBillFormat" v-if="form.chargeMode === '0202'">
        <el-select
          v-model="form.uploadBillFormat"
          placeholder="请选择"
        >
          <el-option
            v-for="item in dict.type.ls_charging_time_share_match_time"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="下发启动是否携带计费" prop="carryFlag">
        <el-select v-model="form.carryFlag" placeholder="请选择">
            <el-option
                    v-for="item in dict.type.ls_charging_status"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="二维码最大长度" prop="codeMaxLength">
        <el-input
          v-model="form.codeMaxLength"
          placeholder="请输入数字，0代表不支持下发"
        ></el-input>
      </el-form-item>

      <el-form-item label="下发二维码规则" prop="qrCodeRule">
        <el-input
          v-model="form.qrCodeRule"
          placeholder="请输入数字，0代表不支持下发"
        ></el-input>
      </el-form-item>

      <el-form-item label="命名规则" prop="deviceNameRule">
        <el-input
          v-model="form.deviceNameRule"
          placeholder="请输入数字，0代表不支持下发"
        ></el-input>
        <div class="tip-text">仅对IOE起作用</div>
      </el-form-item>

      <el-form-item label="计费策略生效规则" prop="chargingStrategyRule">
        <el-select v-model="form.chargingStrategyRule" placeholder="请选择">
          <el-option
                    v-for="item in dict.type.ls_charging_chargingStrategyRule"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
        </el-select>

        
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>

import {
  getComiiProtocolList,
  addProtocolRule,
} from '@/api/pile/index/'
export default {
  name: 'ProtocolDialog',
  dicts: [
    'ls_charging_charge_mode',
    'ls_charging_time_share_match_common_pile',
    'ls_charging_time_share_match_time',
    'ls_charging_status',
    'ls_charging_chargingStrategyRule',
    'ls_charging_time_share_split',
  ],
  data() {
    return {
      visible: false,
      form: {
        protocolCode: '',
        chargeMode: '0201',
        issueMinPeriod: '',
        issueMaxPeriod:'',
        timeShareSplit:'',
        uploadBillFormat: '',
        carryFlag: '',
        codeMaxLength: '',
        qrCodeRule: '',
        deviceNameRule: '',
        chargingStrategyRule: '',
      },
      rules: {
        protocolCode: [
          { required: true, message: '请选择上传账单格式', trigger: 'change' },
        ],
        chargeMode: [
          { required: true, message: '请选择计费模式', trigger: 'change' },
        ],
        // issuePeriod: [
        //   { required: true, message: '请输入下发分时段数限制', trigger: 'blur' },
        //   { validator: this.validateIssuePeriod, trigger: 'blur' },
        // ],
        timeShareSplit: [
        { required: true, message: '请选择拆分策略', trigger: 'change' },
        ],
        uploadBillFormat: [
          { required: true, message: '请选择上传账单格式', trigger: 'change' },
        ],
        carryFlag: [
          { required: true, message: '请选择是否携带计费', trigger: 'change' },
        ],
        codeMaxLength: [
          {
            required: true,
            message: '请输入二维码最大长度',
            trigger: 'change',
          },
        ],
        qrCodeRule: [
          {
            required: true,
            message: '请输入下发二维码规则',
            trigger: 'change',
          },
        ],
        deviceNameRule: [
          { required: true, message: '请输入命名规则', trigger: 'change' },
        ],
        chargingStrategyRule: [
          {
            required: true,
            message: '请选择计费策略生效规则',
            trigger: 'change',
          },
        ],
      },
      protocolOptions: [], // 通讯模块协议选项
      billFormatOptions: [], // 上传账单格式选项
      withBillingOptions: [], // 是否携带计费选项
      billingRuleOptions: [], // 计费策略生效规则选项
    };
  },
  mounted() {
    // this.getComiiProtocolList();
  },
  methods: {
    // 自定义校验规则校验函数
    // validateIssuePeriod(rule, value, callback){
    //         console.log(rule,value,'111')
             
    //     },
    // 获取通讯模块协议
    async getComiiProtocolList() {
      const [err, res] = await getComiiProtocolList({});
      if (err) return

      const { data } = res
      const list = []
      data.forEach(item => {
        list.push({
          label: item.protocolName,
          value: item.protocolCode,
        })
      })
      this.protocolOptions = list
    },

    show() {
      this.visible = true;
    },
    handleClose() {
      this.$refs.protocolForm?.resetFields();
      this.visible = false;
    },
    handleCancel() {
      this.handleClose();
    },
    handleSubmit() {
      this.$refs.protocolForm.validate(async (valid) => {
        if (valid) {

          const {
            protocolCode,
            chargeMode,
            issueMinPeriod,
            issueMaxPeriod,
            timeShareSplit,
            uploadBillFormat,
            carryFlag,
            codeMaxLength,
            qrCodeRule,
            deviceNameRule,
            chargingStrategyRule,
          } = this.form;

          if(chargeMode === '0202') {
            if(!issueMinPeriod || !issueMaxPeriod || issueMaxPeriod< issueMinPeriod) {
              return this.$message.error('请输入正确的下发分时段数限制') 
            }
          }


          const obj = this.protocolOptions.find(item => item.value === protocolCode)

          const protocolName = obj.label

          const params = {
            protocolCode,
            protocolName,
            chargeMode,
            uploadBillFormat,
            carryFlag,
            codeMaxLength,
            qrCodeRule,
            deviceNameRule,
            chargingStrategyRule,
            issueMinPeriod,
            issueMaxPeriod,
            timeShareSplit,
          }

          const [err,res] = await addProtocolRule(
            params
          )

          if (err){ 
            return this.$message.error(err.message || '添加通讯模块协议失败');
          }
          this.$message({
            type: 'success',
            message: '添加成功!'
          });


          this.$emit('submit');
          this.handleClose();
        }
      });
    },

    handleProtocolChange() {
      const protocolCode = this.form.protocolCode
      const obj = this.protocolOptions.find(item => item.value === protocolCode)

      this.form.deviceNameRule = obj.label + '-##equipNo##'
    },

    handleBillingModeChange() {}
  },
};
</script>

<style lang="scss" scoped>
.el-select {
  width: 100%;
}
.tip-text {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
}
</style>
