<template>
    <div class="container container-float " >
        <div class="table-wrap">
            <BuseCrud
                ref="crud"
                :loading="loading"
                :filterOptions="filterOptions"
                :tablePage="tablePage"
                :tableColumn="tableColumn"
                :tableData="tableData"
                :pagerProps="pagerProps"
                :modalConfig="modalConfig"
                  class="buse-wrap-organization"
                @loadData="loadData"
            >
            <template slot="defaultHeader">
                <div>
                    <div class="card-head">
                        <div class="card-head-text">停车优惠配置</div>

                        <el-button 
                            type="primary" 
                            @click="() => handleCreate()">
                            新建
                        </el-button>
                    </div>
                    
                    
                    
                </div>
                
            </template>
                <template slot="status" slot-scope="{ row }">
                <el-switch
                    v-model="row.status"
                    @change="changeStatus(row)"
                ></el-switch>
                </template>

                <template slot="operate" slot-scope="{ row }">
                    <div class="menu-box">
                        <el-button
                            type="primary"
                            plain
                            
                            @click="hanleEdit(row)"
                        >
                            编辑
                        </el-button>
                    
                    </div>
                
                </template>

            </BuseCrud>
        </div>

        <ParkDiscountMoadl ref="ParkDiscountMoadl" />
    </div>
    
  </template>
  
  <script>

  import ParkDiscountMoadl from './components/parkDiscountMoadl.vue'
  
    export default {
    components: {
        ParkDiscountMoadl,
    },
    dicts: [],
    data() {
        return {
            loading: false,
            tablePage: { total: 0, currentPage: 1, pageSize: 10 },
            tableColumn:[
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60, 
                },
                {
                    field: 'stationName',
                    title: '场站名称',
                    minWidth: 120, 
                },
                {
                    field: 'manufacturer',
                    title: '道闸厂商',
                    minWidth: 120, 
                },
                {
                    field: 'parkId',
                    title: '车场ID',
                    minWidth: 120, 
                },
                {
                    field: 'discountType',
                    title: '优惠类型',
                    minWidth: 120, 
                },
                {
                    field: 'discountDesc',
                    title: '优惠方案',
                    minWidth: 120, 
                },
                {
                    field: 'updateTime',
                    title: '更新时间',
                    minWidth: 120,
                },
                {
                    field: 'status',
                    title: '状态',
                    minWidth: 50,
                    fixed: 'right',
                    slots: { default: 'status' },
                },
                {
                    title: '操作',
                    slots: { default: 'operate' },
                    width: 100,
                    align: 'center',
                    fixed: 'right',
                },
    
            ],
            tableData: [],
            pagerProps: {
            layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
            },
            params: {
                stationId: '',
                manufacturer: '',
                discountType: '',
            },
        };
    },

    computed: {
        filterOptions() {
            return {
                config: [
                    {
                        field: 'stationId',
                        title: '所属站点',
                        element: 'el-select',
                        props: {
                            placeholder: '请选择',
                            options: []  
                        }
                    },
                    {
                        field: 'manufacturer',
                        title: '道闸厂商',
                        element: 'el-select',
                        props: {
                            placeholder: '请选择',
                            options: []  
                        }
                    },
                    {
                        field: 'discountType',
                        title: '优惠类型',
                        element: 'el-select',
                        props: {
                            placeholder: '请选择',
                            options: []  
                        }
                    },
                ],
            params: this.params,
            };
        },
        modalConfig() {
            return {
                addBtn: false,
                viewBtn: false,
                menu: false,
                editBtn: false,
                delBtn: false,
            }
        },
    },
    mounted() {
        this.loadData();
    },
    methods: {
        async loadData() {
            const {
                stationId,
                manufacturer,
                discountType,
            } = this.filterOptions.params
            this.tableData = [
            {
                stationName: '北京朝阳CBD停车场',
                manufacturer: '海康威视',
                parkId: 'PARK1001',
                discountType: '新用户优惠',
                discountDesc: '首单立减5元',
                updateTime: '2023-08-15 14:30:45',
                status: true
            },
            {
                stationName: '上海陆家嘴金融停车场',
                manufacturer: '大华',
                parkId: 'PARK1002',
                discountType: '节假日优惠',
                discountDesc: '周末8折优惠',
                updateTime: '2023-08-14 09:12:33',
                status: false
            },
            {
                stationName: '广州天河智慧城停车场',
                manufacturer: '宇视',
                parkId: 'PARK1003',
                discountType: '会员折扣',
                discountDesc: '黄金会员享9折',
                updateTime: '2023-08-13 16:45:21',
                status: false
            },
            {
                stationName: '深圳南山科技园停车场',
                manufacturer: '科拓',
                parkId: 'PARK1004',
                discountType: '满减优惠',
                discountDesc: '满100减20元',
                updateTime: '2023-08-12 11:03:57',
                status: true
            },
            {
                stationName: '杭州未来科技城停车场',
                manufacturer: '捷顺',
                parkId: 'PARK1005',
                discountType: '时段优惠',
                discountDesc: '夜间时段5折',
                updateTime: '2023-08-11 18:20:15',
                status: false
            }
            ]
        },
        
        // 新建
        handleCreate() {
            this.$refs.ParkDiscountMoadl.dialogVisible = true
        },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }
   
 
  .table-wrap {
    ::v-deep .bd3001-table-select-box {
        display: none;
    }
    ::v-deep .bd3001-header  {
        display: block;
    }
    ::v-deep .bd3001-button {
        display: block !important;
    }
    
    .card-head {
        // position: relative; 
        height: 56px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        margin-top: -20px;
        .card-head-text {
        flex:1;
        width: 520px;
            height: 26px;
            background-image: url('~@/assets/images/bg-title.png');
            background-size: 520px 26px;
            background-repeat: no-repeat;
            font-size: 20px;
            font-style: normal;
            font-weight: 500;
            line-height: 16px;
            padding-left: 36px;
            color: #21252e;
        &::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: -3px; /* 调整这个值来改变边框的宽度 */
            width: 0;
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
            border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
        }
        }   

    }

    .card-head-after {
        width: 100%;
        height: 1px;
        background-color: #DCDEE2;
        margin-bottom: 16px;
    }
    .info-wrap {
        margin-top: 16px;
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .info-item {
            background-color: #FAFBFC;
            flex: 1 1 0;
            // min-width: 180px;
            
            border-radius: 5px;
            padding: 8px 24px;
            box-sizing: border-box;
            // margin-right: 16px;
            display: flex;
            .info-icon {
                width: 42px;
                height: 42px;
            }
            .info-right-wrap {
                flex:1;
                margin-left: 8px;
                .info-title {
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 14px;
                    margin-bottom: 8px;
                }
                .info-number {
                    font-size: 20px;
                    font-weight: 500;
                    .info-unit {
                        font-size: 14px;
                        font-weight: 400;
                    }
                }
            }
        }
        .info-item:last-child {
            margin-right: 0;
        }
    }

    .top-button-wrap {
        display:flex;
        margin: 16px 0;
    }
}
 
  </style>
  