import request from '@/utils/request'

const baseUrl = '/vehicle-grid-admin'

// 需求列表
export function getRequirementList(data) {
  return request({
    url: baseUrl + '/adjustment/requirement/list',
    method: 'post',
    data: data
  })
}

// 需求获取
export function syncRequirements(data) {
  return request({
    url: baseUrl + '/adjustment/requirement/sync',
    method: 'post',
    data: data
  })
}

// 需求录入
export function addRequirements(data) {
  return request({
    url: baseUrl + '/adjustment/requirement/add',
    method: 'post',
    data: data
  })
}

// 需求详情
export function getRequirementDetails(data) {
  return request({
    url: baseUrl + '/adjustment/requirement/detail',
    method: 'post',
    data: data
  })
}

// 需求编辑
export function updateRequirements(data) {
  return request({
    url: baseUrl + '/adjustment/requirement/update',
    method: 'post',
    data: data
  })
}

// 参加
export function confirmRequirements(data) {
  return request({
    url: baseUrl + '/adjustment/requirement/confirm',
    method: 'post',
    data: data
  })
}

// 不参加
export function denyRequirements(data) {
  return request({
    url: baseUrl + '/adjustment/requirement/deny',
    method: 'post',
    data: data
  })
}

// 需求删除
export function deleteRequirements(data) {
  return request({
    url: baseUrl + '/adjustment/requirement/delete',
    method: 'post',
    data: data
  })
}

// 需求图表
export function getRequirementGraph(data) {
  return request({
    url: baseUrl + '/adjustment/requirement/graph',
    method: 'post',
    data: data
  })
}