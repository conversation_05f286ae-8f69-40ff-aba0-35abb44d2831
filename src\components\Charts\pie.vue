<template>
    <div v-loading="loading">
      <div ref="chart" :id="id" :style="`height: ${height}`"></div>
    </div>
  </template>
  
  <script>
  import * as echarts from 'echarts/core';
  import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON> } from 'echarts/charts';
  import { CanvasRenderer } from 'echarts/renderers';
  import {
    GridComponent,
    TooltipComponent,
    LegendComponent,
    DataZoomComponent,
    ToolboxComponent,
    GraphicComponent,
  } from 'echarts/components';
  // import moment from 'moment';
  import { throttle } from 'lodash';
  
  echarts.use([
  <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>hart,
    CanvasRenderer,
    GridComponent,
    TooltipComponent,
    LegendComponent,
    DataZoomComponent,
    ToolboxComponent,
    GraphicComponent,
  ]);
  
  const defaultXAxis = {
    type: 'category',
    boundaryGap: true,
    triggerEvent: true,
    axisLine: {
      lineStyle: {
        color: '#E9EBF0',
      },
    },
    axisLabel: {
      color: '#292B33',
    },
    axisTick: {
      show: false,
    },
    splitLine: {
      show: false,
    },
  };
  
  export default {
    name: '<PERSON><PERSON><PERSON>',
    props: {
      id: String,
      height: {
        type: String,
        default: '200px',
      },
      data: {
        type: Array,
        default: () => [],
      },
      loading: {
        type: Boolean,
        default: false,
      },
      notMerge: {
        type: Boolean,
        default: false,
      },
      unit: {
        type: String,
        default: '',
      },
      centText: {
        type: String,
        default: '总数',
      },
      radius: {
        type: Array,
        default: () => ['40%', '50%'],
      },
      isWarp: {
        type: Boolean,
        default: true, // 是否换行
      },
      legendLeft: {
        type: String,
        default: '70%', // 图例位置
      },
      seriesCenter: {
        type: Array,
        default: () => ['35%', '50%'],
      }
    },
    data() {
      return {
        myChart: null,
        resizeFn: null,
      };
    },
    watch: {
      data: {
        handler(n) {
          if (!n || !n.length) {
            // 这里为了解决echarts图表series从有值变为空，但是图表不会更新的问题
            this.myChart &&
              this.myChart.setOption(
                {
                  series: [],
                },
                true
              );
            this.myChart &&
              this.myChart.showLoading({
                text: '暂无数据',
                showSpinner: false,
                textColor: 'rgba(0, 0, 0, 0.45)',
                maskColor: 'rgba(255, 255, 255, 1)',
                fontSize: '16px',
                fontWeight: '500',
              });
            return;
          }
  
          this.myChart && this.myChart.hideLoading();
          this.myChart &&
            this.myChart.setOption(this.getOption(n), this.unit);
        },
        deep: true,
      },
    },
    methods: {
      init() {
        this.$nextTick(() => {
          let dom = this.$refs.chart;
          if (dom) {
            this.myChart = echarts.init(dom);
            const options = this.getOption(this.data,this.unit);
            if (options) {
              this.myChart.setOption(options);
            }
            // 监听鼠标移入移出事件
            this.myChart.on('showTip', (params) => {
              this.$emit('showTip', params);
            });
            this.myChart.on('hideTip', () => {
              this.$emit('hideTip');
            });
          }
        });
      },
      getOption(data = [],unit) {
        console.log( this._props,' getOption')

        const isWarp = this._props.isWarp;

        const legendLeft = this._props.legendLeft;

        const seriesCenter = this._props.seriesCenter;
         // 计算总数
         var total = data.reduce((sum, item) => sum + item.value, 0);
        return {
          tooltip: {
             trigger: 'item',
          },
        //   legend: {
        //         top: 'center',
        //         left: '10%'
        //     },
        legend: {
                orient: 'vertical',
                left: legendLeft,
                top: 'center',
                // 自定义图例显示格式
                formatter: function (name) {
                    // 查找对应的数据项
                    var item = data.find(item => item.name === name);
                    if (item) {
                        // 计算百分比
                        return isWarp?`${name} \n ${unit}${item.value}`:`${name}  ${unit}${item.value}`;
                    }
                    return name;
                },
                // 自定义图例文本样式
                textStyle: {
                    color: '#333',
                    fontSize: 16,
                    fontWeight: 'bold',
                    lineHeight: 20,
                },
                // 自定义图例项的样式
                itemStyle: {
                    borderRadius: 5
                },
                // 自定义图例图标样式
                icon: 'circle'
            },
          series:[
            {
                type: 'pie',
                center: seriesCenter, 
                radius: this.radius,
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 5,
                    borderColor: '#fff',
                    borderWidth: 2,
                    label: {
                        show: false,
                        position: 'center'
                    },
                },
                labelLine: {
                    show: false
                },
                label: {
                        // show: false,
                        // 显示在中心
                        position: 'center', 
                        // 自定义标签内容
                        formatter: `${this.centText}\n ${unit}${total} ` ,
                        // 标签样式
                        textStyle:{
                            color: '#333',
                            fontSize: 16,
                            fontWeight: 'bold',
                            lineHeight: 20,
                            textAlign: 'center'
                        }
                },
                data: data

            }
          ]

        };
      },
      showLoading() {
        this.myChart && this.myChart.showLoading();
      },
      resize() {
        this.myChart && this.myChart.resize && this.myChart.resize();
      },
    },
    mounted() {
      this.resizeFn = throttle(() => {
        this.resize();
      }, 1000);
      // 监听窗口变化
      window.addEventListener('resize', this.resizeFn);
      this.init();
    },
    beforeDestroy() {
      if (this.myChart) {
        this.myChart.dispose();
        this.myChart = null;
      }
      window.removeEventListener('resize', this.resizeFn);
    },
  };
  </script>
  
  <style lang="less" scoped></style>
  