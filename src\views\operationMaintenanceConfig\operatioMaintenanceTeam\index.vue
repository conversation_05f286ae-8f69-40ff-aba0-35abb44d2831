<template>
    <div class="container container-float " >
        <div class="table-wrap">
            <BuseCrud
                ref="crud"
                :loading="loading"
                :filterOptions="filterOptions"
                :tablePage="tablePage"
                :tableColumn="tableColumn"
                :tableData="tableData"
                :pagerProps="pagerProps"
                :modalConfig="modalConfig"
                class="buse-wrap-organization"
                @loadData="loadData"
            >
            <template slot="defaultHeader">
                <div>
                    <div class="card-head">
                        <div class="card-head-text">运维班组管理</div>

                        <div class="top-button-wrap">
                            <el-button
                                type="primary"
                                @click="() => handleAdd()"
                            >
                                新增
                            </el-button>

                        </div>
                    </div>
                    

                   

                    
                </div>
                
            </template>

                    <template slot="operate" slot-scope="{ row }">
                        <el-dropdown trigger="click">
                            <el-button
                                class="button-border"
                                type="primary"
                                plain
                            >
                                操作
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item >
                                    <div v-if="row.status === '0'"  @click="hanleStatus(row, '1')">
                                        启用
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item >
                                    <div v-if="row.status === '1'"  @click="hanleStatus(row, '0')">
                                        停用
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item >
                                    <div   @click="handleEdit(row)">
                                        编辑
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item >
                                    <div   @click="() => handleDelete(row)">
                                        删除
                                    </div>
                                </el-dropdown-item>

                                

                            </el-dropdown-menu>
                        </el-dropdown>
                    
                    </template>

            </BuseCrud>
        </div>


    </div>
    
  </template>
  
  <script>

  import {
    getOperationMaintenanceTeamList,
    updateOperationMaintenanceTeamStatus,
    deleteOperationMaintenanceTeam,
  } from "@/api/operationMaintenanceConfig/operationMaintenanceTeam";

  import StatusDot from '@/components/Business/StatusDot';

  
    export default {
    components: {
        StatusDot,
    },
    dicts: [],
    data() {
      return {
        loading: false,
            tablePage: { total: 0, currentPage: 1, pageSize: 10 },
            tableColumn: [
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60,
                },
                {
                    field: 'memberUserNames',
                    title: '班组成员',
                    minWidth: 180,
                },
                {
                    field: 'leaderStationName',
                    title: '负责充电站',
                    minWidth: 180,
                },
                {
                    field: 'leaderUserName',
                    title: '班组负责人',
                    minWidth: 180,
                },
                {
                    field: 'status',
                    title: '状态',
                    minWidth: 180,
                    slots: {
                        // 自定义render函数
                        default: ({ row }) => {
                            return (
                                <StatusDot
                                    value={row.status}
                                    dictValue={this.enableStatusList}
                                    colors={['success', 'danger']}
                                ></StatusDot> 
                            );
                        },
                    }
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    minWidth: 180,
                },
                
                {
                    title: '操作',
                    slots: { default: 'operate' },
                    width: 80,
                    align: 'center',
                    fixed: 'right',
                },
            ],
            tableData: [],
            params: {
                teamName: '',
                enableStatus: '',
            },

            enableStatusList: [
                {   label: '启用', value: '1' },
                {   label: '停用', value: '0' },
            ],
      };
    },

    computed: {
        filterOptions() {
        return {
            config: [
                {
                    field: 'teamName',
                    title: '班组名称',
                    element: 'el-input',
                },
                {
                    field: 'enableStatus',
                    title: '状态',
                    element: 'el-select',
                    props: {
                        placeholder: '请选择',
                        options: this.enableStatusList
                    }
                },

            ],
            params: this.params,
        };
        },
        modalConfig() {
        return {
            addBtn: false,
            viewBtn: false,
            menu: false,
            editBtn: false,
            delBtn: false,
        }
        },
    },
    mounted() {
        this.loadData()
    },
    methods: {
         // 加载数据
         async loadData() {
            const {
                teamName,
                enableStatus,
            } = this.params

            const params = {
                teamName,
                enableStatus,
                pageNum: this.tablePage.currentPage,
                pageSize: this.tablePage.pageSize,
            }

            this.loading = true;
            const [err, res] = await getOperationMaintenanceTeamList(params)

            this.loading = false;
            if (err) return;

            const { data, total } = res;

            this.tableData = data;
            this.tablePage.total = total;

        },

        // 新增
        handleAdd() {
            this.$router.push({
                path: '/v2g-charging/operationMaintenanceConfig/operatioMaintenanceTeam/create',
            })
        },

        // 编辑
        handleEdit(row) {
            this.$router.push({
                path: '/v2g-charging/operationMaintenanceConfig/operatioMaintenanceTeam/create',
                query: {
                    teamId: row.teamId,
                },
            })
        },


        // 启用停用
        hanleStatus(row, status) {
            updateOperationMaintenanceTeamStatus

            const message = status === '1' ? '启用' : '停用'

            this.$confirm(`确定${message}该班组吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })   
                .then( async () => {
                    const {
                        teamId
                    } = row
                    const [err,res] = await updateOperationMaintenanceTeamStatus(
                        {
                            teamId,
                            enableStatus: status
                        }
                    )
                    if(err) return
                            
                    this.$message.success(`${message}成功`)

                    this.loadData()
                })
                .catch(() => {});

               
        },

        // 删除
        handleDelete(row) {
            this.$confirm(`确定删除该班组吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })  .then( async () => {
                    const {
                        teamId
                    } = row
                    const [err,res] = await deleteOperationMaintenanceTeam(
                        {
                            teamId,
                        }
                    )
                    if(err) return
                            
                    this.$message.success(`删除成功`)

                    this.loadData()
                })
                .catch(() => {});

        }
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }
   
  

.table-wrap {
  ::v-deep .bd3001-table-select-box {
      display: none;
  }
  ::v-deep .bd3001-header  {
      display: block;
  }
  ::v-deep .bd3001-button {
      display: block !important;
  }
  
  .card-head {
      // position: relative; 
      height: 56px;
      padding: 0 16px;
      display: flex;
      align-items: center;
      margin-top: -20px;
      .card-head-text {
      flex:1;
      width: 520px;
          height: 26px;
          background-image: url('~@/assets/images/bg-title.png');
          background-size: 520px 26px;
          background-repeat: no-repeat;
          font-size: 20px;
          font-style: normal;
          font-weight: 500;
          line-height: 16px;
          padding-left: 36px;
          color: #21252e;
      &::before {
          content: '';
          position: absolute;
          top: 0;
          bottom: 0;
          left: -3px; /* 调整这个值来改变边框的宽度 */
          width: 0;
          border-top: 3px solid transparent;
          border-bottom: 3px solid transparent;
          border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
      }   

  }

  .card-head-after {
      width: 100%;
      height: 1px;
      background-color: #DCDEE2;
      margin-bottom: 16px;
  }
  .info-wrap {
      margin-top: 16px;
      display: flex;
      flex-wrap: wrap;
      gap: 16px;

      .info-item {
          background-color: #FAFBFC;
          flex: 1 1 0;
          // min-width: 180px;
          
          border-radius: 5px;
          padding: 8px 24px;
          box-sizing: border-box;
          // margin-right: 16px;
          display: flex;
          .info-icon {
              width: 42px;
              height: 42px;
          }
          .info-right-wrap {
              flex:1;
              margin-left: 8px;
              .info-title {
                  font-weight: 400;
                  font-size: 14px;
                  line-height: 14px;
                  margin-bottom: 8px;
              }
              .info-number {
                  font-size: 20px;
                  font-weight: 500;
                  .info-unit {
                      font-size: 14px;
                      font-weight: 400;
                  }
              }
          }
      }
      .info-item:last-child {
          margin-right: 0;
      }
  }

  .top-button-wrap {
      display:flex;
      margin: 16px 0;
      .set-btn {
        background-color: #FFFFFF;
        color: #292B33;
        border-color: #DFE1E5;
    }
  }
}

.button-border {
  border: 0.01rem solid #217AFF;
  color: #217AFF;
  background-color: #fff;
}


 
  </style>
  