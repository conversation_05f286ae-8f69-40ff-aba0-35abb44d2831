# BuseCrud 组件使用说明

## 简介

BuseCrud 是基于 Element UI 开发的表格组件，用于快速构建数据列表、筛选、分页等功能。该组件在本项目中被广泛应用于各类列表页面。

## 安装与注册

项目已通过 `src/register.js` 全局注册了 BuseCrud 组件：

```js
import BuseCrud from 'buse-components-element';
Vue.use(BuseCrud, {
  BuseCrud: {
    tableProps: {
      stripe: true,
      border: 'none',
      round: true,
    },
    pagerProps: {
      layouts: [
        'Total',
        'PrevPage',
        'JumpNumber',
        'PageCount',
        'NextPage',
        'Sizes',
      ],
    },
  },
  AutoFilters: {
    showCount: 5,
  },
});
```

## 基本用法

```vue
<BuseCrud
  ref="crud"
  :loading="loading"
  :filterOptions="filterOptions"
  :tablePage="tablePage"
  :tableColumn="tableColumn"
  :tableData="tableData"
  :pagerProps="pagerProps"
  :modalConfig="modalConfig"
  @loadData="loadData"
>
  <!-- 自定义插槽内容 -->
</BuseCrud>
```

## 属性说明

| 属性名 | 类型 | 说明 |
| --- | --- | --- |
| loading | Boolean | 表格加载状态 |
| filterOptions | Array | 筛选条件配置 |
| tablePage | Object | 分页信息，包含 total、currentPage、pageSize |
| tableColumn | Array | 表格列配置 |
| tableData | Array | 表格数据 |
| pagerProps | Object | 分页器配置 |
| modalConfig | Object | 模态框配置，如 `{ addBtn: false, menu: false }` |
| tableOn | Object | 表格事件监听，如 checkbox-change 等 |

## 事件说明

| 事件名 | 说明 |
| --- | --- |
| loadData | 加载数据事件，通常用于分页、筛选条件变化时重新加载数据 |

## 插槽说明

BuseCrud 提供了多种插槽用于自定义表格内容：

| 插槽名 | 说明 |
| --- | --- |
| defaultHeader | 表格顶部区域，通常用于放置标题和操作按钮 |
| operate | 操作列，通常用于放置操作按钮，如查看、编辑、删除等 |
| [field] | 根据字段名称的插槽，用于自定义某一列的内容 |

## 使用示例

### 基础列表页面

```vue
<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :modalConfig="modalConfig"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">列表标题</div>
            <div class="top-button-wrap">
              <el-button type="primary" @click="handleAdd">
                新增
              </el-button>
              <el-button type="primary" @click="handleOutput">
                导出
              </el-button>
            </div>
          </div>
        </template>
        
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button type="primary" plain @click="handleView(row)">
              查看
            </el-button>
            <el-button type="primary" plain @click="handleEdit(row)">
              编辑
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableColumn: [
        { type: 'seq', title: '序号', minWidth: 60 },
        { field: 'name', title: '名称', minWidth: 150 },
        { field: 'code', title: '编码', minWidth: 150 },
        { field: 'status', title: '状态', minWidth: 100 },
        { title: '操作', slot: 'operate', minWidth: 200 }
      ],
      modalConfig: { addBtn: false, menu: false },
      filterOptions: [
        { field: 'name', title: '名称', type: 'input' },
        { field: 'status', title: '状态', type: 'select', options: [] }
      ]
    };
  },
  methods: {
    loadData({ page, filters }) {
      this.loading = true;
      // 调用API获取数据
      // ...
      this.loading = false;
    },
    handleView(row) {
      // 查看详情
    },
    handleEdit(row) {
      // 编辑
    },
    handleAdd() {
      // 新增
    },
    handleOutput() {
      // 导出
    }
  }
};
</script>
```

### 自定义列内容

```vue
<template>
  <BuseCrud
    ref="crud"
    :tableColumn="tableColumn"
    :tableData="tableData"
  >
    <!-- 自定义状态列 -->
    <template slot="status" slot-scope="{ row }">
      <el-switch
        v-model="row.status"
        @change="changeStatus(row)"
      >
      </el-switch>
    </template>
  </BuseCrud>
</template>
```

### 复选框事件处理

```vue
<template>
  <BuseCrud
    ref="crud"
    :tableOn="{
      'checkbox-change': handleCheckboxChange,
      'checkbox-all': handleCheckboxChange,
    }"
  >
  </BuseCrud>
</template>

<script>
export default {
  methods: {
    handleCheckboxChange(selection) {
      // 处理选中项变化
      this.selection = selection;
    }
  }
}
</script>
```

## 常见使用场景

1. 列表页面：展示数据列表，支持分页、筛选、排序等功能
2. 详情页中的子表格：展示关联数据
3. 弹窗中的表格：如历史记录、关联数据选择等

## 注意事项

1. 表格列配置中，操作列通常使用 `slot: 'operate'` 来自定义
2. 使用 `ref="crud"` 可以获取组件实例，调用组件方法
3. 自定义列内容时，使用 `slot-scope="{ row }"` 获取行数据