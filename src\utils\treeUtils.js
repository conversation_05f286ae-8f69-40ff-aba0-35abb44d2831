/**
 * 根据指定字段在树状数据中查找并修改节点
 * @param {Array} tree 树状数据
 * @param {String} field 查找的字段名
 * @param {any} value 查找的字段值
 * @param {Object} updateData 要更新的数据对象
 * @param {String} childrenField 子节点字段名，默认为'list'
 * @returns {Boolean} 是否找到并修改了节点
 */
export function findAndModifyTreeNode(tree, field, value, updateData, childrenField = 'list') {
  if (!tree || !Array.isArray(tree) || tree.length === 0) {
    return false;
  }

  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    
    // 找到目标节点，合并更新数据
    if (node[field] === value) {
      Object.assign(node, updateData);
      return true;
    }
    
    // 递归查找子节点
    if (node[childrenField] && node[childrenField].length) {
      const found = findAndModifyTreeNode(
        node[childrenField],
        field,
        value,
        updateData,
        childrenField
      );
      if (found) {
        return true;
      }
    }
  }
  
  return false;
}