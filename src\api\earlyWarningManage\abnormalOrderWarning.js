import request from '@/utils/request';

const baseUrl = '/vehicle-charging-admin';

/**
 * @typedef {Object} WarningOrderPageRequest
 * @property {number} [pageNum=1] - 当前页码，默认为1
 * @property {number} [pageSize=10] - 每页显示条数，默认为10
 * @property {string} [id] - 规则配置ID
 * @property {string} [ruleName] - 规则名称
 * @property {string} [abnormalId] - 异常编号，对应 o_charge_order_except_rule 表 except_id
 * @property {string} [abnormalName] - 异常名称
 * @property {string} [abnormalType] - 异常类型: 01:超充, 02:逾期未支付, 03:未上送, 04:大额数据异常, 05:小额数据异常, 06:其他类异常
 * @property {string} [abnormalLevel] - 异常等级：01:低；02:中；03:高
 * @property {string} [createTimeStart] - 创建时间 - 左区间
 * @property {string} [createTimeEnd] - 创建时间 - 右区间
 * @property {string} [enable] - 启用状态
 */
/**
 * @typedef {Object} WarningOrderPageItem
 * @property {string} id - 规则配置ID
 * @property {string} ruleName - 规则名称
 * @property {string} abnormalId - 异常编号
 * @property {string} abnormalName - 异常名称
 * @property {string} abnormalType - 异常类型
 * @property {string} abnormalLevel - 异常等级
 * @property {string} createTimeStart - 创建时间左区间
 * @property {string} createTimeEnd - 创建时间右区间
 * @property {string} enable - 启用状态
 * @property {string} notifyChannelsSummaryDesc - 通知渠道（已汇总且翻译）
 */
/**
 * @typedef {Object} WarningOrderPageResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {Array<WarningOrderPageItem>} data - 返回数据
 * @property {number} total - 总条数
 */
// 分页列表接口
export function orderPage(params) {
  return request({
    url: baseUrl + '/warning/order/page',
    method: 'post',
    data: params,
  });
}

/**
 * @typedef {Object} WarningOrderDeleteRequest
 * @property {string} operateId - 操作ID
 */
/**
 * @typedef {Object} WarningOrderDeleteResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {boolean} data - 返回数据
 * @property {number} total - 总条数
 */
// 删除接口
export function OrderDelete(data) {
  return request({
    url: baseUrl + '/warning/order/delete',
    method: 'post',
    data: data,
  });
}

/**
 * @typedef {Object} WarningOrderChangeStatusRequest
 * @property {string} operateId - 操作ID
 */
/**
 * @typedef {Object} WarningOrderChangeStatusResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {boolean} data - 返回数据
 * @property {number} total - 总条数
 */
// 启用/停用接口
export function changeStatus(data) {
  return request({
    url: baseUrl + '/warning/order/changeStatus',
    method: 'post',
    data: data,
  });
}

/**
 * @typedef {Object} NotifyChannel
 * @property {string} notifyChannelType - 通知渠道类型
 * @property {string} notifyTemplateId - 推送模板ID
 */
/**
 * @typedef {Object} WarningOrderAddRequest
 * @property {string} ruleName - 规则名称
 * @property {string} abnormalId - 异常编号
 * @property {Array<NotifyChannel>} notifyChannels - 通知渠道配置列表
 */
/**
 * @typedef {Object} WarningOrderAddResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {boolean} data - 返回数据
 * @property {number} total - 总条数
 */
// 新增接口
export function orderAdd(data) {
  return request({
    url: baseUrl + '/warning/order/add',
    method: 'post',
    data: data,
  });
}

/**
 * @typedef {Object} WarningOrderUpdateRequest
 * @property {string} id - 主键ID
 * @property {Array<NotifyChannel>} notifyChannels - 通知渠道配置列表
 */
/**
 * @typedef {Object} WarningOrderUpdateResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {boolean} data - 返回数据
 * @property {number} total - 总条数
 */
// 编辑接口
export function orderUpdate(data) {
  return request({
    url: baseUrl + '/warning/order/update',
    method: 'post',
    data: data,
  });
}

/**
 * @typedef {Object} WarningOrderDetailNotifyChannel
 * @property {string} id
 * @property {string} warningBizType
 * @property {string} warningConfigId
 * @property {string} notifyChannelType
 * @property {string} notifyTemplateId
 */
/**
 * @typedef {Object} WarningOrderDetailData
 * @property {string} id - 主键id
 * @property {string} ruleName - 规则名称
 * @property {string} abnormalId - 异常编号
 * @property {number} enabled - 状态（0=停用; 1=启用）
 * @property {string} createdBy - 创建人
 * @property {string} createdTime - 创建时间
 * @property {string} updatedBy - 更新人
 * @property {string} updatedTime - 更新时间
 * @property {Array<WarningOrderDetailNotifyChannel>} notifyChannels - 通知渠道配置列表
 */
/**
 * @typedef {Object} WarningOrderDetailResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {WarningOrderDetailData} data - 返回数据
 * @property {number} total - 总条数
 */
// 详情接口
export function orderDetail(data) {
  return request({
    url: baseUrl + '/warning/order/detail',
    method: 'post',
    data: data,
  });
}

// 获取名称列表
export function listQuery(data) {
  return request({
    url: baseUrl + '/exRule/listQuery',
    method: 'post',
    data: data,
  });
}

// 异常规则详情
export function abnormalRuleDetail(data) {
  return request({
    url: baseUrl + '/exRule/detail',
    method: 'post',
    data: data,
  });
}

// 推送模版列表
export function templateList(params) {
  return request({
    url: baseUrl + '/warning/message/template/list',
    method: 'post',
    data: params,
  });
}

// 订单预警记录 - 分页列表
export function orderWarningRecordPage(params) {
  return request({
    url: baseUrl + '/warning/record/order/page',
    method: 'post',
    data: params,
  });
}

// 订单预警记录详情
export function recordDetail(params) {
  return request({
    url: baseUrl + '/warning/record/detail',
    method: 'post',
    data: params,
  });
}
