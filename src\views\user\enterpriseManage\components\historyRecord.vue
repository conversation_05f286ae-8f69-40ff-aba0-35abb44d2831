<template>
    <el-dialog title="场站定价历史记录" :visible.sync="dialogVisible" width="1600px">
        <el-form :model="form" :rules="rules" ref="abnormalForm"  label-position="top">
            <BuseCrud
                ref="crud"
                :loading="loading"
                :tablePage="tablePage"
                :tableColumn="tableColumn"
                :tableData="tableData"
                :modalConfig="modalConfig"
                @loadData="loadData"
            >
            </BuseCrud>
        </el-form> 

        <!-- <div class="bottom-button-wrap">
            <div>
                <el-button @click="handleCancel">取消</el-button>

            </div>

        </div> -->
    </el-dialog>
   
</template>
<script>  


import {enterpriseDiscountStationHistory} from '@/api/user/enterprise'

  export default {
    props: {
        editInfo: {
            type: Object,
            default: () => {},
        },
    },
    dicts: [
        'ls_order_except_level',
        'ls_order_except_type',
    ],
    components: {
        
    },
    data() {
        return {
            dialogVisible: false,
            stationId: '',
            enterpriseId: '',

            loading: false,
            tablePage: { total: 0, currentPage: 1, pageSize: 10 },
            tableColumn: [
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60,
                    fixed: 'left',
                },
                {
                    field: 'status',
                    title: '起效类型',
                    minWidth: 150,
                },
                {
                    title: '企业定价',
                    field: 'discountDesc',
                    minWidth: 150,
                },
                {
                    title: '开始时间',
                    field: 'startTime',
                    minWidth: 150,
                },
                {
                    title: '结束时间',
                    field: 'endTime',
                    minWidth: 150,
                    formatter: ({ cellValue }) => {
                        return cellValue ? cellValue  : '长期有效';
                    },
                },


                
            ],
            tableData: []

        };
    },
    watch: {
        
    },
    computed: {
        modalConfig() {
            return {
                addBtn: false,
                viewBtn: false,
                menu: false,
                editBtn: false,
                delBtn: false,
            }
        },
    },
    mounted() {
       
    },
    methods: {
        handleSave() {
           
        },
        handleCancel() {
            this.dialogVisible = false;
            this.resetData();
        },
        resetData() {
            this.tablePage = {
                currentPage: 1,
                pageSize: 10,
                total: 0,
            }
            this.tableData = [];
            this.stationId = '',
            this.enterpriseId = ''
        },

        async loadData() {
            const params = {
                enterpriseId: this.enterpriseId,
                stationId: this.stationId,
                pageNum: this.tablePage.currentPage,
                pageSize: this.tablePage.pageSize,
            }
            

            this.loading = true
            const [err, res] = await enterpriseDiscountStationHistory({
                ...params,
            });
            this.loading = false;

            if (err) {
                return this.$message.error(err.message);
            }

            const { data, total } = res;

            this.tableData = data;
            this.tablePage.total = total;
        },

    },
  };
  </script>

<style lang="scss" scoped>
.bottom-button-wrap {
    height: 86px;
    // margin-top: 46px;
    width: 100%;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    // padding-right: 32px;
    box-sizing: border-box;
}

.remark-brief {
    font-weight: 400;
    font-size: 14px;
    line-height: 16px;
    margin-top: 8px;
    color: #818496;

}

  </style>
  