/**
 * @file 巡视内容管理API
 */

import request from '@/utils/request'

const baseUrl = '/vehicle-charging-admin'

/**
 * @typedef {Object} ContentPageRequest
 * @property {number} [pageNum=1] - 当前页码，默认为1
 * @property {number} [pageSize=10] - 每页显示条数，默认为10
 * @property {string} [patrolContentName] - 检修内容名称
 * @property {number} [status] - 启用状态（0启用 1停用）
 */

/**
 * @typedef {Object} ContentInfo
 * @property {string} patrolContentId - 巡查内容id
 * @property {string} patrolContentNo - 巡查内容编号
 * @property {string} patrolContentName - 巡查内容名称
 * @property {string} patrolRequire - 巡视要求
 * @property {number} watermark - 是否需要上传水印照片（0需要 1不需要）
 * @property {number} sort - 排序
 * @property {number} status - 状态（0正常 1停用）
 * @property {string} createTime - 创建时间
 */

/**
 * @typedef {Object} ApiResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {Array<ContentInfo>|string} data - 返回数据
 * @property {number} total - 总条数
 */

/**
 * 巡视内容分页查询
 * @param {ContentPageRequest} params - 查询参数
 * @returns {Promise<ApiResponse>} 返回包含巡视内容列表的Promise
 */
export function getContentPage(params) {
  return request({
    url: baseUrl + '/patrol/content/page',
    method: 'post',
    data: params
  })
}

/**
 * 巡视内容新增
 * @param {ContentInfo} data - 巡视内容数据
 * @returns {Promise<ApiResponse>} 返回创建结果的Promise
 */
export function createContent(data) {
  return request({
    url: baseUrl + '/patrol/content/add',
    method: 'post',
    data: data
  })
}

/**
 * 巡视内容编辑
 * @param {ContentInfo} data - 巡视内容数据
 * @returns {Promise<ApiResponse>} 返回更新结果的Promise
 */
export function updateContent(data) {
  return request({
    url: baseUrl + '/patrol/content/update',
    method: 'post',
    data: data
  })
}

/**
 * 巡视内容删除
 * @param {Object} params - 删除参数
 * @param {string} params.patrolContentId - 巡检内容id
 * @returns {Promise<ApiResponse>} 返回删除结果的Promise
 */
export function deleteContent(params) {
  return request({
    url: baseUrl + '/patrol/content/delete',
    method: 'post',
    data: params
  })
}

/**
 * 巡视内容启用/停用
 * @param {Object} params - 操作参数
 * @param {string} params.patrolContentId - 巡检内容id
 * @param {number} params.enable - 状态 0启用 1停用
 * @returns {Promise<ApiResponse>} 返回操作结果的Promise
 */
export function toggleContentStatus(params) {
  return request({
    url: baseUrl + '/patrol/content/enable',
    method: 'post',
    data: params
  })
}