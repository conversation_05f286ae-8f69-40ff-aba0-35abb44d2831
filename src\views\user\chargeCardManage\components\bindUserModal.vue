<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="630px"
    @close="handleCancel"
  >
    <el-form :model="form" :rules="rules" ref="ruleForm" label-position="top">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item
            label="绑定用户："
            prop="cardBindUserId"
            :label-width="formLabelWidth"
          >
            <el-select
              v-model="form.cardBindUserId"
              :loading="bindUserLoading"
              filterable
              remote
              :remote-method="debouncedBindUserSearch"
              style="width: 100%"
            >
              <el-option
                v-for="item in bindUserNameList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="handleConfirm">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { cardBindUser, queryEnterpriseUser } from '@/api/user/chargeCardManage';

export default {
  props: {
    dialogTitle: {
      type: String,
      default: '绑定用户',
    },
  },
  components: {},
  dicts: [],
  data() {
    return {
      dialogVisible: false,
      form: {
        cardBindUserId: '',
      },
      rules: {
        cardBindUserId: [
          { required: true, message: '请选择退款方式', trigger: 'change' },
        ],
      },
      formLabelWidth: '120px',
      info: {
        cardId: '',
      },
      bindUserNameList: [],
      bindUserLoading: false,
      submitLoading: false,
    };
  },
  computed: {},
  mounted() {},
  methods: {
    resetForm() {
      Object.keys(this.form).forEach((key) => {
        this.form[key] = '';
      });
    },
    async debouncedBindUserSearch(query) {
      if (query !== '') {
        this.bindUserLoading = true;
        setTimeout(async () => {
          const [err, res] = await queryEnterpriseUser({
            enterpriseName: query,
          });
          this.bindUserLoading = false;
          if (err) return;

          this.bindUserNameList = res.data.map((item) => ({
            label: item.fullName,
            value: item.userId,
          }));
        }, 200);
      } else {
        this.bindUserNameList = [];
      }
    },
    handleCancel() {
      this.$refs.ruleForm.resetFields();
      this.resetForm();
      console.log(this.form, 'this.form');
      this.dialogVisible = false;
    },

    // 新增按钮防抖
    handleConfirm() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          console.log(this.form, 'this.form');
          this.submitLoading = true;
          const params = {
            ...this.form,
            cardId: this.info.cardId || '',
          };

          console.log('params', params);
          const [err, res] = await cardBindUser(params);
          this.submitLoading = false;

          if (err) return;

          this.$message.success('绑定成功');
          this.dialogVisible = false;
          this.$emit('loadData');
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__content {
  display: flex !important;
}

::v-deep .el-input-number {
  width: 100% !important;
}

.info-bg {
  display: flex;
  margin-bottom: 12px;
  justify-content: space-between;
  .info-price {
    background: #ebf3ff;
    width: 271px;
    height: 104px;
    padding: 24px;
    border-radius: 2px;
    text-align: center;
    font-family: 'PingFang SC';
    font-weight: 400;
    font-size: 16px;
    line-height: 16px;
    color: #292b33;
    .price-value {
      font-family: 'Oswald Regular';
      font-weight: 400;
      font-size: 20px;
      line-height: 24px;
      color: #217aff;
      margin-top: 16px;
    }
  }
}

.price-wrap {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  background-color: #ebf3ff;
  border-radius: 2px;
  margin-bottom: 12px;
  font-weight: 400;
  font-size: 16px;
  padding-left: 16px;
  box-sizing: border-box;
  .price {
    font-family: Oswald Regular;
    color: #217aff;
  }
}
</style>
