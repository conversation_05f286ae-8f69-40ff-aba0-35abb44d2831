<template>
  <el-dialog
    title="选择充电站"
    :visible.sync="visible"
    width="80%"
    :before-close="handleClose"
    append-to-body
  >
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :pagerProps="pagerProps"
      :modalConfig="modalConfig"
      :tableOn="{
        'checkbox-change': handleCheckboxChange,
        'checkbox-all': handleCheckboxChange,
      }"
      :tableProps="tableProps"
      @loadData="loadData"
    >
      <template slot="region">
        <el-cascader
          v-model="params.region"
          :options="regionTree"
          :props="{
            checkStrictly: true,
            value: 'areaCode',
            label: 'areaName',
            children: 'children',
          }"
          clearable
          placeholder="请选择省/市/区"
          :style="{ width: '100%' }"
        />
      </template>
    </BuseCrud>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="btnLoading" @click="handleConfirm">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  getStationList,
  batchCreate,
  getAreaList,
} from '@/api/interconnection/index';
export default {
  name: 'SelectStationDialog',
  dicts: ['ls_charging_operation_status', 'ls_station_type'],
  props: {
    operatorCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      tableColumn: [
        { type: 'checkbox', width: 30 },
        { type: 'seq', title: '序号', width: 60 },
        { field: 'stationNo', title: '站点编号', minWidth: 120 },
        { field: 'stationName', title: '站点名称', minWidth: 120 },
        {
          field: 'stationType',
          title: '站点类型',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_station_type,
              cellValue
            );
          },
        },
        { field: 'stationAddress', title: '站点地址', minWidth: 200 },
        { field: 'assetName', title: '资产单位', minWidth: 120 },
        { field: 'operName', title: '运营单位', minWidth: 120 },
        { field: 'maintainName', title: '运维单位', minWidth: 120 },
        {
          field: 'operationStatus',
          title: '运营状态',
          minWidth: 100,
          fixed: 'right',
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_operation_status,
              cellValue
            );
          },
        },
      ],
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      pagerProps: {
        pageSizes: [10, 20, 50, 100],
        layout: 'total, sizes, prev, pager, next, jumper',
      },
      params: {
        region: '',
        stationName: '',
        stationNo: '',
        stationType: '',
        operationStatus: '',
      },
      tableProps: {
        checkboxConfig: {
          checkField: 'chooseFlag',
          trigger: 'row',
          // highlight: true,
          reserve: true,
          checkMethod: ({ row }) => row.chooseFlag !== 1,
        },
      },
      stationNoList: [],
      btnLoading: false,
      regionTree: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            title: '省市区',
            field: 'region',
            element: 'slot',
            slotName: 'region',
          },
          {
            title: '站点名称',
            field: 'stationName',
            element: 'el-input',
            placeholder: '请输入',
          },
          {
            title: '站点编号',
            field: 'stationNo',
            element: 'el-input',
            placeholder: '请输入',
          },
          {
            title: '站点类型',
            field: 'stationType',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_station_type,
            },
          },
          {
            title: '运营状态',
            field: 'operationStatus',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_operation_status,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  methods: {
    async loadData() {
      this.loading = true;
      const [province, city, county] = this.params.region || [];
      const [err, res] = await getStationList({
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        operatorCode: this.operatorCode,
        ...this.params,
        province,
        city,
        county,
      });
      this.loading = false;
      if (err) return;
      this.tableData = res?.data || [];
      this.tablePage.total = res?.total || 0;
    }, // 获取区域列表
    async loadAreaList() {
      const [err, res] = await getAreaList({});
      if (err) return;
      if (res?.data?.length && Array.isArray(res.data)) {
        const areaList = res.data;
        // 区县级
        const districtList = areaList
          .filter((item) => item.areaLevel === '04')
          .map((x) => ({
            areaCode: x.areaCode,
            areaName: x.areaName,
            parentCode: x.upAreaCode,
          }));

        // 市级
        const cityList = areaList
          .filter((item) => item.areaLevel === '03')
          .map((x) => ({
            areaCode: x.areaCode,
            areaName: x.areaName,
            parentCode: x.upAreaCode,
            children: districtList.filter((y) => y.parentCode === x.areaCode),
          }));

        // 省级
        this.regionTree = areaList
          .filter((item) => item.areaLevel === '02')
          .map((x) => ({
            areaCode: x.areaCode,
            areaName: x.areaName,
            parentCode: x.upAreaCode,
            children: cityList.filter((y) => y.parentCode === x.areaCode),
          }));
      }
    },
    show() {
      this.visible = true;
      this.loadData();
      this.loadAreaList();
    },
    handleClose() {
      this.visible = false;
    },
    handleCheckboxChange({ records }) {
      this.stationNoList = records.map((item) => item.stationNo);
    },
    async handleConfirm() {
      if (this.stationNoList.length === 0) {
        this.$message.warning('请选择充电站');
        return;
      }
      this.btnLoading = true;
      const [err, res] = await batchCreate({
        operatorCode: this.operatorCode,
        stationNoList: this.stationNoList,
      });
      this.btnLoading = false;
      if (err) return;
      if (res.code === '10000') {
        this.$message.success('添加成功');
        this.handleClose();
        this.$emit('getList');
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .bd3001-table-select-box {
  display: none;
}
</style>
