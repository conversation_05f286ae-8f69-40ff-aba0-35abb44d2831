import request from '@/utils/request'

const baseUrl = '/vehicle-grid-admin'

// 出清列表
export function getRequirementList(data) {
  return request({
    url: baseUrl + '/adjustment/plan/clear/list',
    method: 'post',
    data: data
  })
}

// 运营商树形结构
export function getOperatorTreeSelect(data) {
  return request({
    url: baseUrl + '/adjustment/plan/clear/tree',
    method: 'post',
    data: data
  })
}

// 方案出清详情
export function getRequirementDetail(data) {
  return request({
    url: baseUrl + '/adjustment/plan/clear/detail',
    method: 'post',
    data: data
  })
}

// 获取需求计划名称
export function getRequireNameList(data) {
  return request({
    url: baseUrl + '/adjustment/requirement/list/simple',
    method: 'post',
    data: data
  })
}