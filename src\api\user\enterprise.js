import request from '@/utils/request';

const baseUrl = '/vehicle-charging-admin';

// 企业客户分页查询
export function getEnterprisePage(data) {
  return request({
    url: baseUrl + '/enterprise/enterprisePage',
    method: 'post',
    data: data,
  });
}

// 批量启用、禁用
export function enterpriseStatus(data) {
  return request({
    url: baseUrl + '/enterprise/enterpriseStatus',
    method: 'post',
    data: data,
  });
}

// 企业客户删除
export function enterpriseRemove(data) {
  return request({
    url: baseUrl + '/enterprise/enterpriseRemove',
    method: 'post',
    data: data,
  });
}

// 企业客户新增
export function enterpriseCreate(data) {
  return request({
    url: baseUrl + '/enterprise/enterpriseCreate',
    method: 'post',
    data: data,
  });
}

// 企业客户编辑
export function enterpriseEdit(data) {
  return request({
    url: baseUrl + '/enterprise/enterpriseEdit',
    method: 'post',
    data: data,
  });
}

// 企业客户明细
export function enterpriseDetail(data) {
  return request({
    url: baseUrl + '/enterprise/enterpriseDetail',
    method: 'post',
    data: data,
  });
}

// 企业批量审核
export function batchApprove(data) {
  return request({
    url: baseUrl + '/enterprise/batchApprove',
    method: 'post',
    data: data,
  });
}


// 企业优惠配置-分页查询
export function enterpriseDiscount(data) {
  return request({
    url: baseUrl + '/enterpriseConfig/queryPage',
    method: 'post',
    data: data,
  })  
} 

// 企业优惠配置-站点启用、禁用
export function enterpriseDiscountStatus(data) {
  return request({
    url: baseUrl + '/enterpriseConfig/stationStatus',
    method: 'post',
    data: data,
  })
}

// 企业优惠配置-场站配置
export function enterpriseDiscountStation(data) {
  return request({
    url: baseUrl + '/enterpriseConfig/stationConfig',
    method: 'post',
    data: data,
  })
}

// 站点定价修改
export function enterpriseDiscountStationUpdate(data) {
  return request({
    url: baseUrl + '/enterpriseConfig/priceEdit',
    method: 'post',
    data: data,
  })
}


// 站点定价历史记录
export function enterpriseDiscountStationHistory(data) {
  return request({
    url: baseUrl + '/enterpriseConfig/history',
    method: 'post',
    data: data,
  })
}

// 账户信息查询
export function enterpriseAccountInfo(data) {
  return request({
    url: baseUrl + '/account/queryInfo',
    method: 'post',
    data: data,
  })
}

// 交易次数统计
export function enterpriseAccountCount(data) {
  return request({
    url: baseUrl + '/account/transStatistic',
    method: 'post',
    data: data,
  })
}

// 交易记录分页查询
export function enterpriseAccountRecord(data) {
  return request({
    url: baseUrl + '/account/transRecordPage',
    method: 'post',
    data: data,
  })
}

// 企业账户阈值设置
export function enterpriseSet(data) {
  return request({
    url: baseUrl + '/account/enterprise/setting',
    method: 'post',
    data: data,
  })
}

// 企业账户提现
export function enterpriseWithdraw(data) {
  return request({
    url: baseUrl + '/account/enterprise/withdrawal',
    method: 'post',
    data: data,
  })
}

// 企业账户充值
export function enterpriseRecharge(data) {
  return request({
    url: baseUrl + '/account/enterprise/recharge',
    method: 'post',
    data: data,
  })
}