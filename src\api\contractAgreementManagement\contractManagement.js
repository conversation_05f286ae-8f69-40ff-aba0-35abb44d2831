import request from '@/utils/request';

const baseUrl = '/vehicle-grid-system';

// 获取合同列表
export function getContractList(data) {
  return request({
    url: baseUrl + '/contract/list',
    method: 'post',
    data: data,
  });
}

// 新增合同
export function addContract(data) {
  return request({
    url: baseUrl + '/contract/add',
    method: 'post',
    data: data,
  });
}

// 合同详情
export function getContractDetail(data) {
  return request({
    url: baseUrl + '/contract/detail',
    method: 'post',
    data: data,
  });
}

// 编辑合同
export function editContract(data) {
  return request({
    url: baseUrl + '/contract/update',
    method: 'post',
    data: data,
  });
}

// 删除合同
export function deleteContract(data) {
  return request({
    url: baseUrl + '/contract/delete',
    method: 'post',
    data: data,
  });
}

// 合同导出
