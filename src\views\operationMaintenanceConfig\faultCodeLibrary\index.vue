<template>
  <div class="container container-float">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        class="buse-wrap-station"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">故障代码</div>
            <div class="top-button-wrap">
              <el-button type="primary" @click="handleAdd">新增</el-button>
            </div>
          </div>
        </template>

        <!-- 故障代码类型列 -->
        <template slot="faultType" slot-scope="{ row }">
          <el-tag :type="getFaultTypeColor(row.faultType)" size="small">
            {{ row.faultType }}
          </el-tag>
        </template>

        <!-- 故障等级列 -->
        <template slot="faultLevel" slot-scope="{ row }">
          <el-tag :type="getFaultLevelColor(row.faultLevel)" size="small">
            {{ row.faultLevel }}
          </el-tag>
        </template>

        <!-- 状态列 -->
        <template slot="status" slot-scope="{ row }">
          <el-tag
            :type="row.status === '启用' ? 'success' : 'info'"
            size="small"
          >
            {{ row.status }}
          </el-tag>
        </template>

        <!-- 操作列 -->
        <template slot="operate" slot-scope="{ row }">
          <el-button type="text" size="small" @click="handleView(row)">
            查看
          </el-button>
          <el-button type="text" size="small" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button
            type="text"
            size="small"
            style="color: #f56c6c"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </BuseCrud>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="故障代码" prop="faultCode">
          <el-input v-model="form.faultCode" placeholder="请输入故障代码" />
        </el-form-item>
        <el-form-item label="故障类型" prop="faultType">
          <el-select v-model="form.faultType" placeholder="请选择故障类型">
            <el-option label="告警" value="告警" />
            <el-option label="故障" value="故障" />
          </el-select>
        </el-form-item>
        <el-form-item label="故障描述" prop="faultDescription">
          <el-input
            v-model="form.faultDescription"
            placeholder="请输入故障描述"
          />
        </el-form-item>
        <el-form-item label="故障原因" prop="faultReason">
          <el-input
            v-model="form.faultReason"
            type="textarea"
            :rows="3"
            placeholder="请输入故障原因"
          />
        </el-form-item>
        <el-form-item label="故障等级" prop="faultLevel">
          <el-select v-model="form.faultLevel" placeholder="请选择故障等级">
            <el-option label="严重" value="严重" />
            <el-option label="普通" value="普通" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="启用">启用</el-radio>
            <el-radio label="停用">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'FaultCodeLibrary',
  data() {
    return {
      loading: false,
      dialogVisible: false,
      dialogTitle: '新增故障代码',
      isEdit: false,
      currentRow: null,

      // 分页信息
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },

      // 表格数据
      tableData: [],

      // 表格列配置
      tableColumn: [
        { type: 'seq', title: '序号', minWidth: 60 },
        { field: 'faultCode', title: '故障代码', minWidth: 120 },
        {
          field: 'faultType',
          title: '故障类型',
          minWidth: 100,
          slot: 'faultType',
        },
        { field: 'faultDescription', title: '故障描述', minWidth: 200 },
        { field: 'faultReason', title: '故障原因', minWidth: 200 },
        {
          field: 'faultLevel',
          title: '故障等级',
          minWidth: 100,
          slot: 'faultLevel',
        },
        { field: 'status', title: '状态', minWidth: 80, slot: 'status' },
        { field: 'createTime', title: '创建时间', minWidth: 160 },
        { title: '操作', slot: 'operate', minWidth: 180, fixed: 'right' },
      ],

      // 分页器配置
      pagerProps: {
        layouts: [
          'Total',
          'PrevPage',
          'JumpNumber',
          'PageCount',
          'NextPage',
          'Sizes',
        ],
      },

      // 模态框配置
      modalConfig: {
        addBtn: false,
        menu: false,
      },

      // 筛选条件配置
      filterOptions: [
        {
          field: 'faultCode',
          title: '故障代码',
          type: 'input',
          placeholder: '请输入',
        },
        {
          field: 'faultType',
          title: '故障类型',
          type: 'select',
          placeholder: '请选择',
          options: [
            { label: '告警', value: '告警' },
            { label: '故障', value: '故障' },
          ],
        },
        {
          field: 'faultLevel',
          title: '故障等级',
          type: 'select',
          placeholder: '请选择',
          options: [
            { label: '严重', value: '严重' },
            { label: '普通', value: '普通' },
          ],
        },
        {
          field: 'status',
          title: '状态',
          type: 'select',
          placeholder: '请选择',
          options: [
            { label: '启用', value: '启用' },
            { label: '停用', value: '停用' },
          ],
        },
      ],

      // 表单数据
      form: {
        faultCode: '',
        faultType: '',
        faultDescription: '',
        faultReason: '',
        faultLevel: '',
        status: '启用',
      },

      // 表单验证规则
      rules: {
        faultCode: [
          { required: true, message: '请输入故障代码', trigger: 'blur' },
        ],
        faultType: [
          { required: true, message: '请选择故障类型', trigger: 'change' },
        ],
        faultDescription: [
          { required: true, message: '请输入故障描述', trigger: 'blur' },
        ],
        faultReason: [
          { required: true, message: '请输入故障原因', trigger: 'blur' },
        ],
        faultLevel: [
          { required: true, message: '请选择故障等级', trigger: 'change' },
        ],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }],
      },
    };
  },

  mounted() {
    this.loadData();
  },

  methods: {
    // 加载数据
    loadData(params = {}) {
      this.loading = true;

      // TODO: 实际项目中这里应该调用API接口
      // const { page, filters } = params;

      // 模拟数据
      setTimeout(() => {
        this.tableData = [
          {
            id: 1,
            faultCode: '1313413131',
            faultType: '告警',
            faultDescription: '充电枪未归位告警',
            faultReason: '内容信息',
            faultLevel: '严重',
            status: '启用',
            createTime: '2024-12-01 12:12:12',
          },
          {
            id: 2,
            faultCode: '1313413131',
            faultType: '告警',
            faultDescription: '充电枪未归位告警',
            faultReason: '内容信息',
            faultLevel: '严重',
            status: '停用',
            createTime: '2024-12-01 12:12:12',
          },
          {
            id: 3,
            faultCode: '1313413131',
            faultType: '故障',
            faultDescription: '充电枪未归位告警',
            faultReason: '内容信息',
            faultLevel: '严重',
            status: '停用',
            createTime: '2024-12-01 12:12:12',
          },
          {
            id: 4,
            faultCode: '1313413131',
            faultType: '故障',
            faultDescription: '充电枪未归位告警',
            faultReason: '内容信息',
            faultLevel: '普通',
            status: '停用',
            createTime: '2024-12-01 12:12:12',
          },
          {
            id: 5,
            faultCode: '1313413131',
            faultType: '故障',
            faultDescription: 'BMS通信故障',
            faultReason: '内容信息',
            faultLevel: '普通',
            status: '停用',
            createTime: '2024-12-01 12:12:12',
          },
          {
            id: 6,
            faultCode: '1313413131',
            faultType: '故障',
            faultDescription: 'BMS通信故障',
            faultReason: '内容信息',
            faultLevel: '普通',
            status: '停用',
            createTime: '2024-12-01 12:12:12',
          },
          {
            id: 7,
            faultCode: '1313413131',
            faultType: '故障',
            faultDescription: 'BMS通信故障',
            faultReason: '内容信息',
            faultLevel: '普通',
            status: '停用',
            createTime: '2024-12-01 12:12:12',
          },
          {
            id: 8,
            faultCode: '1313413131',
            faultType: '故障',
            faultDescription: 'BMS通信故障',
            faultReason: '内容信息',
            faultLevel: '普通',
            status: '停用',
            createTime: '2024-12-01 12:12:12',
          },
          {
            id: 9,
            faultCode: '1313413131',
            faultType: '故障',
            faultDescription: 'BMS通信故障',
            faultReason: '内容信息',
            faultLevel: '普通',
            status: '停用',
            createTime: '2024-12-01 12:12:12',
          },
          {
            id: 10,
            faultCode: '1313413131',
            faultType: '故障',
            faultDescription: 'BMS通信故障',
            faultReason: '内容信息',
            faultLevel: '普通',
            status: '停用',
            createTime: '2024-12-01 12:12:12',
          },
        ];

        this.tablePage.total = 100;
        this.loading = false;
      }, 1000);
    },

    // 获取故障类型颜色
    getFaultTypeColor(type) {
      const colorMap = {
        告警: 'warning',
        故障: 'danger',
      };
      return colorMap[type] || '';
    },

    // 获取故障等级颜色
    getFaultLevelColor(level) {
      const colorMap = {
        严重: 'danger',
        普通: 'primary',
      };
      return colorMap[level] || '';
    },

    // 新增
    handleAdd() {
      this.dialogTitle = '新增故障代码';
      this.isEdit = false;
      this.currentRow = null;
      this.resetForm();
      this.dialogVisible = true;
    },

    // 查看
    handleView(row) {
      this.$message.info(`查看故障代码: ${row.faultCode}`);
    },

    // 编辑
    handleEdit(row) {
      this.dialogTitle = '编辑故障代码';
      this.isEdit = true;
      this.currentRow = row;
      this.form = { ...row };
      this.dialogVisible = true;
    },

    // 删除
    handleDelete(row) {
      this.$confirm(`确定要删除故障代码"${row.faultCode}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // TODO: 实际项目中这里应该调用删除API
          // deleteAPI(row.id).then(() => {
          this.$message.success('删除成功');
          this.loadData();
          // });
        })
        .catch(() => {
          this.$message.info('已取消删除');
        });
    },

    // 提交表单
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.isEdit) {
            this.$message.success('编辑成功');
          } else {
            this.$message.success('新增成功');
          }
          this.dialogVisible = false;
          this.loadData();
        }
      });
    },

    // 关闭对话框
    handleDialogClose() {
      this.resetForm();
    },

    // 重置表单
    resetForm() {
      this.form = {
        faultCode: '',
        faultType: '',
        faultDescription: '',
        faultReason: '',
        faultLevel: '',
        status: '启用',
      };
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 20px;

  .table-wrap {
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;

    .card-head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .card-head-text {
        font-size: 18px;
        font-weight: 500;
        color: #333;
      }

      .top-button-wrap {
        display: flex;
        gap: 10px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

::v-deep .el-tag {
  border: none;
}
</style>
