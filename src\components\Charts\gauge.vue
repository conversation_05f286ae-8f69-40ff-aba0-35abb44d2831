<template>
  <div class="my-gauge">
    <div ref="dom" style="width: 280px; height: 200px"></div>
    <div class="title">{{ title }}</div>
  </div>
</template>

<script>
import * as echarts from 'echarts/core';
import { Gauge<PERSON>hart } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';

echarts.use([GaugeChart, CanvasRenderer]);
export default {
  name: 'gauge',
  props: {
    value: {
      type: [String, Number],
      default: 0,
    },
    text: String,
    subtext: String,
    title: String,
    unit: String,
    maxValue: {
      type: Number,
      default: 1000,
    },
  },
  data() {
    return {
      dom: null,
    };
  },
  watch: {
    maxValue() {
      this.updateData();
    },
    value() {
      this.updateData();
    },
  },
  mounted() {
    let option = this.getOption();
    this.dom = echarts.init(this.$refs.dom);
    this.dom.setOption(option);
  },
  methods: {
    getOption() {
      return {
        series: [
          {
            type: 'gauge',
            startAngle: 180,
            endAngle: 0,
            min: 0,
            max: this.maxValue || 1000,
            splitNumber: 5,
            radius: '95%',
            itemStyle: {
              color: '#58D9F9',
              shadowColor: 'rgba(0,138,255,0.45)',
              shadowBlur: 10,
              shadowOffsetX: 2,
              shadowOffsetY: 2,
            },
            progress: {
              show: true,
              roundCap: true,
              width: 10,
            },
            pointer: {
              icon: 'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z',
              length: '75%',
              width: 8,
              offsetCenter: [0, '5%'],
            },
            axisLine: {
              roundCap: true,
              lineStyle: {
                width: 10,
              },
            },
            axisTick: {
              splitNumber: 2,
              lineStyle: {
                width: 1,
                color: '#999',
              },
            },
            splitLine: {
              length: 10,
              lineStyle: {
                width: 2,
                color: '#999',
              },
            },
            axisLabel: {
              distance: 12,
              color: '#999',
              fontSize: 12,
            },
            title: {
              show: false,
            },
            detail: {
              backgroundColor: '#fff',
              width: '60%',
              lineHeight: 20,
              height: 20,
              offsetCenter: [0, '35%'],
              valueAnimation: true,
              formatter: (value) => {
                return '{value|' + value + '}{unit|' + this.unit + '}';
              },
              rich: {
                value: {
                  fontSize: 16,
                  fontWeight: 'bolder',
                  color: '#777',
                },
                unit: {
                  fontSize: 16,
                  color: '#999',
                  padding: [0, 0, 0, 10],
                },
              },
            },
            data: [
              {
                value: this.value ? Number(this.value.toFixed(2)) : 0,
                unit: this.unit,
              },
            ],
          },
        ],
      };
    },
    updateData() {
      const options = this.getOption();
      if (this.dom) {
        this.dom.setOption(options, true);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.my-gauge {
  position: relative;
  height: 200px;

  .title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 10%;
    font-size: 14px;
    font-weight: bold;
  }
}
</style>
