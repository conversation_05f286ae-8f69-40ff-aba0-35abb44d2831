import request from '@/utils/request'

const baseUrl = '/vehicle-charging-admin'

// 优惠券分页查询
export function getCouponList(params) {
    return request({
        url: baseUrl + '/coupon/queryPage',
        method: 'post',
        data: params
    })
}

// 获取资产单位
export function getAssetUnit(params) {
    return request({
        url: baseUrl + '/common/getAssetUnit',
        method: 'post',
        data: params,
        isDebounce: true
    })
}   

// 获取个人用户列表
export function getPersonalUserList(params) {
    return request({
        url: baseUrl + '/user/list ',
        method: 'post',
        data: params,
    })
}

// 获取企业用户列表
export function getEnterpriseUserList(params) {
    return request({
        url: baseUrl + '/enterpriseUser/queryPage',
        method: 'post',
        data: params,
    })
}

// 新增优惠券
export function addCoupon(params) {
    return request({
        url: baseUrl + '/coupon/create',
        method: 'post',
        data: params
    })
}

// 启用/停用优惠券
export function changeCouponStatus(params) {
    return request({
        url: baseUrl + '/coupon/modifyStatus',
        method: 'post',
        data: params
    })
}

// 删除优惠券
export function deleteCoupon(params) {
    return request({
        url: baseUrl + '/coupon/remove',
        method: 'post',
        data: params
    })
}

// 调整库存
export function adjustStock(params) {
    return request({
        url: baseUrl + '/coupon/adjustStock',
        method: 'post',
        data: params   
    })
}

// 优惠券详情
export function getCouponDetail(params) {
    return request({
        url: baseUrl + '/coupon/queryDetail',
        method: 'post',
        data: params    
    })
}

// 优惠券审核
export function auditCoupon(params) {
    return request({
        url: baseUrl + '/coupon/approve',
        method: 'post',
        data: params    
    })  
}

// 库存调整记录
export function getStockRecord(params) {
    return request({
        url: baseUrl + '/coupon/adjustHistory',
        method: 'post',
        data: params    
    })
}