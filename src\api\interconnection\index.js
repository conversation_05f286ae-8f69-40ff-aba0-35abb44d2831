import request from '@/utils/request'

const baseUrl = '/vehicle-charging-admin'

// 互联互通管理列表
export function getInterconnectionList(data) {
  return request({
    url: baseUrl + '/interconnect/page',
    method: 'post',
    data: data
  })
}

// 新增
export function createInterconnection(data) {
  return request({
    url: baseUrl + '/interconnect/create',
    method: 'post',
    data: data
  })
}

// 更新
export function updateInterconnection(data) {
  return request({
    url: baseUrl + '/interconnect/update',
    method: 'post',
    data: data
  })
}

// 详情
export function getInterconnectionDetail(data) {
  return request({
    url: baseUrl + '/interconnect/detail/' + data,
    method: 'post',
  })
}

// 启用
export function enableInterconnection(data) {
  return request({
    url: baseUrl + '/interconnect/enable/' + data,
    method: 'post',
  })
}

// 禁用
export function disableInterconnection(data) {
  return request({
    url: baseUrl + '/interconnect/disable/' + data,
    method: 'post',
  })
}

// 数据权限 批量新增
export function batchCreate(data) {
  return request({
    url: baseUrl + '/partStation/batchCreate',
    method: 'post',
    data: data
  })
}

// 数据权限 分页
export function getPermissionList(data) {
  return request({
    url: baseUrl + '/partStation/page',
    method: 'post',
    data: data
  })
}

// 数据权限 删除
export function removePermission(data) {
  return request({
    url: baseUrl + '/partStation/remove',
    method: 'post',
    data: data
  })
}

// 数据权限 批量删除
export function batchRemovePermission(data) {
  return request({
    url: baseUrl + '/partStation/batchRemove',
    method: 'post',
    data: data
  })
}

// 站点分页 - 包含是否选中标记
export function getStationList(data) {
  return request({
    url: baseUrl + '/partStation/station/page',
    method: 'post',
    data: data
  })
}

// 省市区
export function getAreaList(data) {
  return request({
    url: baseUrl + '/area/list',
    method: 'post',
    data: data
  })
}