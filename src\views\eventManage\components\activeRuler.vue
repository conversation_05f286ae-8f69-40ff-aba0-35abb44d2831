<template>
  <el-dialog title="新增奖品" :visible.sync="dialogVisible" width="1200px">
    <div class="info-card">
      <div class="form-wrap" v-if="dialogVisible">
        <el-form
          :model="baseInfo.form"
          :rules="baseInfo.rules"
          ref="baseInfoForm"
          label-position="top"
          :disabled="true"
        >
          <div class="info-card">
            <div class="card-head">
              <div class="before-icon"></div>
              <div class="card-head-text">活动规则</div>
            </div>
            <div class="card-head-split"></div>
            <el-tabs v-model="rulerTab">
              <el-tab-pane label="用户管理" name="bussinessRuler">
                <div class="form-wrap form-wrap-top">
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form-item
                        label="使用范围"
                        prop="scopeOfUse"
                        :label-width="formLabelWidth"
                      >
                        <el-radio-group v-model="baseInfo.form.scopeOfUse">
                          <el-radio
                            v-for="(item, index) in scopeOfUseList"
                            :label="item.value"
                            :key="index"
                          >
                            {{ item.label }}
                          </el-radio>
                        </el-radio-group>
                      </el-form-item>
                      <el-form-item
                        label=""
                        prop="limitations"
                        :label-width="formLabelWidth"
                      >
                        <el-radio-group v-model="baseInfo.form.limitations">
                          <el-radio
                            v-for="(item, index) in limitationsList"
                            :label="item.value"
                            :key="index"
                          >
                            {{ item.label }}
                          </el-radio>
                        </el-radio-group>
                      </el-form-item>
                      <div
                        class="table-wrap"
                        v-if="baseInfo.form.limitations !== '1'"
                      >
                        <BuseCrud
                          ref="crud"
                          :tableColumn="stationTableColumnConfig"
                          :tableData="stationTableData"
                          :modalConfig="modalConfig"
                        >
                          <template slot="defaultHeader">
                            <div>
                              <div class="card-head">
                                <div class="top-button-wrap">
                                  <div class="choose-box">
                                    已选择
                                    <span>10</span>
                                    个充电站,
                                    <span>100</span>
                                    个充电桩
                                  </div>
                                </div>
                              </div>
                            </div>
                          </template>
                        </BuseCrud>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </el-tab-pane>
              <el-tab-pane label="配置管理" name="userRuler">
                <div class="form-wrap form-wrap-top">
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form-item
                        label="用户类型"
                        prop="userType"
                        :label-width="formLabelWidth"
                      >
                        <el-radio-group v-model="baseInfo.form.userType">
                          <el-radio
                            v-for="(item, index) in userTypeList"
                            :label="item.value"
                            :key="index"
                          >
                            {{ item.label }}
                          </el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <div class="table-wrap" v-if="baseInfo.form.userType === '3'">
                    <BuseCrud
                      ref="cruda"
                      :tableColumn="userTableColumn"
                      :tableData="userTableData"
                      :modalConfig="modalConfig"
                    >
                      <template slot="defaultHeader">
                        <div>
                          <div class="card-head">
                            <div class="top-button-wrap">
                              <div class="choose-box">
                                圈选
                                <span>10</span>
                                个用户
                              </div>
                            </div>
                          </div>
                        </div>
                      </template>
                    </BuseCrud>
                  </div>
                  <el-row>
                    <el-col :span="24">
                      <el-form-item
                        label="是否新用户注册"
                        prop="newUserRegister"
                        :label-width="formLabelWidth"
                      >
                        <el-radio-group v-model="baseInfo.form.newUserRegister">
                          <el-radio
                            v-for="(item, index) in newUserRegisterList"
                            :label="item.value"
                            :key="index"
                          >
                            {{ item.label }}
                          </el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <div class="flex-end">
                        <el-form-item
                          label="常用充电区域"
                          prop="chargingArea"
                          :label-width="formLabelWidth"
                        >
                          <el-radio-group v-model="baseInfo.form.chargingArea">
                            <el-radio
                              v-for="(item, index) in chargingAreaList"
                              :label="item.value"
                              :key="index"
                            >
                              {{ item.label }}
                            </el-radio>
                          </el-radio-group>
                        </el-form-item>
                        <el-form-item
                          v-if="baseInfo.form.chargingArea === '2'"
                          label=""
                          prop="activeType"
                          :label-width="formLabelWidth"
                        >
                          <el-select
                            style="margin-left: 20px"
                            v-model="baseInfo.form.activeType"
                            placeholder="请选择"
                            multiple
                          >
                            <el-option
                              v-for="item in activeTypeList"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </div>
                    </el-col>
                    <el-col :span="24">
                      <div class="flex-end">
                        <el-form-item
                          label="充电情况"
                          prop="chargingSituation"
                          :label-width="formLabelWidth"
                        >
                          <el-radio-group
                            v-model="baseInfo.form.chargingSituation"
                          >
                            <el-radio
                              v-for="(item, index) in chargingSituationList"
                              :label="item.value"
                              :key="index"
                            >
                              {{ item.label }}
                            </el-radio>
                          </el-radio-group>
                        </el-form-item>
                        <div
                          class="flex-end"
                          style="margin-left: 10px"
                          v-if="baseInfo.form.chargingSituation !== '1'"
                        >
                          <el-form-item
                            label=""
                            prop="activeType"
                            :label-width="formLabelWidth"
                          >
                            <span style="margin-right: 4px">近</span>
                            <el-input
                              style="width: 70% !important"
                              v-model="baseInfo.form.activeType"
                              placeholder="请输入"
                            ></el-input>
                            <span style="margin-left: 4px">天达到</span>
                          </el-form-item>

                          <el-form-item
                            label=""
                            prop="activeType"
                            :label-width="formLabelWidth"
                          >
                            <el-input
                              v-model="baseInfo.form.activeType"
                              placeholder="请输入"
                            ></el-input>
                          </el-form-item>
                          <el-form-item
                            label=""
                            prop="activeType"
                            :label-width="formLabelWidth"
                          >
                            <el-select
                              style="margin-left: 20px"
                              v-model="baseInfo.form.activeType"
                              placeholder="请选择单位"
                            >
                              <el-option
                                v-for="item in activeTypeList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                              ></el-option>
                            </el-select>
                          </el-form-item>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </el-tab-pane>
              <el-tab-pane label="角色管理" name="limitRuler">
                <div class="form-wrap form-wrap-top">
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <div class="flex-end">
                        <el-form-item
                          label="活动限制"
                          prop="activityLimitation"
                          :label-width="formLabelWidth"
                        >
                          <el-radio-group
                            v-model="baseInfo.form.activityLimitation"
                          >
                            <el-radio
                              v-for="(item, index) in activityLimitationList"
                              :label="item.value"
                              :key="index"
                            >
                              {{ item.label }}
                            </el-radio>
                          </el-radio-group>
                        </el-form-item>
                      </div>
                    </el-col>
                    <el-col :span="24">
                      <div
                        class="flex-end"
                        style="margin-left: 10px"
                        v-if="baseInfo.form.activityLimitation !== '1'"
                      >
                        <el-form-item
                          label=""
                          prop="activeType"
                          :label-width="formLabelWidth"
                        >
                          <span style="margin-right: 4px">
                            活动期间每个用户
                          </span>
                          <el-select
                            v-model="baseInfo.form.activeType"
                            placeholder="请选择"
                          >
                            <el-option
                              v-for="item in activeTypeList"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                            ></el-option>
                          </el-select>
                        </el-form-item>

                        <el-form-item
                          label=""
                          prop="activeType"
                          :label-width="formLabelWidth"
                        >
                          <span style="margin: 0 4px 0 10px">限制参与</span>

                          <el-input
                            style="width: 60% !important"
                            v-model="baseInfo.form.activeType"
                            placeholder="请输入"
                          ></el-input>
                          <span style="margin-left: 4px">次</span>
                        </el-form-item>
                      </div>
                    </el-col>
                    <el-col :span="24">
                      <div
                        class="flex-end"
                        style="margin-left: 10px"
                        v-if="baseInfo.form.activityLimitation !== '1'"
                      >
                        <el-form-item
                          label=""
                          prop="activeType"
                          :label-width="formLabelWidth"
                        >
                          <span style="margin-right: 4px">活动期间限制</span>
                          <el-select
                            v-model="baseInfo.form.activeType"
                            placeholder="请选择"
                          >
                            <el-option
                              v-for="item in activeTypeList"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                            ></el-option>
                          </el-select>
                        </el-form-item>

                        <el-form-item
                          label=""
                          prop="activeType"
                          :label-width="formLabelWidth"
                        >
                          <span style="margin: 0 4px 0 10px">发放优惠券</span>

                          <el-input
                            style="width: 60% !important"
                            v-model="baseInfo.form.activeType"
                            placeholder="请输入"
                          ></el-input>
                          <span style="margin-left: 4px">张</span>
                        </el-form-item>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-form>
      </div>
    </div>

    <div class="bottom-wrap">
      <div>
        <el-button @click="handleCancel" type="primary">确定</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: {},
  components: {},
  dicts: [
    'ls_order_except_level', // 异常级别
    'ls_order_except_type', // 异常类型
    'ls_order_except_measures', // 处理措施
    'ls_order_except_status', // 异常状态
    'ls_except_rule_item', // 异常规则
  ],
  data() {
    return {
      dialogVisible: false,
      formLabelWidth: '120px',
      baseInfo: {
        form: {
          timePeriodId: '',
          periodName: '',
          suitCityCode: '',
          createUnit: '',
          activeType: '',
          scopeOfUse: '1',
          limitations: '1',
          userType: '1',
          newUserRegister: '1',
          chargingArea: '1',
          chargingSituation: '1',
          activityLimitation: '1',
        },
      },
      submitLoading: false,
      stationTableData: [],
      userTableData: [],
      reductionTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'periodName',
          title: '立减时间段',
          minWidth: 150,
        },
        {
          field: 'validMonthSummary',
          title: '优惠范围',
          minWidth: 150,
        },
        {
          field: 'createBy',
          title: '立减方式',
          minWidth: 120,
        },
      ],
      userTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'periodName',
          title: '用户ID',
          minWidth: 150,
        },
        {
          field: 'validMonthSummary',
          title: '用户昵称',
          minWidth: 150,
        },
        {
          field: 'createBy',
          title: '手机号码',
          minWidth: 120,
        },
      ],
      stationTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'periodName',
          title: '充电站编号',
          minWidth: 150,
        },
        {
          field: 'validMonthSummary',
          title: '充电站名称',
          minWidth: 150,
        },
        {
          field: 'createBy',
          title: '站点类型',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '运营模式',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '资产属性',
          minWidth: 120,
        },
      ],
      pileTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'periodName',
          title: '所属充电站',
          minWidth: 150,
        },
        {
          field: 'validMonthSummary',
          title: '运营模式',
          minWidth: 150,
        },
        {
          field: 'createBy',
          title: '充电桩编号',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '充电桩名称',
          minWidth: 120,
        },
      ],
      activeTypeList: [
        { label: '领券活动', value: '1' },
        { label: '定向发券活动', value: '2' },
        { label: '立减活动', value: '3' },
      ],
      institutionTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'periodName',
          title: '产权机构',
          minWidth: 250,
          align: 'center',
        },
      ],
      rulerTab: 'bussinessRuler',
      scopeOfUseList: [
        { label: '按场站', value: '1' },
        { label: '按充电桩', value: '2' },
        { label: '按产权机构', value: '3' },
      ],
      limitationsList: [
        { label: '不限制', value: '1' },
        { label: '指定', value: '2' },
        { label: '批量导入', value: '3' },
      ],
      userTypeList: [
        { label: '个人用户', value: '1' },
        { label: '企业用户', value: '2' },
        { label: '批量导入', value: '3' },
      ],
      newUserRegisterList: [
        { label: '不限制', value: '1' },
        { label: '是', value: '2' },
        { label: '否', value: '3' },
      ],
      chargingAreaList: [
        { label: '不限制', value: '1' },
        { label: '限制', value: '2' },
      ],
      chargingSituationList: [
        { label: '不限制', value: '1' },
        { label: '充电量', value: '2' },
        { label: '充电金额', value: '3' },
      ],
      activityLimitationList: [
        { label: '不限制', value: '1' },
        { label: '限制', value: '2' },
      ],
    };
  },
  watch: {
    dialogVisible(value) {
      console.log(value, 888);
      if (!value) {
        this.baseInfo.form = {
          warningName: '',
          webPushTemplate: '',
        };
      }
    },
  },
  computed: {
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },

    stationTableColumnConfig() {
      return this.baseInfo.form.scopeOfUse === '1'
        ? this.stationTableColumn
        : this.baseInfo.form.scopeOfUse === '2'
        ? this.pileTableColumn
        : this.institutionTableColumn;
    },
  },
  mounted() {},
  methods: {
    orderTypeChange(event) {
      console.log(event, 88);
    },
    orderChange(event) {
      console.log(event, 88);
    },
    handleCancel() {
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.info-card {
  //   margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
        background-color: #1ab2ff;
        border-color: #1ab2ff;
      }
      ::v-deep .el-button--small {
        font-size: 14px;
      }
      .distribution {
        margin-left: 24px;
        margin-right: 24px;
        display: flex;
        align-items: center;
      }
    }
  }

  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
    padding: 0 16px 16px 16px;
    .custom-header {
      background: -webkit-gradient(
          linear,
          left top,
          left bottom,
          from(rgba(0, 149, 255, 0.5)),
          to(rgba(87, 152, 255, 0))
        ),
        #f5faff;
      background: linear-gradient(
          180deg,
          rgba(0, 149, 255, 0.5) 0%,
          rgba(87, 152, 255, 0) 100%
        ),
        #f5faff;
      background-repeat: no-repeat;
    }
  }
  .form-wrap-top {
    margin-top: 16px;
  }
}

.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .bottom-wrap {
    // position: fixed;
    // bottom: 0;
    // left: 0;
    // width: 100%;
    // height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
  }
}
.coupon-ruler {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #ebf3ff;
  height: 40px;
  padding: 12px 16px;
  margin: 6px 0 12px;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #505363;
  .ruler-no span {
    color: #292b33;
  }
  .ruler-desc {
    color: #217aff;
  }
}
::v-deep .hidden-label .el-form-item__label:before {
  visibility: hidden;
}
.upload-wrap {
  padding: 24px 24px 0 24px;
  ::v-deep .el-upload-dragger {
    width: 732px;
    height: 180px;
    border: 1px dashed #5798ff;
    border-radius: 2px;
    .upload-icon {
      width: 24px;
      height: 24px;
      background-image: url('~@/assets/station/upload-icon.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin: 46px auto 0 auto;
    }
    .upload-text {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      margin: 16px auto;
      color: #292b33;
    }
    .upload-tip {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      margin: 16px auto;
      color: #818496;
    }
  }
  .upload-info {
    font-weight: 400;
    font-size: 16px;
    line-height: 16px;
    margin: 16px 0 24px 0;
    color: #292b33;
  }
}
.flex-end {
  display: flex;
  align-items: flex-end;
}
</style>
