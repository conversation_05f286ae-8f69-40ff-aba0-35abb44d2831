import Vue from 'vue';
import Vuex from 'vuex';
import app from './modules/app';
import dict from './modules/dict';
import user from './modules/user';
import tagsView from './modules/tagsView';
import permission from './modules/permission';
import settings from './modules/settings';
import getters from './getters';
import createPersistedState from 'vuex-persistedstate';
// import Cookies from 'js-cookie';

const persistedState = createPersistedState({
  key: 'dictStore',
  paths: ['dict'],
  // storage: {
  //   getItem: (key) => Cookies.get(key),
  //   setItem: (key, value) => {
  //     Cookies.set(key, value);
  //   },
  //   removeItem: (key) => Cookies.remove(key),
  // },
  // filter: (mutation) => mutation.type !== 'clearState', // 过滤不需要持久化的mutation
});

Vue.use(Vuex);

const store = new Vuex.Store({
  plugins: [persistedState],
  modules: {
    app,
    dict,
    user,
    tagsView,
    permission,
    settings,
  },
  getters,
});

export default store;
