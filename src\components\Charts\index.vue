<template>
  <div :style="{ width: width, height: height }" v-loading="loading">
    <div ref="chart" :style="{ opacity: loading ? 0 : 1, width: '100%', height: '100%' }"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { throttle } from 'lodash';

export default {
  name: 'sm-chart',
  props: {
    chartOptions: {
      type: Object,
      default: () => ({}),
    },
    id: {
      type: String,
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      myChart: null,
    };
  },
  watch: {
    chartOptions: {
      handler(newVal) {
        this.myChart.setOption(newVal, true);
      },
      deep: true,
    },
  },
  mounted() {
    this.loadEchart();
    this.$emit('getEchart', this.myChart);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeEvent);
  },
  methods: {
    loadEchart() {
      this.myChart = echarts.init(this.$refs.chart, 'walden');
      this.myChart.setOption(this.chartOptions);
      window.addEventListener('resize', throttle(this.resizeEvent, 500));
      //监测父盒子大小变化
      new ResizeObserver(() => {
        this.myChart.resize();
      }).observe(this.$refs.chart);
    },
    resizeEvent() {
      this.myChart.resize();
    },
  },
};
</script>

<style scoped lang="less">
.loading {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
