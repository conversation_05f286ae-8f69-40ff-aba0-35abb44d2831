<template>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      @close="handleCancel"
      width="1600px"
      :destroy-on-close="true"
    >
        <BuseCrud
            ref="crud"
            :loading="loading"
            :tablePage="tablePage"
            :tableColumn="tableColumn"
            :tableData="tableData"
            :pagerProps="pagerProps"
            :modalConfig="modalConfig"
            :tableOn="{
                'checkbox-change': handleCheckboxChange,
                'checkbox-all': handleCheckboxChange,
            }"
            @loadData="loadData"
        >
        </BuseCrud>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
    </el-dialog>
  </template>
  <script>  


import {
    getPersonalUserList
} from '@/api/operator/coupon'


  export default {
    props: {
        dialogTitle:{
            type: String,
            default: '个人用户选择'
        }
    },
    components: {
    },

    dicts: [
      
    ],
    data() {
        return {
            dialogVisible: false,
            operStatusList: [],

            tablePage: { total: 0, currentPage: 1, pageSize: 10 },
        
            loading: false,
            stationList: [],
            tableColumn:[
                {
                    type: 'checkbox',
                    width: 50,
                    fixed: 'left',
                },
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60, // 最小宽度
                },
                {
                    field: 'userId', // todo 更新
                    title: '用户ID',
                    minWidth: 220, // 最小宽度
                },
                {
                    field: 'nickName', // todo 更新
                    title: '用户昵称',
                    minWidth: 220, // 最小宽度
                },
                {
                    field: 'mobile', // todo 更新
                    title: '手机号码',
                    minWidth: 220, // 最小宽度
                },
            ],
            tableData: [],
            pagerProps: {
                layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
            },

            stationNameList: [],
            stationLoading: false,

        };
    },
    computed: {
      modalConfig() {
        return {
            addBtn: false,
            viewBtn: false,
            menu: false,
            editBtn: false,
            delBtn: false,
        }
      },
    },
    mounted() {
        this.loadData();
    },
    methods: {

        handleCancel() {
            this.dialogVisible = false;
        },



        // 新增按钮防抖        
        handleConfirm: _.debounce(function() {
            if(!this.stationList.length) {
                this.$message.warning('请先选择站点');
            } else {
                this.$emit('confirm', this.stationList);
                this.stationList = [];
                this.dialogVisible = false;
            }
        }, 300) ,

        async loadData() {

        this.loading = true;

        const [err, res] = await getPersonalUserList({
                pageNum: this.tablePage.currentPage,
                pageSize: this.tablePage.pageSize,
        })

        this.loading = false;
        if (err) return 
        const { data, total } = res;

        const list = [];

        this.tableData = data;
        this.tablePage.total = total;
        
      },

        handleCheckboxChange({ records }) {
            console.log('选中的记录:', records);
            this.stationList = records
        },


    },
  };
  </script>
  <style lang="scss" scoped>
::v-deep .el-form-item__content{
    display: flex !important;
}



  </style>
  