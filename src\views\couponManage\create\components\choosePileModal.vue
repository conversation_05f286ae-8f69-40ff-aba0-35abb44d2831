<template>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      @close="handleCancel"
      width="1600px"
      :destroy-on-close="true"
    >
        <BuseCrud
            ref="crud"
            :loading="loading"
            :filterOptions="filterOptions"
            :tablePage="tablePage"
            :tableColumn="tableColumn"
            :tableData="tableData"
            :pagerProps="pagerProps"
            :modalConfig="modalConfig"
            :tableOn="{
                'checkbox-change': handleCheckboxChange,
                'checkbox-all': handleCheckboxChange,
            }"
            @loadData="loadData"
        >
        </BuseCrud>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
    </el-dialog>
  </template>
  <script>  

import {
  getStationList,
  getBrandList,
  getModelList,
  getAssetUnit,
  getPileList,
  getPileSummary,
  getPileControlDetail,
  getPileLog,
} from '@/api/pile/index';


  export default {
    props: {
        dialogTitle:{
            type: String,
            default: '充电桩选择'
        }
    },
    components: {
    },

    dicts: [
      'ls_charging_operation_mode', // 运营模式
      'ls_charging_subType', // 设备类型  
      'ls_charging_adjustable_type', // 可控类型
      'ls_charging_status',// 是否
      'ls_charging_station_source', // 桩来源
      'ls_charging_operation_status', // 运营状态
      'ls_charging_station_access_type', // 充电桩接入方式
    ],
    data() {
        return {
            dialogVisible: false,
            operStatusList: [],

            tablePage: { total: 0, currentPage: 1, pageSize: 10 },
        
            loading: false,
            stationList: [],
            tableColumn:[
                {
                    type: 'checkbox',
                    width: 50,
                    fixed: 'left',
                },
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60, // 最小宽度
                },
                {
                    field: 'stationName',
                    title: '所属充电站',
                    minWidth: 150, // 最小宽度
                },
                {
                    field: 'operationMode',
                    title: '运营模式',
                    minWidth: 100, // 最小宽度
                    formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                          this.dict.type.ls_charging_operation_mode,
                          cellValue
                        );
                    },
                },
                {
                    field: 'pileNo',
                    title: '充电桩编号',
                    minWidth: 100, // 最小宽度
                },
                {
                    field: 'pileName',
                    title: '充电桩名称',
                    minWidth: 100, // 最小宽度
                },
                {
                  field: 'brandName',
                  title: '设备品牌',
                  minWidth: 120, // 最小宽度
                },
                {
                  field: 'modelName',
                  title: '设备型号',
                  minWidth: 100, // 最小宽度
                },
                {
                  field: 'subType',
                  title: '设备类型',
                  minWidth: 120, // 最小宽度
                  formatter: ({ cellValue }) => {
                          return this.selectDictLabel(
                            this.dict.type.ls_charging_subType,
                            cellValue
                          );
                        },
                },
                {
                  field: 'controlType',
                  title: '可控类型',
                  minWidth: 100, // 最小宽度
                  formatter: ({ cellValue }) => {
                          return this.selectDictLabel(
                            this.dict.type.ls_charging_adjustable_type,
                            cellValue
                          );
                        },
                },
                {
                  field: 'ratePower',
                  title: '额定功率(kW)',
                  minWidth: 130, // 最小宽度
                },
                {
                    title: '枪数量',
                    field: 'gunSum',
                    minWidth: 100, // 最小宽度
                },
                {
                    title: '充电堆编号',
                    field: 'stackNo',
                    minWidth: 100, // 最小宽度
                },
            ],
            tableData: [],
            pagerProps: {
                layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
            },
            params: {
              stationId: '',
              pileName: '',
              pileId: '',
              operationMode: '',
              accessMethod: '',
              pileSource: '',
              deviceBrand: '',
              deviceModel: '',
              deviceType:'',
              stackNumber: '',
            },

            stationNameList: [],
            stationLoading: false,

            brandList: [], //设备品牌
            modelList: [], //设备型号
            assetUnitList:  [], //资产单位
        };
    },
    computed: {
      filterOptions() {
        return {
          config: [
          {
            field: 'stationId',
            title: '所属充电站',
            element: 'el-select',
            props: {
              options: this.stationNameList,
              filterable: true,
              remote: true,
              remoteMethod: this.debouncedStationSearch,
              loading: this.stationLoading,
            },
          },
          {
            field: 'pileName',
            title: '充电桩名称',
            element: 'el-input',
            placeholder: '请输入',
          },
          {
            field: 'pileId',
            title: '充电桩编号',
            element: 'el-input',
            placeholder: '请输入',
          },
          {
            field: 'operationMode',
            title: '运营模式',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_operation_mode,
            },
          },
          {
            field: 'accessMethod',
            title: '接入方式',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_station_access_type,
            },
          },
          {
            field: 'pileSource',
            title: '桩来源',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_station_source,
            },
          },
          {
            field: 'deviceBrand',
            title: '设备品牌',
            element: 'el-select',
            props: {
              options: this.brandList,
            },
          },
          {
            field: 'deviceModel',
            title: '设备型号',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.modelList,
            },
          },
          {
            field: 'deviceType',
            title: '设备类型',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_subType,
            },
          },
          {
            field: 'stackNumber',
            title: '充电堆编号',
            element: 'el-input',
          },

          ],
          params: this.params,
        };
      },
      modalConfig() {
        return {
            addBtn: false,
            viewBtn: false,
            menu: false,
            editBtn: false,
            delBtn: false,
        }
      },
    },
    mounted() {
        this.loadData();

        this.getBrandList();
        this.getModelList();
        // this.getAssetUnit();
    },
    methods: {

        handleCancel() {
            this.dialogVisible = false;
        },

         // 获取产权单位
    async getAssetUnit() {
        const [err, res] = await getAssetUnit({})
        if (err) return
        this.assetUnitList = res.data
      },

           // 获取设备品牌
    async getBrandList() {
        const [err, res] = await getBrandList({})
        if (err) return

        const { data } = res
        const list = []
        data.forEach(item => {
          list.push({
            label: item.brandName,
            value: item.brandId,
          })
        })
        this.brandList = list
    },

    // 获取设备型号
    async getModelList() {
        const [err, res] = await getModelList({})
        if (err) return

        const { data } = res
        const list = []
        data.forEach(item => {
          list.push({
            label: item.modelName,
            value: item.modelId,
          })
        })
        this.modelList = list
    },

        async debouncedStationSearch(query) {
          console.log('11111');
          if (query !== '') {
              this.stationLoading = true;
              setTimeout(async() => {
                const [err, res] = await getStationList(
                  {
                    stationName: query,
                  }
                );

                if (err) return;
                this.stationLoading = false;
                this.stationNameList = res.data.map((item) => ({
                  label: item.stationName,
                  value: item.stationId,
                }));
              }, 200);
              
            } else {
              this.stationNameList = []
          }
        
        },


        // 新增按钮防抖        
        handleConfirm: _.debounce(function() {
            if(!this.stationList.length) {
                this.$message.warning('请先选择站点');
            } else {
                this.$emit('confirm', this.stationList);
                this.stationList = [];
                this.dialogVisible = false;
            }
        }, 300) ,

        async loadData() {
          const {
            stationId,
            pileName,
            pileId,
            operationMode,
            accessMethod,
            pileSource,
            deviceBrand,
            deviceModel,
            deviceType,
            stackNumber,
        } = this.filterOptions.params
                    
        const parmas = {
            stationId,
            pileName,
            pileNo:pileId,
            operationMode,
            stationAccessType: accessMethod,
            stationSource: pileSource,
            pileBrandId: deviceBrand,
            pileModelId: deviceModel,
            subType:deviceType,
            stackNo:stackNumber,
            operStatusList: this.operStatusList,
            applyType: '00',

            pageNum: this.tablePage.currentPage,
            pageSize: this.tablePage.pageSize,
        }

        this.loading = true;

        const [err, res] = await getPileList(parmas)

        this.loading = false;
        if (err) return 
        const { data, total } = res;

        const list = [];

        this.tableData = data;
        this.tablePage.total = total;
        
      },

        handleCheckboxChange({ records }) {
            console.log('选中的记录:', records);
            this.stationList = records
        },


    },
  };
  </script>
  <style lang="scss" scoped>
::v-deep .el-form-item__content{
    display: flex !important;
}



  </style>
  