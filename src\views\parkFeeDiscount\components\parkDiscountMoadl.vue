<template>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="630px"
      @close="handleCancel"
    >
        <el-form :model="form" :rules="rules" ref="ruleForm"  label-position="top">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item
                        label="场站名称："
                        prop="stationId"
                        :label-width="formLabelWidth"
                    >
                        <el-select
                            v-model="form.stationId"
                            placeholder="请选择"
                            style="width: 100%"
                            multiple
                            >
                            <el-option
                                v-for="item in stationIdList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="道闸厂商："
                        prop="manufacturer"
                        :label-width="formLabelWidth"
                    >
                        <el-select
                            v-model="form.manufacturer"
                            placeholder="请选择"
                            style="width: 100%"
                        >
                            <el-option
                                v-for="item in manufacturerList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="车场ID："
                        prop="parkId"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.parkId"
                            placeholder="请输入车场ID"
                        ></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="优惠模式："
                        prop="discountMode"
                        :label-width="formLabelWidth"
                    >
                        <el-radio-group v-model="form.discountMode">
                            <el-radio label="1">固定时长</el-radio>
                            <el-radio label="2">充电时长</el-radio>
                            
                        </el-radio-group>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="充电不低于："
                        prop="fixedChargeTime"
                        :label-width="formLabelWidth"
                        v-if="form.discountMode ==='1'"
                    >
                        <el-input
                            v-model="form.fixedChargeTime"
                            placeholder="请输入"
                        ></el-input> 
                        <div style="width: 40px; margin-left: 8px;">分钟</div>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="优惠固定时长："
                        prop="fixedDiscountTime"
                        :label-width="formLabelWidth"
                        v-if="form.discountMode ==='1'"
                    >
                        <el-input
                            v-model="form.fixedDiscountTime"
                            placeholder="请输入"
                        ></el-input> 
                        <div style="width: 40px; margin-left: 8px;">分钟</div>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="充电不低于："
                        prop="chargeChargeTime"
                        :label-width="formLabelWidth"
                        v-if="form.discountMode ==='2'"
                    >
                        <div style="display: flex; align-items: center;">
                            <el-input
                                v-model="form.chargeChargeTime"
                                placeholder="请输入"
                                style="width: 30%;"
                            ></el-input> 
                            <div style="margin-left: 8px;">分钟</div>
                            <div style="margin-left: 8px; margin-right: 4px;">停车优惠:充电时长+</div>
                            <el-input
                                v-model="form.chargeDiscountTime"
                                placeholder="请输入"
                                style="width: 30%;"
                            ></el-input> 
                            <div style="margin-left: 8px;">分钟</div>
                        </div>
                       
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="是否有上限："
                        prop="isLimit"
                        :label-width="formLabelWidth"
                        v-if="form.discountMode ==='2'"
                    >
                        <el-radio-group v-model="form.isLimit">
                            <el-radio label="1">无上限</el-radio>
                            <el-radio label="2">有上限</el-radio>
                            
                        </el-radio-group>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="优惠上线："
                        prop="discountLimit"
                        :label-width="formLabelWidth"
                        v-if="form.discountMode ==='2' && form.isLimit === '2'"
                    >
                        <el-input
                            v-model="form.discountLimit"
                            placeholder="请输入"
                        ></el-input> 
                        <div style="width: 40px; margin-left: 8px;">分钟</div>
                    </el-form-item>
                </el-col>

               
            </el-row>
           

        </el-form>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleConfirm">保存</el-button>
            <el-button type="primary" @click="handleConfirm">保存并启用</el-button>

        </div>
    </el-dialog>
  </template>
  <script>  

import {
    editPileControl
} from '@/api/pile/index';


import moment from 'moment';

import _ from 'lodash';

  export default {
    props: {
        dialogTitle:{
            type: String,
            default: '调控配置'
        }
    },
    components: {
  
    },
    dicts: [
        'ls_charging_status', // 是否状态
    ],
    data() {
        return {
            dialogVisible: false,
            pileId: '',
            form: {
                stationId: [],
                manufacturer: '',
                parkId: '',
                discountMode: '1',
                fixedChargeTime: '',
                fixedDiscountTime: '',
                chargeChargeTime: '',
                chargeDiscountTime: '',
                isLimit: '1',
                discountLimit: '',
            },
            rules: {
                stationId: [
                    { required: true, message: '请选择场站', trigger: 'change' },
                ],
                manufacturer: [
                    { required: true, message: '请选择道闸厂商', trigger: 'blur' },
                ],
                parkId: [
                    { required: true, message: '请输入车场ID', trigger: 'blur'}
                ],
                discountMode: [
                    { required: true, message: '请选择优惠模式', trigger: 'change'}
                ],
                fixedChargeTime: [
                    { required: true, message: '请输入充电不低于时长', trigger: 'blur'}
                ],
                fixedDiscountTime: [
                    { required: true, message: '请输入优惠固定时长', trigger: 'blur'}
                ],
                chargeChargeTime: [
                    { 
                        validator: (rule, value, callback) => {
                            if (!this.form.chargeChargeTime) {
                                callback(new Error('请输入充电时长'));
                            } else if ( !this.form.chargeDiscountTime){
                                callback(new Error('请输入优惠时长'));
                            }  else {
                                callback();
                            }
                        },
                        trigger: 'change'
                    }
                ],
                isLimit: [
                    { required: true, message: '请选择是否有上限', trigger: 'change'}
                ],
                discountLimit: [
                    { required: true, message: '请输入优惠上限', trigger: 'blur'}
                ]
                
            },
            formLabelWidth: '120px',

            stationIdList: [
                {
                    label: '场站1',
                    value: '1',
                },
                {
                    label: '场站2',
                    value: '2',
                },
                {
                    label: '场站3',
                    value: '3',
                }
            ],

            manufacturerList: [
                {
                    label: '厂商1',
                    value: '1',
                },
                {
                    label: '厂商2',
                    value: '2',
                },
                {
                    label: '厂商3',
                    value: '3',
                }
            ],


        };
    },
    computed: {},
    mounted() {},
    methods: {

        resetForm() {
            this.pileId = '';

            Object.keys(this.form).forEach((key) => {
                if(key === 'discountMode' || key ==='isLimit') {
                    this.form[key] = '1';
                } else if(key==='stationId') {
                    this.form[key] = [];
                } else  {
                    this.form[key] = '';
                }
                
            });
        },

        handleCancel() {
            this.$refs.ruleForm.resetFields();
            this.resetForm();
            console.log(this.form, 'this.form')
            this.dialogVisible = false;
        },



        // 新增按钮防抖        
        handleConfirm: _.debounce(function() {
            this.$refs.ruleForm.validate(async (valid) => {
                if (valid) {
                    console.log(this.form, 'this.form')
                    const {
                        controlType,
                        maxDownCapability,
                        maxUpwardCapability
                    } = this.form;
             
                         const params = {
                            pileId: this.pileId,
                            controlType,
                            maxDownCapability,
                            maxUpwardCapability
                        }

                        console.log('params', params)
                        const [err, res] = await editPileControl(params);
                        if (err) return;
                    
                        this.$message.success('编辑成功');
                        this.dialogVisible = false;
                        this.pileId  = '';
                        this.$emit('loadData');
                    
                }
            });
        }, 300) ,


    },
  };
  </script>
  <style lang="scss" scoped>
::v-deep .el-form-item__content{
    display: flex !important;
}

::v-deep .el-input-number {
    width: 90% !important;
}



.info-wrap {
    display: flex;
    height: 20px;
    margin-bottom: 24px;
    align-items: center;
    .info-title {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #505363;
      }
      .info-detail {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #292B33;
      }
}
  </style>
  