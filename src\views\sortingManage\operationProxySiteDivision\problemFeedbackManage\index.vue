<template>
  <div class="container container-float" style="padding: 0">
    <!-- 异常规则管理标签栏内容 -->

    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="manageLoading"
        :filterOptions="manageFilterOptions"
        :tablePage="manageTablePage"
        :tableColumn="manageTableColumn"
        :tableData="manageTableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        class="buse-wrap-station"
        @loadData="loadManageData"
      >
        <template slot="defaultHeader">
          <div>
            <div class="card-head">
              <div class="card-head-text">问题反馈管理列表</div>
            </div>
          </div>
        </template>
        <template slot="clearNo" slot-scope="{ row }">
          <el-button type="text">{{ row.clearNo || '' }}</el-button>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button @click="goFeedbackDetail(row)">详情</el-button>
            <el-button
              v-if="row.checkStatus === '0'"
              @click="feedbackCheck(row)"
            >
              核查
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
  </div>
</template>

<script>
import {
  clearProblemFeedbackPage,
  clearProblemFeedbackCheck,
} from '@/api/sortingManage/operationProxySiteDivision';
import { getStationList } from '@/api/pile/index';
import { getOperatorsList } from '@/api/operator/index';
import moment from 'moment';
export default {
  components: {},
  dicts: [
    'ls_clear_to_obj_type',
    'ls_clear_space_owner',
    'ls_clear_space_operator',
  ],
  data() {
    return {
      activeName: 'manage',
      manageLoading: false,
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        clearNo: '',
        stationId: '',
        toObjType: undefined,
        toObj: undefined,
        createBy: '',
        createDate: [],
        checkStatus: undefined,
      },
      manageTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      manageTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'feedbackNo',
          title: '问题反馈编号',
          minWidth: 190,
        },
        {
          field: 'clearNo',
          title: '分成编号',
          minWidth: 190,
          slots: { default: 'clearNo' },
        },
        {
          field: 'stationName',
          title: '充电站名称',
          minWidth: 190,
        },
        {
          field: 'problemOrderNum',
          title: '问题订单数',
          minWidth: 150,
        },

        {
          field: 'checkStatus',
          title: '状态',
          minWidth: 120,
          fixed: 'right',
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(this.checkStatusList, cellValue);
          },
        },
        {
          field: 'createBy',
          title: '反馈人',
          minWidth: 120,
        },

        {
          field: 'toObjName',
          title: '分成方',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'createTime',
          title: '创建时间',
          align: 'center',
          minWidth: 180,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 180,
          align: 'center',
          fixed: 'right',
        },
      ],
      manageTableData: [],
      checkStatusList: [
        { label: '未核查', value: '0' },
        { label: '已核查', value: '1' },
      ],
      orderRuleType: 'create',
      stationLoading: false,
      stationNameList: [],
      operationLoading: false,
      operationUnitList: [],
    };
  },

  computed: {
    manageFilterOptions() {
      return {
        config: [
          {
            field: 'clearNo',
            title: '分成编号',
            element: 'el-input',
          },
          {
            field: 'stationId',
            title: '充电站',
            element: 'el-select',
            props: {
              options: this.stationNameList,
              filterable: true,
              remote: true,
              remoteMethod: this.debouncedStationSearch,
              loading: this.stationLoading,
            },
          },
          {
            field: 'toObjType',
            title: '分成方类型',
            element: 'el-select',
            props: {
              placeholder: '请选择分成方类型',
              options: this.dict.type.ls_clear_to_obj_type,
            },
            on: {
              change: (val) => {
                this.params.toObj = undefined;
              },
            },
          },
          {
            field: 'toObj',
            title: '分成方',
            element: 'el-select',
            props: {
              placeholder: '请输入分成方',
              options: this.operationUnitList,
              filterable: true,
              remote: true,
              remoteMethod: this.debouncedOperationSearch,
              loading: this.operationLoading,
            },
            show: this.params.toObjType === '0',
          },
          {
            field: 'toObj',
            title: '分成方',
            element: 'el-select',
            props: {
              placeholder: '请选择分成方',
              options: this.dict.type.ls_clear_space_owner,
            },
            show: this.params.toObjType === '1',
          },
          {
            field: 'createBy',
            title: '反馈人',
            element: 'el-input',
          },
          {
            field: 'createDate',
            title: '创建时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              options: [],
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'checkStatus',
            title: '状态',
            element: 'el-select',
            props: {
              placeholder: '请选择状态',
              options: this.checkStatusList,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadManageData();
  },
  methods: {
    async debouncedStationSearch(query) {
      if (query !== '') {
        this.stationLoading = true;
        setTimeout(async () => {
          const [err, res] = await getStationList({
            stationName: query,
          });

          if (err) return;
          this.stationLoading = false;
          this.stationNameList = res.data.map((item) => ({
            label: item.stationName,
            value: item.stationId,
          }));
        }, 200);
      } else {
        this.stationNameList = [];
      }
    },
    async debouncedOperationSearch(query) {
      if (query !== '') {
        this.operationLoading = true;
        setTimeout(async () => {
          const [err, res] = await getOperatorsList({
            operatorName: query,
            pageNum: 1,
            pageSize: 50,
            linkBizTypeList: [],
            operatorCategoryList: [],
          });

          if (err) return;
          this.operationLoading = false;
          this.operationUnitList = res.data.map((item) => ({
            label: item.operatorName,
            value: item.operatorNo,
          }));
        }, 200);
      } else {
        this.operationUnitList = [];
      }
    },
    async feedbackCheck(row) {
      this.$confirm(
        `'确定核查问题反馈编号：${row?.feedbackNo || ''}吗？'`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        const [err, res] = await clearProblemFeedbackCheck({
          id: row?.proId || '',
        });

        if (err) return;

        this.$message({
          type: 'success',
          message: '核查成功!',
        });

        this.loadManageData();
      });
    },
    // 获取管理列表数据
    async loadManageData() {
      const params = { ...this.manageFilterOptions.params };

      if (params.createDate && params.createDate.length > 0) {
        params.begDate = moment(params.createDate[0]).format('YYYY-MM-DD');
        params.endDate = moment(params.createDate[1]).format('YYYY-MM-DD');
      }

      this.manageLoading = true;
      const [err, res] = await clearProblemFeedbackPage({
        ...params,
        pageNum: this.manageTablePage.currentPage,
        pageSize: this.manageTablePage.pageSize,
      });

      this.manageLoading = false;
      if (err) return;
      const { data, total } = res;
      this.manageTableData = data;
      this.manageTablePage.total = total;
    },

    // 编辑异常规则
    goFeedbackDetail(row) {
      this.$router.push({
        path: `/v2g-charging/sortingManage/operationProxySiteDivision/feedbackDetail?id=${
          row?.infoId || ''
        }&feedbackId=${row?.proId}&isChecked=${
          row?.checkStatus === '1' ? '1' : '0'
        }`,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-tabs {
  margin-top: 16px;
  background-color: #f5f6f9;
  .el-tabs__header {
    padding-left: 0;
    display: flex;
    justify-content: center;
    text-align: center;
    margin-bottom: 1px;
    .el-tabs__item {
      padding: 0;
      width: 164px;
      font-size: 18px;
      font-weight: 400;
      background-color: #fff;
    }
    .el-tabs__item.is-active {
      background-color: #1677fe;
      color: #fff;
    }
    .el-tabs__nav-scroll {
      border-radius: 25px;
      border: solid 1px #dfe1e5;
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__nav-wrap::after {
      width: 0;
    }
  }
}

.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }
  margin: 16px;

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
  }
}
::v-deep .menu-box .el-button {
  color: #217aff;
  border-color: #217aff;
}
</style>
