<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :modalConfig="modalConfig"
        @loadData="loadData"
        :tableOn="{
          'checkbox-change': handleCheckboxChange,
          'checkbox-all': handleCheckboxChange,
        }"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">清分明细报表</div>
            <div class="top-button-wrap">
              <el-button type="primary" @click="handleOutput">
                导出报表
              </el-button>
            </div>
          </div>
          <div class="card-head-after"></div>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button type="primary" plain @click="handleView(row)">
              清分明细
            </el-button>
            <el-button type="primary" plain @click="handleEdit(row)">
              修改
            </el-button>
            <el-button type="primary" plain @click="handleHistory(row)">
              修改记录
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
    <el-dialog
      :title="modifyData.title"
      :visible.sync="modifyData.open"
      width="50%"
      append-to-body
      @close="handleCancel"
    >
      <div>
        <el-form :model="modifyData.form" ref="modifyForm" label-position="top">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="清分周期" :label-width="formLabelWidth">
                2024.9.1-2024.9.30
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="交易单位" :label-width="formLabelWidth">
                省电动
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="充电站名称" :label-width="formLabelWidth">
                充电站1
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="原资产属性" :label-width="formLabelWidth">
                主业
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="修正资产属性" :label-width="formLabelWidth">
                <el-select
                  v-model="modifyData.form.assetProperty"
                  placeholder="请选择资产属性"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dict.type.ls_charging_asset_property"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  dicts: [
    'ls_charging_asset_property', // 资产属性
  ],
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [{}],
      tableColumn: [
        { type: 'seq', title: '序号', minWidth: 60 },
        { field: 'clearingCycle', title: '清分周期', minWidth: 150 },
        { field: 'stationName', title: '充电站名称', minWidth: 160 },
        { field: 'city', title: '地市', minWidth: 120 },
        { field: 'stationCode', title: '充电站编号', minWidth: 140 },
        { field: 'tradeUnit', title: '交易单位', minWidth: 150 },
        { field: 'tradeOrgCode', title: '交易单位组织机构编码', minWidth: 180 },
        { field: 'assetType', title: '资产属性', minWidth: 140 },
        { field: 'chargeEnergy', title: '充电电量（KWH）', minWidth: 180 },
        { field: 'electricityFee', title: '充电电费（元）', minWidth: 140 },
        { field: 'serviceFee', title: '充电服务费（元）', minWidth: 160 },
        { field: 'totalAmount', title: '充电总金额（元）', minWidth: 160 },
        { field: 'discountAmount', title: '优惠金额（元）', minWidth: 140 },
        {
          field: 'afterDiscountAmount',
          title: '优惠后交易金额（元）',
          minWidth: 200,
        },
        {
          field: 'headquartersDiscountFee',
          title: '总部营销优惠金额（元）',
          minWidth: 220,
        },
        {
          field: 'headquartersServiceDiscount',
          title: '总部营销优惠服务费（元）',
          minWidth: 220,
        },
        {
          field: 'receiptDiscountAmount',
          title: '收款单位营销优惠金额（元）',
          minWidth: 240,
        },
        {
          field: 'receiptElectricityDiscount',
          title: '收款单位营销优惠电费（元）',
          minWidth: 240,
        },
        {
          field: 'receiptServiceDiscount',
          title: '收款单位营销优惠服务费（元）',
          minWidth: 260,
        },
        { field: 'clearingEnergy', title: '清分电量（KWH）', minWidth: 160 },
        {
          field: 'clearingElectricityFee',
          title: '清分充电电费（元）',
          minWidth: 200,
        },
        {
          field: 'clearingServiceFee',
          title: '清分充电服务费（元）',
          minWidth: 180,
        },
        {
          field: 'clearingTotalAmount',
          title: '清分充电总金额（元）',
          minWidth: 180,
        },
        { field: 'operationMode', title: '运营模式', minWidth: 140 },
        {
          //   field: 'operation',
          slots: { default: 'operate' },
          title: '操作',
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],
      // 检索参数
      params: {
        chargingStation: '',
        clearingCycle: [],
        tradeUnit: '',
        assetType: '',
        city: '',
      },
      // 下拉选项数据
      chargingStationOptions: [
        { label: '充电站A', value: 'a' },
        { label: '充电站B', value: 'b' },
      ],
      tradeUnitOptions: [
        { label: '单位A', value: 'a' },
        { label: '单位B', value: 'b' },
      ],
      assetTypeOptions: [
        { label: '自建', value: 'self' },
        { label: '合资', value: 'joint' },
      ],
      cityOptions: [
        { label: '北京市', value: 'beijing' },
        { label: '上海市', value: 'shanghai' },
      ],
      modifyData: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: '修改',
        form: {
          assetProperty: '',
        },
      },
      formLabelWidth: '120px',
      selectList: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'chargingStation',
            title: '充电站',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.chargingStationOptions,
            },
          },
          {
            field: 'clearingCycle',
            title: '清分周期',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
          {
            field: 'tradeUnit',
            title: '交易单位',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.tradeUnitOptions,
            },
          },
          {
            field: 'assetType',
            title: '资产属性',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.assetTypeOptions,
            },
          },
          {
            field: 'city',
            title: '地市',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.cityOptions,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    loadData(page = this.tablePage) {
      //   console.log('加载数据', page);
      //   this.loading = true;
      //   setTimeout(() => {
      //     this.tableData = [];
      //     this.tablePage.total = 0;
      //     this.loading = false;
      //   }, 500);
    },
    // 清分明细
    handleView(row) {
      console.log('清分明细', row);
      this.$router.push({
        path: '/v2g-charging/sortingManage/stationSorting/sortingOrders',
        // query: {
        //   status,
        // },
      });
    },
    // 修改
    handleEdit(row) {
      console.log('修改', row);
      this.modifyData.open = true;
    },
    // 弹窗取消
    handleCancel() {
      this.modifyData.open = false;
    },
    // 弹窗提交
    submitFileForm() {
      console.log('this.modifyData', this.modifyData.form);
    },
    // 修改记录
    handleHistory(row) {
      console.log('导出', row);
      this.$router.push({
        path: '/v2g-charging/sortingManage/stationSorting/sortingSummary/detailHistory',
        // query: {
        //   status,
        // },
      });
    },
    // 勾选行
    handleCheckboxChange({ records }) {
      this.selectList = records;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 20px;
}

.table-wrap {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px;
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1);
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
}
</style>
