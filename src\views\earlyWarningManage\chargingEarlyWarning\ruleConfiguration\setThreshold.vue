<template>
  <div class="container">
    <div class="table-wrap">
      <div class="table-wrap">
        <BuseCrud
          ref="crud"
          :loading="loading"
          :filterOptions="filterOptions"
          :tablePage="tablePage"
          :tableColumn="tableColumn"
          :tableData="tableData"
          :pagerProps="pagerProps"
          :modalConfig="modalConfig"
          :tableOn="{
            'checkbox-change': handleCheckboxChange,
            'checkbox-all': handleCheckboxChange,
          }"
          @loadData="loadData"
        >
          <template slot="defaultHeader">
            <div class="card-head">
              <div class="card-head-text">计费预警阈值设置</div>
              <div class="top-button-wrap">
                <el-button type="primary" @click="handleAdd">
                  新增阈值设置
                </el-button>
                <el-button type="primary" @click="handleEdit">
                  批量修改阈值
                </el-button>
              </div>
            </div>
            <div class="card-head-after"></div>
          </template>
          <template #serviceTopThreshold="{ row, column }">
            <el-input
              v-model="row[column.property]"
              size="small"
              @change="handleCellValueChange(row, 'serviceTopThreshold')"
              type="number"
              :min="0"
            ></el-input>
          </template>
          <template #servicePeakThreshold="{ row, column }">
            <el-input
              v-model="row[column.property]"
              size="small"
              @change="handleCellValueChange(row, 'servicePeakThreshold')"
              type="number"
              :min="0"
            ></el-input>
          </template>
          <template #serviceFlatThreshold="{ row, column }">
            <el-input
              v-model="row[column.property]"
              size="small"
              @change="handleCellValueChange(row, 'serviceFlatThreshold')"
              type="number"
              :min="0"
            ></el-input>
          </template>
          <template #serviceValleyThreshold="{ row, column }">
            <el-input
              v-model="row[column.property]"
              size="small"
              @change="handleCellValueChange(row, 'serviceValleyThreshold')"
              type="number"
              :min="0"
            ></el-input>
          </template>
        </BuseCrud>
      </div>
    </div>
    <EditModal
      ref="editModal"
      @closeModal="closeEditModal"
      @okModal="editModalOk"
    />
  </div>
</template>

<script>
import EditModal from './components/editModal';
import {
  getPriceThresholdPage,
  areaList,
  batchUpdatePriceThreshold,
} from '@/api/earlyWarningManage/ruleConfiguration';
import { getStationList } from '@/api/stationSOC/index.js';

export default {
  dicts: [
    'ls_charging_station_type', // 站点类型
    'ls_charging_asset_property', // 资产属性
  ],
  components: {
    EditModal,
  },
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableColumn: [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
        },
        {
          type: 'seq',
          title: '序号',
          minWidth: 60,
        },
        {
          field: 'stationName',
          title: '充电站名称',
          minWidth: 200,
        },
        {
          field: 'stationNo',
          title: '充电站编号',
          minWidth: 200,
        },
        {
          field: 'city',
          title: '地市',
          minWidth: 200,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(this.cityOptions, cellValue);
          },
        },
        {
          field: 'assetProperty',
          title: '资产属性',
          minWidth: 200,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_asset_property,
              cellValue
            );
          },
        },
        {
          field: 'stationType',
          title: '站点类型',
          minWidth: 200,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_station_type,
              cellValue
            );
          },
        },
        {
          field: 'serviceTopThreshold',
          title: '尖服务费单价阈值',
          minWidth: 200,
          slots: {
            default: 'serviceTopThreshold',
          },
        },
        {
          field: 'servicePeakThreshold',
          title: '峰服务费单价阈值',
          minWidth: 200,
          slots: {
            default: 'servicePeakThreshold',
          },
        },
        {
          field: 'serviceFlatThreshold',
          title: '平服务费单价阈值',
          minWidth: 200,
          slots: {
            default: 'serviceFlatThreshold', // 自定义单元格插槽
          },
        },
        {
          field: 'serviceValleyThreshold',
          title: '谷服务费单价阈值',
          minWidth: 200,
          slots: {
            default: 'serviceValleyThreshold',
          },
        },
        {
          field: 'updatedBy',
          title: '更新人',
          minWidth: 200,
        },
        {
          field: 'updatedTime',
          title: '更新时间',
          minWidth: 200,
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        stationNo: '',
        stationId: '',
        assetProperty: '',
        city: '',
        stationType: '',
      },
      batchList: [],
      stationList: [],
      stationLoading: false,
      cityOptions: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'stationNo',
            title: '充电站编号',
          },
          {
            field: 'stationId',
            title: '充电站',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.stationList,
              filterable: true,
              remote: true,
              remoteMethod: this.debouncedStationSearch,
              loading: this.stationLoading,
            },
          },
          {
            field: 'assetProperty',
            title: '资产属性',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_asset_property,
            },
          },
          {
            field: 'city',
            title: '地市',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.cityOptions,
            },
          },
          {
            field: 'stationType',
            title: '站点类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_station_type,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  async mounted() {
    await this.loadData();
    await this.getCitys();
  },
  methods: {
    async loadData() {
      this.loading = true;
      let params = {
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        ...this.params,
      };
      const [err, res] = await getPriceThresholdPage(params);
      this.loading = false;
      if (err) {
        return;
      }
      console.log('格阈值分页列表', res);
      const { data, total } = res;
      this.tableData = data;
      this.tablePage.total = total;
    },
    // 获取场站
    async debouncedStationSearch(query) {
      // console.log(query, 'query');
      if (query !== '') {
        this.stationLoading = true;
        setTimeout(async () => {
          const [err, res] = await getStationList({
            stationName: query,
          });

          if (err) return;
          this.stationLoading = false;
          this.stationList = res.data.map((item) => ({
            label: item.stationName,
            value: item.stationId,
          }));
        }, 200);
      } else {
        this.stationList = [];
      }
    },
    // 获取地市
    async getCitys() {
      let params = {
        areaLevel: '03',
      };
      const [err, res] = await areaList(params);
      if (err) return;
      // console.log('获取地市', res.data);
      this.cityOptions = res.data.map((item) => ({
        label: item.areaName,
        value: item.areaCode,
      }));
    },
    // 勾选
    handleCheckboxChange({ records }) {
      // console.log('records', records);
      this.batchList = records;
    },
    // 处理单元格值变化
    async handleCellValueChange(row, str) {
      console.log('单元格值变化:', row);
      console.log(str);
      if (row[str] < 0) {
        row[str] = 0;
      }
      // 保存
      let params = {
        ids: [row.id],
        serviceTopThreshold: row.serviceTopThreshold,
        servicePeakThreshold: row.servicePeakThreshold,
        serviceFlatThreshold: row.serviceFlatThreshold,
        serviceValleyThreshold: row.serviceValleyThreshold,
      };
      const [err, res] = await batchUpdatePriceThreshold(params);
      if (err) return;
      this.$message.success('编辑成功');
    },
    // 新增
    handleAdd() {
      this.$router.push({
        path: '/v2g-charging/earlyWarningManage/chargingEarlyWarning/ruleConfiguration/addThreshold',
      });
    },
    // 编辑
    handleEdit() {
      console.log('this.batchList', this.batchList);
      if (this.batchList.length > 0) {
        this.$refs.editModal.stationIds = this.batchList;
        this.$refs.editModal.editForm = {
          serviceTopThreshold: '',
          servicePeakThreshold: '',
          serviceFlatThreshold: '',
          serviceValleyThreshold: '',
        };
        this.$refs.editModal.dialogVisible = true;
      } else {
        this.$message.error(`请勾选需要修改的数据!`);
      }
    },
    // 关闭编辑弹窗
    closeEditModal() {
      this.$refs.editModal.dialogVisible = false;
    },
    // 编辑接口确定
    editModalOk() {
      this.$refs.editModal.dialogVisible = false;
      this.loadData();
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  background: #fff;
  padding: 20px;
  border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
}
</style>
